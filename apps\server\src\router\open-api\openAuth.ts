import { verifySecret } from '@/lib/utils/crypto'
import { openAPIProcedure } from '@/procedure'
import { publicApiProcedure, router } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { sessionService } from '@/lib/services/session'
import { tokenService } from '@/lib/services/session'
import {
  AppInfoResponseSchema,
  AuthenticateResponseSchema,
  AuthenticateSchema,
  TokenVerifyResponseSchema,
} from '@coozf/zod'

export const openAuthRouter = router({
  // 使用 appId 和 secret 获取 token
  authenticate: publicApiProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/auth/authenticate',
        tags: ['auth'],
        summary: '获取访问令牌',
        description: '使用应用ID和密钥获取访问令牌，用于后续OpenAPI调用的身份验证。令牌有效期为30天。',
      },
    })
    .input(AuthenticateSchema)
    .output(AuthenticateResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const { appId, secret } = input
      const application = await ctx.db.application.findUnique({
        where: {
          appId,
        },
      })

      if (!application) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '无效的 App ID',
        })
      }

      const isSecretValid = await verifySecret(secret, application.secret)
      if (!isSecretValid) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '无效的 Secret',
        })
      }

      // 生成 token
      const token = await tokenService.generateToken(application.id)

      return {
        access_token: token,
        token_type: 'Bearer',
        expires_in: 30 * 24 * 60 * 60, // 30天，单位秒
        application: {
          id: application.id,
          name: application.name,
          appId: application.appId,
        },
      }
    }),

  // 验证 token 是否有效（需要使用 openAPIProcedure）
  verify: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/auth/verify',
        tags: ['auth'],
        summary: '验证访问令牌',
        description: '验证当前访问令牌是否有效，并返回关联的用户和应用信息。',
      },
    })
    .input(z.void().describe('无需输入参数'))
    .output(TokenVerifyResponseSchema)
    .query(async ({ ctx }) => {
      return {
        valid: true,
        user: {
          id: ctx.user.id,
          name: ctx.user.name || '',
          phone: ctx.user.phoneNumber || undefined,
        },
        application: {
          id: ctx.application.id,
          name: ctx.application.name,
          appId: ctx.application.appId,
        },
      }
    }),

  // 获取应用信息（需要使用 openAPIProcedure）
  getAppInfo: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/auth/app-info',
        tags: ['auth'],
        summary: '获取应用信息',
        description: '获取当前访问令牌关联的应用详细信息，包括应用配置、流量使用情况等。',
      },
    })
    .input(z.void().describe('无需输入参数'))
    .output(AppInfoResponseSchema)
    .query(async ({ ctx }) => {
      return {
        application: {
          id: ctx.application.id,
          name: ctx.application.name,
          description: ctx.application.description || undefined,
          appId: ctx.application.appId,
        },
        user: {
          id: ctx.user.id,
          name: ctx.user.name || '',
        },
      }
    }),

  // // 获取session_token
  // getSessionToken: openAPIProcedure
  //   .meta({
  //     openapi: {
  //       method: 'GET',
  //       path: '/auth/session-token',
  //       tags: ['auth'],
  //       summary: '获取session_token',
  //       description: '获取当前访问令牌关联的session_token。用户需要将session_token返回给前端，用于后续的API调用。',
  //     },
  //   })
  //   .input(z.void().describe('无需输入参数'))
  //   .output(
  //     z.object({
  //       session_token: z.string().describe('session_token'),
  //     }),
  //   )
  //   .query(async ({ ctx }) => {
  //     const sessionToken = await sessionService.generateSessionToken(ctx.user.id, ctx.application.id)
  //     return {
  //       session_token: sessionToken,
  //     }
  //   }),
})
