import { openAuthRouter } from '@/router/open-api/openAuth'
import { oauth2Router } from './oauth2'
import { router, t } from '@/trpc'
import { openMediaAccountRouter } from './media-account'
import { openXiaohongshuRouter } from './xiaohongshu'
import { openDouyinRouter } from './douyin'
import { versonRouter } from './verson'
import { deviceRouter } from './device'

export const openApiRouter = router({
  openAuth: openAuthRouter,
  mediaAccount: openMediaAccountRouter,
  xiaohongshu: openXiaohongshuRouter,
  douyin: openDouyinRouter,
  verson: versonRouter,
  oauth2: oauth2Router,
  device: deviceRouter,
})
// export type definition of API
export type AppRouter = typeof openApiRouter

export const createCaller = t.createCallerFactory(openApiRouter)
