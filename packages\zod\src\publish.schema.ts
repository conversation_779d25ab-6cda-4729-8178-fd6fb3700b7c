import z from "zod";
import { channelSchema } from "./common";

export const clientIdSchema = z.object({
  clientId: z.string().optional().nullish()
})
// /platform-publish/tasks
export const CreatePublishTaskSchema = z.object({
  publishType: z.string(),
  tasks: z.array(z.any()),
  publishChannel: channelSchema.optional(),
}).merge(clientIdSchema)



// 视频 tasks
export const CreateVideoTaskSchema = z.object({
  taskId: z.string(),
  publishData: z.object({
    video: z.object({
        url: z.string().url(),
    }),
    covers: z.array(z.object({
        pathOrUrl: z.string().url(),
    }))
  }),
  authorInfo: z.object({
    id: z.string(),
  }),
  title: z.string().optional(),
  desc: z.string().optional()
})

export const CrawlerWebhookSchema = z.object({
  event: z.string(),
})

export const PublishTaskCallbackSchema = z.object({
  taskId: z.string(),
  stages: z.string(),
  errorMessage: z.string().optional(),
  stageStatus: z.string()
}).merge(CrawlerWebhookSchema)

// 授权回调
export const AuthResultCallbackSchema = z.object({
  platformCode: z.string().describe('平台代码'),
  userId: z.string().describe('平台用户ID'),
  userName: z.string().describe('平台用户名'),
  avatar: z.string().optional().describe('平台用户头像'),
  cookie: z.string().describe('平台Cookie'),
  verify: z.boolean().optional().describe('是否实名认证'),
  // 授权localstorage
  localStorage: z.string().optional().describe('本地存储数据'),
  status: z.enum(['SUCCESS', 'FAILED']).describe('授权状态'),
  errorMessage: z.string().optional().describe('错误信息'),
}).merge(CrawlerWebhookSchema)

// 获取发布记录详情的输入schema
export const GetPublishRecordDetailSchema = z.object({
  publishId: z.string()
})

// 开放平台授权
export const OpenPlatformAuthSchema = z.object({
  platformCode: z.string().describe('平台代码'),
  platformUserId: z.string().describe('平台用户ID'),
  platformUserName: z.string().describe('平台用户名'),
  avatar: z.string().optional().describe('平台用户头像'),
  platformTokenExpiresAt: z.date().describe('平台Token过期时间'),
  platformCookieHash: z.string().describe('平台Cookie哈希'),
  platformRefreshTokenExpiresAt: z.date().describe('平台RefreshToken过期时间'),
  parentPlatformUserId: z.string().optional().describe('父平台用户ID'),
})


export type CreatePublishTaskInput = z.infer<typeof CreatePublishTaskSchema>
export type CreateVideoTaskInput = z.infer<typeof CreateVideoTaskSchema>
export type AuthResultCallbackInput = z.infer<typeof AuthResultCallbackSchema>
export type OpenPlatformAuthInput = z.infer<typeof OpenPlatformAuthSchema>