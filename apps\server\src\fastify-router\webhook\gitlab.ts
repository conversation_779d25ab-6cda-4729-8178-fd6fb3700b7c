import { env } from '@/env'
import { logger } from '@/lib'
import { ClientBuildStatus, db } from '@coozf/db'
import { gitlabWebhookSchema, pipelienWebhookSchema } from '@coozf/zod'
import { FastifyInstance } from 'fastify'
import { VersionService } from '@/lib/business/version'

export async function gitlabWebhookRoutes(app: FastifyInstance) {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers['x-gitlab-token']
    if (authHeader !== env.GIT_WEBHOOK_SECRET) {
      _reply.status(403).send('Forbidden')
    }
  })
  app.post('/', async (request, reply) => {
    const body = gitlabWebhookSchema.parse(request.body)
    if (body.object_kind === 'pipeline') {
      const pipelien = pipelienWebhookSchema.parse(request.body)

      // 查找pipeline记录
      if (pipelien.object_attributes.source === 'trigger') {
        // 日志记录
        logger.info(
          `[GitLab Webhook] Pipeline event received: ${pipelien.object_attributes.id} - ${pipelien.object_attributes.status}`,
          {
            type: 'gitlab-webhook',
            ...pipelien,
          },
        )
        const version = await db.clientVersion.findFirst({
          where: { gitlabPipelineId: pipelien.object_attributes.id },
          include: {
            baseVersion: {
              select: {
                version: true,
              },
            },
            application: {
              select: {
                brandName: true,
              },
            },
          },
        })
        if (version && version.buildStatus !== ClientBuildStatus.SUCCESS) {
          let finalBuildStatus = convertGitLabStatusToClientStatus(
            pipelien.object_attributes.status as ********************,
          )
          let errorMessage: string | null = null

          // 如果 GitLab 状态为成功，检查 OSS 文件是否存在
          if (pipelien.object_attributes.status === 'success') {
            try {
              const url = new URL(version.downloadUrl!)
              // 去除第一个斜杠
              const path = url.pathname.substring(1)
              await VersionService.checkOssResourceExists(path, version.platform)
              logger.info(
                `[GitLab Webhook] OSS files verified successfully for version ${version.baseVersion.version}, platform ${version.platform}`,
                {
                  type: 'gitlab-webhook-oss-check',
                  versionId: version.id,
                  version: version.baseVersion.version,
                  platform: version.platform,
                },
              )
            } catch (error) {
              // OSS 文件不存在，将状态设为失败
              finalBuildStatus = ClientBuildStatus.FAILED
              errorMessage = `OSS文件检测失败: ${error instanceof Error ? error.message : error}`
              logger.error(
                `[GitLab Webhook] OSS files verification failed for version ${version.baseVersion.version}, platform ${version.platform}`,
                {
                  type: 'gitlab-webhook-oss-check-error',
                  versionId: version.id,
                  version: version.baseVersion.version,
                  platform: version.platform,
                  error: errorMessage,
                },
              )
            }
          }

          await db.clientVersion.update({
            where: { id: version.id },
            data: {
              buildStartedAt: pipelien.object_attributes.created_at
                ? new Date(pipelien.object_attributes.created_at)
                : null,
              buildCompletedAt: pipelien.object_attributes.finished_at
                ? new Date(pipelien.object_attributes.finished_at)
                : null,
              buildStatus: finalBuildStatus,
              errorMessage: errorMessage || version.errorMessage,
            },
          })
        }
      }
    }
    return reply.send({ success: true })
  })
}

type ******************** =
  | 'created'
  | 'waiting_for_resource'
  | 'preparing'
  | 'pending'
  | 'running'
  | 'success'
  | 'failed'
  | 'canceled'
  | 'skipped'
  | 'manual'

// 转换状态
function convertGitLabStatusToClientStatus(gitlabStatus: ********************): ClientBuildStatus {
  switch (gitlabStatus) {
    case 'created':
    case 'waiting_for_resource':
    case 'preparing':
    case 'pending':
    case 'manual':
      return ClientBuildStatus.PENDING

    case 'running':
      return ClientBuildStatus.BUILDING

    case 'success':
      return ClientBuildStatus.SUCCESS

    case 'failed':
    case 'skipped':
      return ClientBuildStatus.FAILED

    case 'canceled':
      return ClientBuildStatus.CANCELLED

    default:
      // 兜底处理，对于未知状态默认返回PENDING
      return ClientBuildStatus.PENDING
  }
}
