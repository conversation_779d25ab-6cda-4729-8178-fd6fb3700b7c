import { env } from '@/env'
import { storageService } from '@coozf/huoshan'

export class AdminOssService {
  private deskTopBucketName = env.OSS_DEFAULT_BUCKET

  async getListObjects(name: string, bucket?: string): Promise<any> {
    try {
      const getListObjects = await storageService.getFileList(name, bucket || this.deskTopBucketName)
      return getListObjects
    } catch (error) {
      throw new Error(`获取文件列表失败: ${error}`)
    }
  }

  async getUploadSignatureUrl(key: string) {
    const { uploadUrl: signatureUrl, accessUrl } = await storageService.getUploadUrl(
      key,
      undefined,
      this.deskTopBucketName,
    )
    return {
      serviceUrl: signatureUrl,
      key: key,
      url: accessUrl,
    }
  }

  /**
   * 上传文件到oss
   * @param buffer
   * @param name
   * @returns
   */
  async uploadFile(buffer: Buffer, name: string) {
    try {
      await storageService.uploadFile(buffer, name)
      return name
    } catch (error) {
      throw new Error(`上传文件失败: ${error}`)
    }
  }

  /**
   * 获取文件内容
   * @param key
   */
  async getFileContent(key: string) {
    try {
      return await storageService.getFileContent(key, this.deskTopBucketName)
    } catch (error) {
      throw new Error(`获取文件内容失败: ${error}`)
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await storageService.deleteFile(key)
    } catch (error) {
      throw new Error(`删除文件失败: ${error}`)
    }
  }
}

export const adminOssService = new AdminOssService()
