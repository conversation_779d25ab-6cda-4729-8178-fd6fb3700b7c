import { FastifyInstance } from 'fastify'
import { deviceManager } from '../device-manager'
import { ApiError } from '../utils'
import { logger } from '../services/logger'
import { locationRequestManager } from '../services/socket-response-manager'
import { Application } from '@prisma/client'
import { sessionService } from '../services/session'
import axios from 'axios'
import { config } from '@/env'

// socket通用检查
const socketCommonCheck = async (clientId: string) => {
  // 检查设备是否在线
  const isOnline = await deviceManager.isOnline(clientId)
  if (!isOnline) {
    throw new ApiError(40000, `设备离线，无法进行操作, ${clientId}`)
  }

  // 获取设备信息
  const socketId = await deviceManager.getSocketId(clientId)
  if (!socketId) {
    throw new ApiError(40000, `设备信息不存在 ${clientId}`)
  }

  return socketId
}

// 本地发布的处理
export const localPublishHandler = async (
  clientId: string,
  sessionToken: string,
  data: unknown,
  fastify: FastifyInstance,
) => {
  try {
    // 检查设备是否在线
    const socketId = await socketCommonCheck(clientId)

    // 准备发送给设备的数据
    const deviceMessage = {
      type: 'task',
      sessionToken,
      data: data,
      timestamp: new Date().toISOString(),
    }

    logger.info('开始推送任务到设备', {
      type: 'publish',
      event: 'task_push',
      clientId,
      socketId,
    })

    fastify.io.to(socketId).emit('task:push', deviceMessage)
  } catch (error) {
    logger.error('本地发布异常', {
      type: 'publish',
      event: 'local_publish_error',
      clientId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}

// 本地地理位置查询
export const localLocationHandler = async (clientId: string, data: unknown, fastify: FastifyInstance) => {
  try {
    // 检查设备是否在线
    const socketId = await socketCommonCheck(clientId)

    // 生成请求ID（包含实例信息）
    const requestId = locationRequestManager.generateRequestId(clientId)
    // 准备发送给设备的数据
    const deviceMessage = {
      type: 'location',
      data: data,
      timestamp: new Date().toISOString(),
      requestId, // 添加请求ID
    }
    logger.info('开始推送定位任务到设备', {
      type: 'location',
      event: 'location_push',
      clientId,
      socketId,
      requestId,
    })

    // 先设置响应监听，再发送请求
    const responsePromise = locationRequestManager.waitForResponse(
      requestId,
      clientId,
      30000, // 30秒超时
    )
    // 发送消息到设备（不使用 ack）
    fastify.io.to(socketId).emit('location', deviceMessage)

    // 等待响应
    const response = await responsePromise

    logger.info('定位任务完成', {
      type: 'location',
      event: 'location_complete',
      clientId,
      socketId,
      requestId,
    })

    return response
  } catch (error) {
    logger.error('本地定位异常', {
      type: 'location',
      event: 'local_location_error',
      clientId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}

// 本地授权sendSocketAuthCommand
export const localAuthHandler = async (
  clientId: string,
  platformCode: string,
  proxy: unknown | undefined,
  callbackId: string,
  app: Application,
  fastify: FastifyInstance,
  extra: unknown,
) => {
  try {
    // 检查设备是否在线
    const socketId = await socketCommonCheck(clientId)

    // 生成 sessionToken
    const sessionToken = sessionService.generateSessionToken(app.userId, app.id, callbackId, extra)

    // 准备发送给设备的数据
    const deviceMessage = {
      type: 'auth',
      sessionToken,
      data: {
        platformCode,
        proxy,
      },
      timestamp: new Date().toISOString(),
    }

    logger.info('开始推送授权任务到设备', {
      type: 'auth',
      event: 'auth_push',
      clientId,
      socketId,
    })

    fastify.io.to(socketId).emit('auth', deviceMessage)
  } catch (error) {
    logger.error('本地授权异常', {
      type: 'auth',
      event: 'local_auth_error',
      clientId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}

// 远程授权
export const remoteAuthHandler = async (
  platformCode: string,
  proxy: unknown | undefined,
  callbackId: string,
  app: Application,
  extra: unknown,
) => {
  try {
    // 生成 sessionToken
    const sessionToken = await sessionService.generateSessionToken(app.userId, app.id, callbackId, extra)

    logger.info('开始进行云授权', {
      type: 'auth',
      event: 'remote_auth',
      url: config.CLOUD_AUTH_URL,
      clientId: app.userId,
      platformCode,
      proxy,
    })
    const res = await axios.post<{ id: string; processedData: string }>(
      config.CLOUD_AUTH_URL,
      {
        platformCode,
        proxy,
      },
      {
        headers: {
          Authorization: sessionToken,
        },
        timeout: 30000, // 30秒超时
      },
    )
    return res
  } catch (error) {
    logger.error('云授权异常', {
      type: 'auth',
      event: 'remote_auth_error',
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}
