import { createFileRoute } from '@tanstack/react-router'
import { useState, useMemo } from 'react'
import { cn } from '@coozf/ui/lib/utils'
import { LoadingContainer } from '@coozf/ui/components/loading'
import { OverviewTab } from '@/components/app-detail/tabs/overview-tab'
import { SettingsTab } from '@/components/app-detail/tabs/settings-tab'
import { ClientManagementTab } from '@/components/app-detail/tabs/client-management-tab'
import { useApplicationDetails } from '@/components/app-detail/hooks/useApplicationDetails'
import { MENU_ITEMS } from '@/components/app-detail/constants'

export const Route = createFileRoute('/_authenticated/apps/$id/')({
  component: ApplicationDetailPage,
})

function ApplicationDetailPage() {
  const { id } = Route.useParams()
  const [activeTab, setActiveTab] = useState('overview')

  const { application, isLoading } = useApplicationDetails(id)

  // 获取应用的OEM配置状态
  const oemEnabled = application?.oemEnabled || false

  // 根据OEM状态动态过滤菜单项
  const availableMenuItems = useMemo(() => {
    return MENU_ITEMS.map((item) => {
      // 如果OEM功能未启用，则隐藏client-management菜单项
      if (item.id === 'client-management' && !oemEnabled) {
        return { ...item, label: '客户端' }
      }
      return item
    })
  }, [oemEnabled])

  if (isLoading) {
    return <LoadingContainer />
  }

  if (!application) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">应用不存在</h1>
        <p className="text-gray-600 mt-2">请检查应用ID是否正确</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      {/* 主要内容区域 */}
      <div className="flex gap-6">
        {/* 左侧菜单栏 */}
        <div className="w-64 flex-shrink-0">
          <div className="bg-card border-r border-border h-full">
            <nav className="space-y-1 p-4">
              {availableMenuItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={cn(
                      'w-full flex items-center gap-3 px-4 py-3 text-left text-sm font-medium transition-colors rounded-md',
                      activeTab === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted',
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    {item.label}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1">
          {activeTab === 'overview' && <OverviewTab applicationId={id} application={application} />}

          {activeTab === 'settings' && <SettingsTab applicationId={id} application={application} />}

          {activeTab === 'client-management' && <ClientManagementTab applicationId={id} application={application} />}
        </div>
      </div>
    </div>
  )
}
