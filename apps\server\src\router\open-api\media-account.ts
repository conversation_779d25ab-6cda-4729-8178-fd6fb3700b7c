import { z } from 'zod'
import { router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import {
  AccountListQuerySchema,
  GetAccountByIdSchema,
  MediaAccountResponseSchema,
  createPaginationResponseSchema,
  InitiateAuthorizationSchema,
  InitiateAuthorizationResponseSchema,
  BindAccountSchema,
  BindAccountResponseSchema,
  UpdateAccountSchema,
  MigrateAccountSchema,
  UpdateAuthorizationSchema,
} from '@coozf/zod'
import { TRPCError } from '@trpc/server'
import { MediaAccountServies } from '@/lib'
import { systemConfigCache } from '@/lib/services/cache'

// export const requestHeader = z.object({
//   'x-secret-key': z.string().describe('加密密钥，用于加密Cookie'),
// })

export const openMediaAccountRouter = router({
  // POST /media-accounts - 创建(绑定)媒体账号
  create: openAPIProcedure
    .input(BindAccountSchema)
    .meta({
      openapi: {
        method: 'POST',
        path: '/media-accounts',
        protect: true,
        tags: ['媒体账号'],
        summary: '绑定媒体账号',
        description: '将指定平台的账号绑定到当前应用。用户传入原始Cookie，后端自动加密处理。',
      },
    })
    .output(BindAccountResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        platformCode,
        platformUserId: userId,
        platformUserName: userName,
        platformAvatar: avatar,
        platformCookie: cookie,
      } = input

      const authAccount = await MediaAccountServies.createMediaAccount(
        {
          platformCode,
          userId,
          userName,
          avatar,
          cookie,
        },
        ctx.application,
      )

      return {
        id: authAccount.id,
      }
    }),
  // put /media-accounts/{id} - 更新媒体账号
  upadate: openAPIProcedure
    .meta({
      openapi: {
        method: 'PUT',
        path: '/media-accounts/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '更新媒体账号',
        description: '将指定平台的账号绑定到当前应用。用户传入原始Cookie，后端自动进行安全哈希处理。',
      },
    })
    .input(UpdateAccountSchema)
    .output(BindAccountResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const { platformCode, platformUserId, platformUserName, platformAvatar, platformCookie } = input

      const authAccount = await MediaAccountServies.updateMediaAccount({
        id: input.id,
        applicationId: ctx.application.id,
        platformCode,
        platformUserId,
        platformUserName,
        platformAvatar,
        platformCookie,
      })

      return {
        id: authAccount.id,
      }
    }),

  // GET /media-accounts - 获取媒体账号列表
  list: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/media-accounts',
        protect: true,
        tags: ['媒体账号'],
        summary: '获取媒体账号列表',
        description: '获取当前应用下绑定的所有媒体账号列表，支持分页和筛选。',
      },
    })
    .input(AccountListQuerySchema)
    .output(createPaginationResponseSchema(MediaAccountResponseSchema))
    .query(async ({ ctx, input }) => {
      return MediaAccountServies.getMediaAccountOrders({
        ...input,
        applicationId: ctx.application.id,
      })
    }),

  // GET /media-accounts/{id} - 获取单个媒体账号详情
  getById: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/media-accounts/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '获取媒体账号详情',
        description: '根据ID获取指定媒体账号的详细信息。',
      },
    })
    .input(GetAccountByIdSchema)
    .output(MediaAccountResponseSchema)
    .query(async ({ ctx, input }) => {
      const authAccount = await ctx.db.authAccount.findFirst({
        where: {
          id: input.id,
          applicationId: ctx.application.id,
        },
      })

      if (!authAccount) {
        throw new Error('媒体账号不存在或无权限访问')
      }

      return {
        id: authAccount.id,
        applicationId: authAccount.applicationId,
        platformCode: authAccount.platformCode,
        platformUserId: authAccount.platformUserId,
        platformUserName: authAccount.platformUserName,
        platformAvatar: authAccount.platformAvatar,
        createdAt: authAccount.createdAt,
        updatedAt: authAccount.updatedAt,
      }
    }),

  // DELETE /media-accounts/{id} - 删除媒体账号
  delete: openAPIProcedure
    .meta({
      openapi: {
        method: 'DELETE',
        path: '/media-accounts/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '删除媒体账号',
        description: '删除指定的媒体账号绑定，解除与当前应用的关联。',
      },
    })
    .input(GetAccountByIdSchema)
    .output(z.object({}))
    .mutation(async ({ ctx, input }) => {
      return MediaAccountServies.deleteMediaAccount(input.id, ctx.application.id)
    }),

  // post /media-account/migration - 开放平台账号迁移
  migration: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/media-account/migration',
        protect: true,
        tags: ['媒体账号'],
        summary: '媒体账号迁移',
        description: '将指定的媒体账号从一个应用迁移到另一个应用',
      },
    })
    .input(MigrateAccountSchema)
    .output(z.object({ id: z.string().describe('迁移后的账号ID') }))
    .mutation(async ({ ctx, input }) => {
      // 验证账号是否在当前系统中存在
      const existingAccount = await MediaAccountServies.getFindFirst({
        platformCode: input.platformCode,
        platformUserId: input.platformUserId,
      })

      if (existingAccount) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '账号已存在',
        })
      }

      const tokenJsonStr = JSON.stringify({
        access_token: input.accessToken,
        refresh_token: input.refreshToken,
        expires_in: input.expiresIn,
        refresh_token_expires_in: input.refreshExpiresIn,
        accountRole: input.accountRole || '',
      })

      const authAccount = {
        platformCode: input.platformCode,
        platformUserId: input.platformUserId,
        platformUserName: input.platformUserName || '',
        platformAvatar: input.platformAvatar || '',
        platformTokenExpiresAt: new Date(input.tokenExpiresAt),
        platformRefreshTokenExpiresAt: new Date(input.refreshTokenExpiresAt),
        platformCookieHash: tokenJsonStr,
        parentPlatformUserId: input.parentPlatformUserId || '',
      }

      const newAccount = await MediaAccountServies.createOpenPlatformAccount(authAccount, ctx.application.id)

      return {
        id: newAccount.id,
      }
    }),
  // POST /media-accounts/authorize - 发起授权
  initiateAuthorization: openAPIProcedure
    .input(InitiateAuthorizationSchema)
    .meta({
      openapi: {
        method: 'POST',
        path: '/media-accounts/authorize',
        protect: true,
        tags: ['媒体账号'],
        summary: '发起媒体账号授权',
        description: '发起媒体平台账号授权，支持本地授权和云授权两种方式',
      },
    })
    .output(InitiateAuthorizationResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const { platformCode, authType, clientId, callBackData, proxy } = input

      // 验证platformCode
      const platformConfigs = await systemConfigCache.getPlatformConfigs()
      if (!platformConfigs || !platformConfigs[platformCode]) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '无效的platformCode',
        })
      }
      return await MediaAccountServies.authorizeMediaAccount(
        authType,
        platformCode,
        proxy,
        ctx.application,
        ctx.req.server,
        { callBackData },
        clientId,
      )
    }),

  // PUT /media-accounts/authorize - 更新授权
  updateAuthorization: openAPIProcedure
    .input(UpdateAuthorizationSchema)
    .meta({
      openapi: {
        method: 'PUT',
        path: '/media-accounts/authorize/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '更新媒体账号授权',
        description: '更新媒体账号的授权信息',
      },
    })
    .output(InitiateAuthorizationResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, authType, clientId, callBackData, proxy } = input

      // 验证媒体账号是否存在
      const existingAccount = await MediaAccountServies.getFindFirst({
        id,
      })

      if (!existingAccount) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在',
        })
      }

      return await MediaAccountServies.authorizeMediaAccount(
        authType,
        existingAccount.platformCode,
        proxy,
        ctx.application,
        ctx.req.server,
        { callBackData, accountId: existingAccount.id },
        clientId,
      )
    }),
})
