import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@coozf/ui/components/tabs'
import { ClientVersions } from '../oem/client-versions'
import { PublicVersions } from '../oem/public-verisons'
import type { TabComponentProps } from '../types'
import { useState } from 'react'

export type PublicVersion = {
  id: string
  version: string
  description: string
  platform: string
  publishedAt: string
  forceUpdate: boolean
}

export function ClientManagementTab({ applicationId, application }: TabComponentProps) {
  const [selectedTab, setSelectedTab] = useState<'oem' | 'pub'>('oem')
  const oemEnabled = application?.oemEnabled || false

  return (
    <div className="space-y-6">
      {/* 客户端版本管理 */}
      {oemEnabled ? (
        <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as 'oem' | 'pub')}>
          <TabsList className='w-full'>
            <TabsTrigger value="oem">oem版本</TabsTrigger>
            <TabsTrigger value="pub">通用版</TabsTrigger>
          </TabsList>
          <TabsContent value="oem">
            <ClientVersions applicationId={applicationId} application={application} />
          </TabsContent>
          <TabsContent value="pub">
            <PublicVersions application={application} />
          </TabsContent>
        </Tabs>
      ) : (
        <PublicVersions application={application} />
      )}
    </div>
  )
}
