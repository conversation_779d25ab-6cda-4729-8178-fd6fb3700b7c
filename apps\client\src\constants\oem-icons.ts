// constants/oem-icons.ts
export const OEM_ICON_CONFIGS = {
  logo: {
    label: '品牌 Logo',
    constraints: {
      maxSize: 5 * 1024 * 1024, // 5MB
      acceptedFormats: ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'] as const,
      recommendedSize: { width: 512, height: 512 },
      required: true,
    },
    description: '应用主图标，用于界面展示',
  },
  trayIconMac: {
    label: 'Mac 托盘图标',
    constraints: {
      maxSize: 1 * 1024 * 1024, // 1MB
      acceptedFormats: ['image/png'] as const,
      recommendedSize: { width: 44, height: 44 },
      required: true,
    },
    description: 'Mac 菜单栏图标（Template Image，建议使用黑色图标，系统会自动适配明暗模式）',
  },
  trayIconWindows: {
    label: 'Windows 托盘图标',
    constraints: {
      maxSize: 1 * 1024 * 1024, // 1MB
      acceptedFormats: ['image/png', 'image/x-icon'] as const,
      recommendedSize: { width: 32, height: 32 },
      required: true,
    },
    description: 'Windows 系统托盘图标',
  },
} as const