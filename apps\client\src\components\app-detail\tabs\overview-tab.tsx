import { ApplicationInfo } from '../overview/application-info'
import { DataStatistics } from '../overview/data-statistics'
import { TrendsChart } from '../overview/trends-chart'
import { useSecretManagement } from '../hooks/useSecretManagement'
import { useApplicationDetails } from '../hooks/useApplicationDetails'
import type { TabComponentProps } from '../types'

export function OverviewTab({ applicationId, application }: TabComponentProps) {
  const { copyToClipboard } = useSecretManagement(applicationId)
  const { trendsData } = useApplicationDetails(applicationId)

  return (
    <div className="space-y-6">
      <ApplicationInfo 
        application={application} 
        onCopyToClipboard={copyToClipboard}
      />
      
      <DataStatistics 
        application={application} 
        applicationId={applicationId}
      />
      
      {trendsData && <TrendsChart data={trendsData} />}
    </div>
  )
}