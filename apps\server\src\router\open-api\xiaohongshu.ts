import { publicProcedure, router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import {
  PageListQuerySchema,
  createPaginationResponseSchema,
  PageListResponseSchema,
  MaterialListResponseSchema,
  MaterialListQuerySchema,
  NoteListResponseSchema,
  NoteListQuerySchema,
  CommentListQuerySchema,
  CommentListResponseSchema,
  TradeCardListQuerySchema,
  TradeCardListResponseSchema,
  ThirdSendMessageSchema,
  ThirdSendMessageResponseSchema,
  BindUsersQuerySchema,
  BindUsersResponseSchema,
} from '@coozf/zod'
import { TRPCError } from '@trpc/server'
import axios from 'axios'
import { encryptXiaohongshuData, paginate, OpenPlatformTokenData } from '@/lib/utils'
import { env } from '@/env'
import { AccountAuthType } from '@prisma/client'
import z from 'zod'

export const openXiaohongshuRouter = router({
  // POST /xiaohongshu/open/im/page/list - 获取落地页列表
  pageList: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/page/list',
        protect: true,
        tags: ['小红书'],
        summary: '查落地页列表接口',
        description: '查询授权客户在聚光创建的落地页列表，默认时间倒序，请注意KOS暂不支持获取落地页',
      },
    })
    .input(PageListQuerySchema)
    .output(createPaginationResponseSchema(PageListResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/page/list'

      const requestData = {
        user_id: account.platformUserId,
        page_num: input.page,
        page_size: input.pageSize,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data.list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/material/list - 名片/留资卡列表
  materialList: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/material/list',
        protect: true,
        tags: ['小红书'],
        summary: '查名片/留资卡列表接口',
        description: '查询授权客户在聚光创建的名片/留资卡列表，默认时间倒序',
      },
    })
    .input(MaterialListQuerySchema)
    .output(createPaginationResponseSchema(MaterialListResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/material/list'

      const requestData = {
        user_id: account.platformUserId,
        page_num: input.page,
        page_size: input.pageSize,
        type: input.type,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data.list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/note/list - 获取笔记列表
  noteList: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/note/list',
        protect: true,
        tags: ['小红书'],
        summary: '查笔记列表接口',
        description: '查询授权客户在聚光创建的笔记列表列表，默认时间倒序',
      },
    })
    .input(NoteListQuerySchema)
    .output(createPaginationResponseSchema(NoteListResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/note/list'

      const requestData = {
        user_id: account.platformUserId,
        page_num: input.page,
        page_size: input.pageSize,
        note_id: input.noteId,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data.list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/comment/list - 获取意向评论列表
  commentList: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/comment/list',
        protect: true,
        tags: ['小红书'],
        summary: '查意向评论列表接口',
        description:
          '1、若需要使用意向评论获取与回复，请对接意向评论推送能力，该接口后续不再维护（只for企业号本身仍可使用，但对于员工号以及在私信通上做了企业号授权的账号将不可使用），2、接口调用有频控，100次/s，且为统一频控，超出频控会报错，请勿频繁调用',
      },
    })
    .input(CommentListQuerySchema)
    .output(createPaginationResponseSchema(CommentListResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/comment/list'

      const requestData = {
        user_id: account.platformUserId,
        page_num: input.page,
        page_size: input.pageSize,
        begin_time: input.startDate,
        end_time: input.endDate,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data.list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/trade-card/list - 获取交易卡列表
  tradeCardList: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/trade-card/list',
        protect: true,
        tags: ['小红书'],
        summary: '查交易卡列表接口',
        description: '查询授权客户在聚光创建的交易卡列表，默认时间倒序',
      },
    })
    .input(TradeCardListQuerySchema)
    .output(createPaginationResponseSchema(TradeCardListResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/material/list'

      const requestData = {
        user_id: account.platformUserId,
        page_num: input.page,
        page_size: input.pageSize,
        type: input.type,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data.list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/bind_users - 获取kos用户列表
  bindUsers: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/bind_users',
        protect: true,
        tags: ['小红书'],
        summary: '获取kos用户列表',
        description: '获取kos用户列表',
      },
    })
    .input(BindUsersQuerySchema)
    .output(createPaginationResponseSchema(BindUsersResponseSchema))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/auth/bind_users'

      const requestData = {
        user_id: account.platformUserId,
      }

      const pageList = await axios.post(url, requestData, {
        headers: {
          'Access-Token': tokenData.access_token,
        },
      })

      if (pageList.data.code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: pageList.data.message,
        })
      }

      return paginate(
        {
          page: input.page,
          pageSize: input.pageSize,
        },
        {
          getTotal: () => pageList.data.data.total,
          getItems: () => pageList.data.data?.kos_user_list || [],
        },
      )
    }),

  // POST /xiaohongshu/open/im/send - 发送私信接口
  thridSendMessage: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/xiaohongshu/open/im/send',
        protect: true,
        tags: ['小红书'],
        summary: '发送私信接口',
        description: '发送私信接口',
      },
    })
    .input(ThirdSendMessageSchema)
    .output(ThirdSendMessageResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'xiaohongshuopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是小红书开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://adapi.xiaohongshu.com/api/open/im/third/send'

      const content: {
        message_id?: string
        id?: string
        card_id?: string
        content?: string
        comment_id?: string
        text?: string
        link?: string
        video?: string
        material?: string
        note?: string
        size?: { width: number; height: number }
        duration?: number
        cover?: string
        video_size?: number
        note_id?: string
        content_type?: string
        trade_card?: string
        page_id?: string
      } = {}

      switch (input.messageType) {
        case 'TEXT':
          content.text = input.content.text
          break
        case 'IMAGE':
          content.link = input.content.link
          content.size = {
            width: input.content.size?.width ?? 0,
            height: input.content.size?.height ?? 0,
          }
          break
        case 'VIDEO':
          content.duration = input.content.duration
          content.size = {
            width: input.content.size?.width ?? 0,
            height: input.content.size?.height ?? 0,
          }
          content.cover = input.content.cover
          content.video_size = input.content.videoSize
          content.link = input.content.link
          break
        case 'CARD':
          switch (input.content.contentType) {
            case 'note':
              content.note_id = input.content.noteId
              content.content_type = input.content.contentType
              break
            case 'common':
              content.page_id = input.content.pageId
              content.content_type = input.content.contentType
              break
            case 'purchaseComments':
              content.comment_id = input.content.commentId
              content.content_type = input.content.contentType
              content.content = input.content.content
              break
            case 'serviceCard':
              content.card_id = input.content.cardId
              content.content_type = input.content.contentType
              break
            case 'social_card':
              content.id = input.content.id
              content.content_type = input.content.contentType
              break
            case 'lead_card':
              content.id = input.content.id
              content.content_type = input.content.contentType
              break
            case 'tradeBusinessCard':
              content.id = input.content.id
              content.content_type = input.content.contentType
              break
          }
          break
        case 'REVOKE':
          content.message_id = input.content.messageId
          break
      }

      const encryptedContent = encryptXiaohongshuData(JSON.stringify(content), env.XIAOHONGSHU_SECRET)

      const requestData = {
        user_id: account.platformUserId,
        request_id: input.requestId,
        message_type: input.messageType,
        from_user_id: input.fromUserId,
        to_user_id: input.toUserId,
        third_account_id: input.thirdAccountId,
        timestamp: input.timestamp,
        content: encryptedContent,
      }

      try {
        const response = await axios.post(url, requestData, {
          headers: {
            'Access-Token': tokenData.access_token,
          },
        })

        console.log('response.data', response)

        if (response.data.code !== 0) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: response.data.message,
          })
        }
  
        return {
          message_id: response.data.data.message_id,
          request_id: response.data.data.request_id,
        }
      } catch (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '发送失败',
        })
      }
      
    }),
})
