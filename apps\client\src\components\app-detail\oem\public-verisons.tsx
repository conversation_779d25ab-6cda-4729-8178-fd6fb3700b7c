import { trpc } from '@/lib/trpc'
import type { Application } from '../types'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@coozf/ui/components/button'
import { Card } from '@coozf/ui/components/card'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@coozf/ui/components/dropdown-menu'
import { Tabs, TabsList, TabsTrigger } from '@coozf/ui/components/tabs'
import { TableHead, Table, TableHeader, TableRow, TableBody, TableCell } from '@coozf/ui/components/table'
import { Monitor, Apple, Download } from 'lucide-react'
import { LoadingContainer } from '@coozf/ui/components/loading'
import { useState } from 'react'
import { Badge } from '@coozf/ui/components/badge'
import { formatDate } from '@coozf/ui/lib/day'

// type VersionItem = RouterOutput['clientVersion']['getPublicVersion']['data'][number]
export const PublicVersions = ({ application }: { application: Application }) => {
  const [selectedTab, setSelectedTab] = useState<'WIN' | 'MAC'>('WIN')
  const { data, isLoading } = useQuery(
    trpc.clientVersion.getPublicVersion.queryOptions({
      page: 1,
      pageSize: 10,
      applicationId: application.id,
      platform: selectedTab,
    }),
  )
  return (
    <div className="flex gap-2 flex-col">
      <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as 'WIN' | 'MAC')}>
        <TabsList>
          <TabsTrigger value="WIN">WINDOWS</TabsTrigger>
          <TabsTrigger value="MAC">MAC</TabsTrigger>
        </TabsList>
      </Tabs>
      {isLoading ? (
        <LoadingContainer />
      ) : (
        <Card className="border border-muted/30">
          <Table>
            <TableHeader>
              <TableRow className="border-muted/30 h-9">
                <TableHead className="text-xs font-medium">版本</TableHead>
                <TableHead className="text-xs font-medium">平台</TableHead>
                <TableHead className="text-xs font-medium">描述</TableHead>
                <TableHead className="text-xs font-medium">时间</TableHead>
                <TableHead className="text-xs font-medium w-16">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.data.map((version) => (
                <TableRow key={version.id} className="border-muted/20 hover:bg-muted/5 h-10">
                  <TableCell>
                    <Badge>{version.version}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {version.platform === 'WIN' ? (
                        <Monitor className="w-3 h-3 text-muted-foreground" />
                      ) : (
                        <Apple className="w-3 h-3 text-muted-foreground" />
                      )}
                      <span className="text-xs">{version.platform === 'WIN' ? 'Win' : 'Mac'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-muted-foreground text-xs">{version.description}</span>
                  </TableCell>
                  <TableCell className="text-xs text-muted-foreground">{formatDate(version.createdAt)}</TableCell>
                  <TableCell>
                    {version.downloadUrls && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                            <Download className="w-4 h-4" />
                            <span className="sr-only">Toggle menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {Object.keys(version.downloadUrls).map((key) => (
                            <DropdownMenuItem key={key}>
                              <a
                                href={version.downloadUrls[key as keyof typeof version.downloadUrls]}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {key}
                              </a>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}
    </div>
  )
}
