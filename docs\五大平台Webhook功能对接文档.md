# 五大社交媒体平台 Webhook 功能对接文档

## 📋 概述

本文档详细介绍了 qdy-service 项目中集成的五大社交媒体平台的 Webhook 功能，包括事件类型、数据结构、对接方式和返回字段说明，为业务迁移提供完整的技术参考。

## 🎯 支持的平台

| 平台 | 复杂度 | 事件类型数量 | 特殊要求 |
|------|--------|-------------|----------|
| 🎵 **抖音开放平台** | ⭐⭐⭐⭐⭐ | 14种 | 签名验证 |
| 📹 **微信视频号** | ⭐⭐⭐⭐ | 3种 | 第三方API |
| ⚡ **快手开放平台** | ⭐⭐⭐ | 6种 | AES解密 |
| 📱 **微博开放平台** | ⭐⭐ | 1种 | SHA1验证 |
| 📝 **小红书开放平台** | ⭐⭐⭐⭐ | 5种 | AES解密 |

---

## 🎵 1. 抖音开放平台 Webhook

### 1.1 基础配置

```typescript
// Webhook端点配置
const webhookUrl = process.env.WEBHOOK_URL; // 如: "/ttac99dde1ess-webhook"

// 请求方法
POST ${webhookUrl}

// 请求头验证
headers: {
  'x-douyin-signature': string,  // 抖音签名
  'msg-id': string              // 消息ID
}
```

### 1.2 支持的事件类型

#### 基础事件
```typescript
enum WebhookEvents {
  // 验证webhook
  VerifyWebhook = 'verify_webhook',
  
  // 私信相关
  IMReceiveMessage = 'im_receive_msg',      // 接收私信
  IMSendMessage = 'im_send_msg',            // 发送私信  
  IMRecallMsg = 'im_recall_msg',            // 撤回私信
  ImEnterDirectMessage = 'im_enter_direct_msg', // 进入私信会话
  
  // 群消息相关
  IMGroupReceiveMessage = 'im_group_receive_msg', // 接收群消息
  IMGroupSendMessage = 'im_group_send_msg',       // 发送群消息
  EnterGroupAuditChange = 'enter_group_audit_change', // 加群申请
  GroupFansEvent = 'group_fans_event',            // 加群成功
  
  // 用户行为
  NewFollowAction = 'new_follow_action',    // 用户关注
  NewVideoDigg = 'new_video_digg',         // 视频点赞
  CommentReply = 'item_comment_reply',     // 评论回复
  
  // 授权相关
  Authorize = 'authorize',                 // 用户授权
  Unauthorize = 'unauthorize',             // 取消授权
  ContractAuthorize = 'contract_authorize', // 合同授权
  ContractUnauthorize = 'contract_unauthorize' // 合同取消授权
}
```

### 1.3 数据结构

#### 请求体结构
```typescript
interface WebhookBody {
  event: WebhookEvents;           // 事件类型
  client_key: string;             // 应用标识
  from_user_id: string;           // 发送方用户ID
  to_user_id: string;             // 接收方用户ID
  content: {
    challenge?: string;           // webhook验证码
    user_infos?: {               // 用户信息
      open_id: string;
      nick_name: string;
      avatar: string;
    }[];
    card_data?: {                // 留资卡片数据
      label: string;
      value: string;
    }[];
    card_status?: number;        // 卡片状态
    scopes?: string[];           // 权限范围
    [key: string]: unknown;     // 其他内容
  };
  create_time: number;           // 创建时间戳
  log_id?: string;              // 日志ID
}
```

### 1.4 响应格式

```typescript
// 成功响应
return 'success';

// 验证响应
return body.content.challenge;
```

---

## 📹 2. 微信视频号 Webhook

### 2.1 基础配置

```typescript
// Webhook端点配置 
const wechatWebhookUrl = process.env.WECHAT_WEBHOOK_URL; // 如: "/wechat-webhook"
const wechatWebhookNewUrl = process.env.WECHAT_WEBHOOK_NEW_URL; // 新版本

// 请求方法
POST ${wechatWebhookUrl}

// 数据格式：使用JsonBigint解析
const body = JsonBigint({ storeAsString: true }).parse(req.rawBody.toString());
```

### 2.2 支持的事件类型

```typescript
enum WebhookWechatEvents {
  FinderMsg = 'FinderBypMsg',      // 视频号私信消息
  Offline = 'Offline',             // 账号下线通知
  FinderSyncMsg = 'FinderSyncMsg'  // 视频号消息通知(点赞、评论、小红心)
}
```

### 2.3 数据结构

#### 基础结构
```typescript
interface WebHookWechatBody {
  TypeName: WebhookWechatEvents;  // 回调消息类型
  Appid: string;                  // 设备appid
  Wxid: string;                   // 归属微信的wxid
  Data: {
    // 具体数据内容，根据事件类型不同而变化
  };
}
```

### 2.4 响应格式

```typescript
// 所有webhook都返回空或处理结果
return; // 或具体的处理结果
```

---

## ⚡ 3. 快手开放平台 Webhook

### 3.1 基础配置

```typescript
// Webhook端点配置
const kuaishouWebhookUrl = process.env.KUAISHOU_WEBHOOK_URL;

// 请求方法  
POST ${kuaishouWebhookUrl}

// 数据解密配置
const kuaishouClientSecret = process.env.KUAISHOU_CLIENT_SECRET;
```

### 3.2 支持的事件类型

```typescript
enum WebhookKuaishouEvents {
  Test = 'TEST',                              // 测试事件
  ReceiveMsg = 'RECEIVE_MSG',                 // 接收私信
  ImEnterSession = 'IM_ENTER_SESSION',        // 进入消息会话
  PushPotentialCustomerMsg = 'PUSH_POTENTIAL_CUSTOMER_MSG', // 互动信息推送
  Unauthorize = 'UN_AUTHORIZE',               // 取消授权
  Authorize = 'AUTHORIZE'                     // 授权成功
}
```

### 3.3 响应格式

```typescript
// 标准响应格式
return {
  result: 1,                    // 1=成功 0=失败
  message_id: body.message_id   // 返回消息ID
};
```

---

## 📱 4. 微博开放平台 Webhook  

### 4.1 基础配置

```typescript
// Webhook端点配置
const weiboWebhookUrl = process.env.WEIBO_WEBHOOK_URL;

// 验证配置
const weiboClientSecret = process.env.WEIBO_CLIENT_SECRET;

// 请求方法
GET/POST ${weiboWebhookUrl}
```

### 4.2 响应格式

```typescript
// GET验证响应
return echostr; // 返回查询参数中的echostr

// POST消息响应  
return 'success'; // 或其他成功标识
```

---

## 📝 5. 小红书开放平台 Webhook

### 5.1 基础配置

```typescript
// 多个Webhook端点
const endpoints = {
  bindAccount: '/open/im/third/bind_account',      // 账号绑定
  unbindAccount: '/open/im/third/unbind_account',  // 账号解绑
  bindUser: '/open/im/auth/bind_user/event',       // 用户绑定
  message: '/open/im/send',                        // 消息接收
  pushLead: '/open/im/push_lead'                   // 留资推送
};

// 解密密钥
const xiaohongshuSecret = process.env.XIAOHONGSHU_SECRET;
```

### 5.2 支持的事件类型

```typescript
enum BindEventXiaohongshu {
  BindAccount = 'BindAccount',      // 聚光账号绑定
  UnBindAccount = 'UnBindAccount',  // 聚光账号解绑  
  BindKosUser = 'BindKosUser'       // KOS用户绑定
}
```

### 5.3 响应格式

```typescript
// 所有端点统一响应格式
return {
  success: true,      // 成功标识
  message?: string,   // 可选消息
  data?: any         // 可选数据
};
```

---

## 🔧 6. 通用对接指南

### 6.1 环境变量配置

```bash
# 抖音配置
WEBHOOK_URL=/ttac99dde1ess-webhook
DOUYIN_CLIENT_SECRET=your_douyin_secret

# 微信视频号配置  
WECHAT_WEBHOOK_URL=wechat-webhook
WECHAT_WEBHOOK_NEW_URL=wechat-webhook-new

# 快手配置
KUAISHOU_WEBHOOK_URL=kuaishou-webhook
KUAISHOU_CLIENT_SECRET=your_kuaishou_secret

# 微博配置
WEIBO_WEBHOOK_URL=weibo-webhook  
WEIBO_CLIENT_SECRET=your_weibo_secret

# 小红书配置
XIAOHONGSHU_SECRET=your_xiaohongshu_secret

# Redis配置(用于队列处理)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_SYNC_DB=0
```

## 📊 7. 平台对比总结

| 特性 | 抖音 | 微信视频号 | 快手 | 微博 | 小红书 |
|------|------|-----------|------|------|--------|
| **技术复杂度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **事件类型** | 14种 | 3种 | 6种 | 1种 | 5种 |
| **数据加密** | ❌ | ❌ | ✅ AES | ❌ | ✅ AES |
| **签名验证** | ✅ 自定义 | ❌ | ❌ | ✅ SHA1 | ❌ |
| **响应格式** | 字符串 | 空/对象 | 标准JSON | 字符串 | 标准JSON |
| **官方支持** | ✅ 完整 | ❌ 第三方 | ✅ 完整 | ✅ 完整 | ✅ 完整 |
| **文档质量** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

### 迁移优先级建议

1. **高优先级**: 抖音开放平台 - 功能最完整，用户基数大
2. **中优先级**: 小红书开放平台 - 年轻用户群体，商业价值高  
3. **中优先级**: 快手开放平台 - 下沉市场重要平台
4. **低优先级**: 微信视频号 - 依赖第三方API，稳定性待观察
5. **低优先级**: 微博开放平台 - 功能相对简单，用户活跃度下降

---

*最后更新时间: 2024年12月*
