import { trpc, type RouterOutput } from '@/lib/trpc'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import { alertDialog } from '@coozf/ui/lib/alert-dialog'
import type { ColumnDef } from '@tanstack/react-table'
import { ArrowUpFromLine, Download, Edit, MoreHorizontal, Trash2 } from 'lucide-react'
import { StatusBadge } from '../status-badge'
import type { ClientBuildStatus } from '@coozf/zod'
import { useMutation } from '@tanstack/react-query'
import { LoadingButton } from '@coozf/ui/components/loading'

type VersionItem = RouterOutput['version']['list']['data'][number]

const typeMap = {
  DESKTOP: '桌面端',
  BROWSER_PLUGIN: '浏览器插件',
  RPA: 'RPA',
  CRAWLER: '爬虫脚本',
}

const typeIconMap = {
  DESKTOP: '💻',
  BROWSER_PLUGIN: '🔌',
  RPA: '🤖',
  CRAWLER: '🕷️',
}

const platformMap = {
  WIN: 'Windows',
  MAC: 'macOS',
}

const platformIconMap = {
  WIN: '🪟',
  MAC: '🍎',
}

export function getTableColumns(
  handleEditVersion: (version: VersionItem) => void,
  handleDeleteVersion: (versionId: string) => void,
  handlePublishVersion: (versionId: string) => void,
): ColumnDef<VersionItem>[] {
  return [
    {
      accessorKey: 'version',
      header: '版本号',
      cell: ({ row }) => {
        const version = row.original
        return (
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                <span className="text-xs">{typeIconMap[version.type]}</span>
              </div>
            </div>
            <div>
              <div className="font-mono font-medium">{version.version}</div>
              <div className="text-sm text-muted-foreground">{typeMap[version.type]}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'platform',
      header: '平台',
      cell: ({ row }) => {
        const platform = row.original.platform
        if (!platform) return <span className="text-muted-foreground">-</span>
        return (
          <div className="flex items-center gap-2">
            <span className="text-lg">{platformIconMap[platform]}</span>
            <span>{platformMap[platform]}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'forceUpdate',
      header: '强制更新',
      cell: ({ row }) => {
        const forceUpdate = row.original.forceUpdate
        return <Badge variant={forceUpdate ? 'destructive' : 'secondary'}>{forceUpdate ? '是' : '否'}</Badge>
      },
    },
    {
      accessorKey: 'publishedBy',
      header: '发布人',
      cell: ({ row }) => <div className="text-sm">{row.original.admin_user.name}</div>,
    },
    {
      accessorKey: 'publishedAt',
      header: '发布时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.publishedAt).toLocaleString('zh-CN')}
        </div>
      ),
    },
    {
      accessorKey: 'description',
      header: '版本说明',
      cell: ({ row }) => {
        const description = row.original.description
        return <div className="max-w-xs truncate text-sm text-muted-foreground">{description || '-'}</div>
      },
    },
    {
      accessorKey: 'isActive',
      header: '状态',
      cell: ({ row }) => {
        const { isActive, oemSuccessCount, clientVersions, type } = row.original
        if (type === 'DESKTOP' && !isActive) {
          if (oemSuccessCount === clientVersions.length) {
            return <Badge variant="default">构建完成</Badge>
          } else {
            return (
              <div
                onClick={() => {
                  alertDialog.show({
                    title: 'oem构建中',
                    description: `当前进度：${oemSuccessCount}/${clientVersions.length}`,
                    content: (
                      <div className="flex flex-col gap-2">
                        {clientVersions.map((cv) => (
                          <OemItem
                            key={cv.id}
                            id={cv.id}
                            name={cv.oemConfig.name}
                            brandName={cv.oemConfig.brandName}
                            buildStatus={cv.buildStatus}
                          />
                        ))}
                      </div>
                    ),
                    cancelText: '关闭',
                    showConfirm: false,
                  })
                }}
              >
                <Badge variant="warning">构建中 {`${oemSuccessCount}/${clientVersions.length}`}</Badge>
              </div>
            )
          }
        }
        return <Badge variant={isActive ? 'success' : 'warning'}>{isActive ? '已发布' : '未发布'}</Badge>
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const version = row.original
        const hasPublish = !version.isActive && version.oemSuccessCount === version.clientVersions.length
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {hasPublish && (
                <DropdownMenuItem onClick={() => handlePublishVersion(version.id)}>
                  <ArrowUpFromLine className="mr-2 h-4 w-4" />
                  发布
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => window.open(version.downloadUrl, '_blank')}>
                <Download className="mr-2 h-4 w-4" />
                下载
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditVersion(version)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteVersion(version.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}

const OemItem = ({
  id,
  name,
  brandName,
  buildStatus,
}: {
  id: string
  name: string
  brandName: string
  buildStatus: ClientBuildStatus
}) => {
  const mutation = useMutation(
    trpc.version.buildOem.mutationOptions({
      onSuccess: () => {
        // Handle success
      },
      onError: () => {
        // Handle error
      },
    }),
  )
  return (
    <div key={id} className="flex items-center gap-2">
      <span className="font-medium">{name}</span>({brandName})
      <StatusBadge status={buildStatus} />
      {['CANCELLED', 'FAILED'].includes(buildStatus) && (
        <LoadingButton
          isPending={mutation.isPending}
          disabled={mutation.isPending}
          onClick={() => mutation.mutate({ clientVersionId: id })}
          size="sm"
          variant="link"
        >
          重新构建
        </LoadingButton>
      )}
    </div>
  )
}
