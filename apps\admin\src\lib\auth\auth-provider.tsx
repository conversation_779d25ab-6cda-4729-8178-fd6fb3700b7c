import { useEffect, useState, type <PERSON>actNode } from 'react'
import { router } from '@/router'
import { AuthContext, type AuthContextType } from './auth-context'
import { authClient } from './auth-client'
import { TwoFactor } from '@/components/admin-user/two-factor'

export function AuthProvider({ children }: { children: ReactNode }) {
  const session = authClient.useSession()
  const [showTwoFactor, setShowTwoFactor] = useState(false)

  const logout = async () => {
    await authClient.signOut()
  }

  useEffect(() => {
    if (!session.data && !session.isPending) {
      router.navigate({ to: '/login' })
    }
  }, [session.data, session.isPending])

  useEffect(() => {
    if (session.data && !session.data.user.twoFactorEnabled) {
      setShowTwoFactor(true)
    }
  }, [session.data])

  const contextValue: AuthContextType = {
    isAuthenticated: !!session.data,
    user: session.data?.user,
    isLoading: session.isPending,
    logout,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
      <TwoFactor open={showTwoFactor} onSuccess={() => setShowTwoFactor(false)} />
    </AuthContext.Provider>
  )
}
