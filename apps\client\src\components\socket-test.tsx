import { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { Button } from '@coozf/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { ScrollArea } from '@coozf/ui/components/scroll-area'
import { Separator } from '@coozf/ui/components/separator'
import { Trash2, Wifi, WifiOff, RotateCcw } from 'lucide-react'

interface UpdateMessage {
  version: string
  forceUpdate: boolean
  description?: string
  downloadUrl: string
  timestamp: number
}

interface MessageLog {
  id: string
  type: string
  event: string
  message: UpdateMessage
  receivedAt: string
}

interface AckTestResult {
  id: string
  deviceId: string
  success: boolean
  responseTime: number
  socketId: string
  timestamp: string
  error?: string
}

export function SocketTest() {
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [deviceId, setDeviceId] = useState({
    deviceId: 'test-device-001',
    name: 'Test Device',
    type: 'WIN',
    version: '1.0.0',
    appId: 'test-app-001',
  })
  const [messages, setMessages] = useState<MessageLog[]>([])
  const [registrationStatus, setRegistrationStatus] = useState<'idle' | 'registering' | 'registered' | 'failed'>('idle')
  const [ackTestResults, setAckTestResults] = useState<AckTestResult[]>([])
  const [isTesting, setIsTesting] = useState(false)
  const socketRef = useRef<Socket | null>(null)

  // 连接到 Socket.IO 服务器
  const connectSocket = async () => {
    if (socketRef.current?.connected) return

    setIsConnecting(true)

    try {
      // 创建 Socket.IO 连接
      const socket = io('http://localhost:3000', {
        path: '/socket.io/',
        transports: ['websocket'],
        timeout: 5000,
      })

      socketRef.current = socket

      // 监听连接事件
      socket.on('connect', () => {
        console.log('Socket connected:', socket.id)
        setIsConnected(true)
        setIsConnecting(false)

        // 注册设备
        registerDevice()
      })

      socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason)
        setIsConnected(false)
        setRegistrationStatus('idle')
      })

      socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
        setIsConnecting(false)
        setIsConnected(false)
      })

      // 监听版本更新消息
      socket.on('update:reptile', (message: UpdateMessage) => {
        console.log('收到爬虫更新消息:', message)
        addMessageLog('爬虫 (CRAWLER)', 'update:reptile', message)
      })

      socket.on('update:rpa', (message: UpdateMessage) => {
        console.log('收到RPA更新消息:', message)
        addMessageLog('RPA', 'update:rpa', message)
      })

      socket.on('update', (message: UpdateMessage) => {
        console.log('收到桌面端更新消息:', message)
        addMessageLog('桌面端 (DESKTOP)', 'update', message)
      })

      socket.on('update:browser', (message: UpdateMessage) => {
        console.log('收到浏览器插件更新消息:', message)
        addMessageLog('浏览器插件 (BROWSER_PLUGIN)', 'update:browser', message)
      })

      socket.on('location', (message: any) => {
        const { requestId, type } = message

        console.log('收到定位请求', { requestId, type })
        addMessageLog('定位任务 (LOCATION)', 'location', message)

        socket.emit('location_response', {
          requestId,
          success: true,
          data: {
            latitude: 39.9042,
            longitude: 116.4074,
          },
        })
      })
    } catch (error) {
      console.error('Socket connection failed:', error)
      setIsConnecting(false)
    }
  }

  // 注册设备
  const registerDevice = () => {
    if (!socketRef.current) return

    setRegistrationStatus('registering')

    socketRef.current.emit('device:register', deviceId, (response: any) => {
      if (response?.success) {
        console.log('设备注册成功:', response)
        setRegistrationStatus('registered')
      } else {
        console.error('设备注册失败:', response)
        setRegistrationStatus('failed')
      }
    })
  }

  // 断开连接
  const disconnectSocket = () => {
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }
    setIsConnected(false)
    setRegistrationStatus('idle')
  }

  // 添加消息日志
  const addMessageLog = (type: string, event: string, message: UpdateMessage) => {
    const logEntry: MessageLog = {
      id: Date.now().toString(),
      type,
      event,
      message,
      receivedAt: new Date().toLocaleString('zh-CN'),
    }

    setMessages((prev) => [logEntry, ...prev])
  }

  // 清空消息日志
  const clearMessages = () => {
    setMessages([])
  }

  // 清空ACK测试结果
  const clearAckResults = () => {
    setAckTestResults([])
  }

  // 多次ACK测试
  const runAckTest = async (testCount: number = 10) => {
    if (!socketRef.current?.connected) {
      alert('请先连接Socket服务器')
      return
    }

    setIsTesting(true)
    setAckTestResults([])

    for (let i = 1; i <= testCount; i++) {
      const testDeviceId = `ack-test-${Date.now()}-${i}`
      const startTime = Date.now()

      try {
        const result = await new Promise<AckTestResult>((resolve) => {
          const timeout = setTimeout(() => {
            resolve({
              id: `test-${i}`,
              deviceId: testDeviceId,
              success: false,
              responseTime: 5000,
              socketId: socketRef.current?.id || '',
              timestamp: new Date().toLocaleString('zh-CN'),
              error: 'ACK超时 (5秒)',
            })
          }, 5000)

          socketRef.current?.emit('device:register', testDeviceId, (response: any) => {
            clearTimeout(timeout)
            const endTime = Date.now()
            const responseTime = endTime - startTime

            resolve({
              id: `test-${i}`,
              deviceId: testDeviceId,
              success: response?.success || false,
              responseTime,
              socketId: socketRef.current?.id || '',
              timestamp: new Date().toLocaleString('zh-CN'),
              error: response?.success ? undefined : response?.error || '未知错误',
            })
          })
        })

        setAckTestResults((prev) => [...prev, result])

        // 每次测试间隔100ms，避免请求过于频繁
        if (i < testCount) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      } catch (error) {
        const result: AckTestResult = {
          id: `test-${i}`,
          deviceId: testDeviceId,
          success: false,
          responseTime: Date.now() - startTime,
          socketId: socketRef.current?.id || '',
          timestamp: new Date().toLocaleString('zh-CN'),
          error: error instanceof Error ? error.message : '测试异常',
        }
        setAckTestResults((prev) => [...prev, result])
      }
    }

    setIsTesting(false)
  }

  // 清理副作用
  useEffect(() => {
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect()
      }
    }
  }, [])

  const getStatusColor = () => {
    if (isConnecting) return 'yellow'
    if (isConnected && registrationStatus === 'registered') return 'green'
    if (isConnected && registrationStatus === 'failed') return 'orange'
    return 'red'
  }

  const getStatusText = () => {
    if (isConnecting) return '连接中...'
    if (isConnected && registrationStatus === 'registering') return '注册中...'
    if (isConnected && registrationStatus === 'registered') return '已连接并注册'
    if (isConnected && registrationStatus === 'failed') return '已连接但注册失败'
    if (isConnected) return '已连接'
    return '未连接'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Socket.IO 版本更新测试
            <Badge variant="outline" className={`text-${getStatusColor()}-600 border-${getStatusColor()}-200`}>
              {isConnected ? <Wifi className="w-3 h-3 mr-1" /> : <WifiOff className="w-3 h-3 mr-1" />}
              {getStatusText()}
            </Badge>
          </CardTitle>
          <CardDescription>测试版本管理系统的 Socket.IO 消息推送功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <label htmlFor="deviceId" className="block text-sm font-medium mb-1">
                设备ID
              </label>
              <input
                id="deviceId"
                type="text"
                value={deviceId.deviceId}
                onChange={(e) => setDeviceId({ ...deviceId, deviceId: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入设备ID"
                disabled={isConnected}
              />
            </div>
          </div>

          <div className="flex gap-2">
            {!isConnected ? (
              <Button onClick={connectSocket} disabled={isConnecting || !deviceId} className="flex items-center gap-2">
                <Wifi className="w-4 h-4" />
                {isConnecting ? '连接中...' : '连接 Socket'}
              </Button>
            ) : (
              <Button onClick={disconnectSocket} variant="destructive" className="flex items-center gap-2">
                <WifiOff className="w-4 h-4" />
                断开连接
              </Button>
            )}

            {isConnected && registrationStatus === 'failed' && (
              <Button onClick={registerDevice} variant="outline" className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4" />
                重新注册
              </Button>
            )}

            {isConnected && (
              <>
                <Button
                  onClick={() => runAckTest(10)}
                  disabled={isTesting}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  {isTesting ? '测试中...' : 'ACK测试 (10次)'}
                </Button>
                <Button
                  onClick={() => runAckTest(50)}
                  disabled={isTesting}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  {isTesting ? '测试中...' : 'ACK压力测试 (50次)'}
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>消息日志</CardTitle>
              <CardDescription>实时显示接收到的版本更新消息 ({messages.length} 条)</CardDescription>
            </div>
            <Button
              onClick={clearMessages}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              disabled={messages.length === 0}
            >
              <Trash2 className="w-4 h-4" />
              清空
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] w-full">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">暂无消息，请连接 Socket 并等待版本更新通知</div>
            ) : (
              <div className="space-y-4">
                {messages.map((msg, index) => (
                  <div key={msg.id}>
                    <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="default">{msg.type}</Badge>
                          <code className="text-sm bg-gray-200 px-2 py-1 rounded">{msg.event}</code>
                        </div>
                        <span className="text-sm text-gray-500">{msg.receivedAt}</span>
                      </div>

                      <div className="space-y-1 text-sm">
                        <div>
                          <strong>版本:</strong> {msg.message.version}
                        </div>
                        <div>
                          <strong>强制更新:</strong>
                          <Badge variant={msg.message.forceUpdate ? 'destructive' : 'secondary'} className="ml-2">
                            {msg.message.forceUpdate ? '是' : '否'}
                          </Badge>
                        </div>
                        {msg.message.description && (
                          <div>
                            <strong>描述:</strong> {msg.message.description}
                          </div>
                        )}
                        <div>
                          <strong>下载地址:</strong>
                          <a
                            href={msg.message.downloadUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline ml-1"
                          >
                            {msg.message.downloadUrl}
                          </a>
                        </div>
                      </div>
                    </div>
                    {index < messages.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>ACK 回复测试结果</CardTitle>
              <CardDescription>
                测试多实例下 Socket.IO ACK 回复的可靠性 ({ackTestResults.length} 条记录)
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={clearAckResults}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={ackTestResults.length === 0}
              >
                <Trash2 className="w-4 h-4" />
                清空
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {ackTestResults.length > 0 && (
            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2">测试统计</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">总测试数:</span>
                  <span className="font-medium ml-2">{ackTestResults.length}</span>
                </div>
                <div>
                  <span className="text-gray-600">成功数:</span>
                  <span className="font-medium ml-2 text-green-600">
                    {ackTestResults.filter((r) => r.success).length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">失败数:</span>
                  <span className="font-medium ml-2 text-red-600">
                    {ackTestResults.filter((r) => !r.success).length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">成功率:</span>
                  <span className="font-medium ml-2">
                    {((ackTestResults.filter((r) => r.success).length / ackTestResults.length) * 100).toFixed(1)}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">平均响应时间:</span>
                  <span className="font-medium ml-2">
                    {(ackTestResults.reduce((sum, r) => sum + r.responseTime, 0) / ackTestResults.length).toFixed(0)}ms
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">最快响应:</span>
                  <span className="font-medium ml-2">{Math.min(...ackTestResults.map((r) => r.responseTime))}ms</span>
                </div>
                <div>
                  <span className="text-gray-600">最慢响应:</span>
                  <span className="font-medium ml-2">{Math.max(...ackTestResults.map((r) => r.responseTime))}ms</span>
                </div>
              </div>
            </div>
          )}

          <ScrollArea className="h-[400px] w-full">
            {ackTestResults.length === 0 ? (
              <div className="text-center text-gray-500 py-8">暂无测试结果，请点击 ACK 测试按钮开始测试</div>
            ) : (
              <div className="space-y-2">
                {ackTestResults.map((result) => (
                  <div
                    key={result.id}
                    className={`p-3 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant={result.success ? 'default' : 'destructive'}>
                          {result.success ? '成功' : '失败'}
                        </Badge>
                        <span className="text-sm font-mono">{result.deviceId}</span>
                      </div>
                      <div className="text-sm text-gray-500">{result.timestamp}</div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">响应时间:</span>
                        <span
                          className={`ml-2 font-medium ${result.responseTime > 1000 ? 'text-orange-600' : 'text-green-600'}`}
                        >
                          {result.responseTime}ms
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Socket ID:</span>
                        <span className="ml-2 font-mono text-xs">{result.socketId}</span>
                      </div>
                      {result.error && (
                        <div className="col-span-full">
                          <span className="text-gray-600">错误信息:</span>
                          <span className="ml-2 text-red-600">{result.error}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
