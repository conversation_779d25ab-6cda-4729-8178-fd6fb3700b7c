import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '@coozf/ui/components/button'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/versions/table-search'
import { CreateVersionDialog } from '@/components/versions/create-version-dialog'
import { DeleteConfirmDialog } from '@/components/versions/delete-confirm-dialog'
import { getTableColumns } from '@/components/versions/columns'
import { Plus } from 'lucide-react'
import type { VersionType } from '@coozf/zod'

type VersionItem = RouterOutput['version']['list']['data'][number]

type VersionSearchInput = {
  search?: string
  type?: VersionType
  platform?: 'WIN' | 'MAC'
}

type Search = {
  search?: string
  type?: string
  platform?: string
}

export const Route = createFileRoute('/_authenticated/versions')({
  component: VersionsPage,
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      search: search?.search as string,
      type: search?.type as string,
      platform: search?.platform as string,
    }
  },
})

function VersionsPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { search, type, platform } = useSearch({ strict: false })

  const [filter, setFilter] = useState<VersionSearchInput>({
    search,
    type: type as VersionType | undefined,
    platform: platform as 'WIN' | 'MAC' | undefined,
  })

  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [deleteVersionId, setDeleteVersionId] = useState<string | null>(null)

  // 获取版本列表
  const {
    data: versionsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.version.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...filter,
    }),
  )

  // 创建版本
  const createVersionMutation = useMutation(
    trpc.version.create.mutationOptions({
      onSuccess: () => {
        refetch()
        setCreateDialogOpen(false)
        toast.success('版本创建成功')
      },
      onError: (error) => {
        toast.error(`创建失败: ${error.message}`)
      },
    }),
  )

  // 删除版本
  const deleteVersionMutation = useMutation(
    trpc.version.delete.mutationOptions({
      onSuccess: () => {
        refetch()
        setDeleteVersionId(null)
        toast.success('版本删除成功')
      },
      onError: (error) => {
        toast.error(`删除失败: ${error.message}`)
      },
    }),
  )

  const publishVersionMutation = useMutation(
    trpc.version.publish.mutationOptions({
      onSuccess: () => {
        refetch()
        toast.success('版本发布成功')
      },
      onError: (error) => {
        toast.error(`发布失败: ${error.message}`)
      },
    }),
  )

  const versions = versionsData?.data || []

  const handleEditVersion = (_version: VersionItem) => {
    // TODO: 实现编辑功能
    toast.info('编辑功能开发中...')
  }

  const handleDeleteVersion = (versionId: string) => {
    setDeleteVersionId(versionId)
  }

  const confirmDeleteVersion = () => {
    if (deleteVersionId) {
      deleteVersionMutation.mutate({ id: deleteVersionId })
    }
  }

  const handlePublishVersion = (versionId: string) => {
    publishVersionMutation.mutate({ id: versionId })
  }

  const columns = useMemo(() => getTableColumns(handleEditVersion, handleDeleteVersion, handlePublishVersion), [])

  return (
    <div className="flex flex-1 flex-col gap-6 overflow-hidden">
      {/* 搜索组件 */}
      <div className="flex items-center justify-between">
        <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 })
            setFilter(value)
          }}
        />
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="size-4" />
          发布版本
        </Button>
      </div>

      {/* 版本表格 */}
      <DataTable
        columns={columns}
        data={versions}
        rowCount={versionsData?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />

      {/* 创建版本弹窗 */}
      <CreateVersionDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={(data) => createVersionMutation.mutate(data)}
        isPending={createVersionMutation.isPending}
      />

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={!!deleteVersionId}
        onOpenChange={() => setDeleteVersionId(null)}
        onConfirm={confirmDeleteVersion}
        isPending={deleteVersionMutation.isPending}
      />
    </div>
  )
}
