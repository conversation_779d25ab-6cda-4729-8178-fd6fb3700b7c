// 重构后的lib目录统一导出

// 业务逻辑层
export { ApplicationService, formatTraffic } from './business/application'
export { AuthService, type SafeUser } from './business/auth'
export { OrderService } from './business/order'
export { QuotaService } from './business/quota'
export { PlatformCreditsService } from './business/platform-credits'
export { VersionService } from './business/version'
export { MediaAccountServies } from './business/media-account'
export { PublishService } from './business/publish'
export { SystemConfigService } from './business/system-config'

// 服务层
export { SmsService, smsService } from './services/sms'
export {
  OAuthService,
  SUPPORTED_PLATFORMS,
  type PlatformType,
  type OAuthTokenResponse,
  type UserInfoResponse,
} from './services/oauth'
export { CacheService, cacheService } from './services/cache'
export { WebhookService } from './services/webhook'
export { SessionService, TokenService, sessionService, tokenService, type SessionTokenData } from './services/session'
export { LoggerService, createLoggerService, logger, getGlobalLogger, setGlobalLogger, type LogContext, type LogLevel, type LoggerConfig } from './services/logger'
export { MessagePublisher, messagePublisher, type UpdateMessage } from './services/message-publisher'
export { MessageSubscriber, messageSubscriber } from './services/message-subscriber'

// 工具层
export {
  generateSecret,
  hashSecret,
  verifySecret,
  formatSecretForDisplay,
  decryptXiaohongshuData,
  decryptKuaishouData,
} from './utils/crypto'
export { generateJWT, verifyJWT, createTokenPayload, type AccessTokenPayload } from './utils/token'
export { ResponseWrapper, ApiError, type ApiResponse } from './utils/response'
export { VideoUtils } from './utils/video'

// 类型定义
export * from './types'

// 向后兼容的导出
// export default from './services/cache'
