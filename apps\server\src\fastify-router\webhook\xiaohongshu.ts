import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { decryptXiaohongshuData, WebhookService, logger, MediaAccountServies } from '@/lib'
import { db } from '@coozf/db'
import { env } from '@/env'
import type {
  XiaohongshuMessageWebhookBody,
  XiaohongshuPushLeadWebhookBody,
  XiaohongshuCommentWebhookBody,
  XiaohongshuBindAccountDecryptedData,
  XiaohongshuUnbindAccountDecryptedData,
  XiaohongshuBindUserDecryptedData,
  XiaohongshuWebhookBaseBody,
} from './types/xiaohongshu'

// 转发给应用的标准数据格式
interface ForwardedWebhookData {
  platform: string
  event: string
  original_data: unknown
  user_info: {
    platform_user_id?: string
    user_id?: string
    account_id?: string
  }
  timestamp: number
  [key: string]: unknown
}

// Webhook事件类型配置
interface WebhookEventConfig {
  eventType: string
  logIcon: string
  description: string
}

// 支持的webhook事件类型
const WEBHOOK_EVENTS: Record<string, WebhookEventConfig> = {
  bind_account: {
    eventType: 'bind_account',
    logIcon: '🔗',
    description: '账号绑定',
  },
  unbind_account: {
    eventType: 'unbind_account',
    logIcon: '🔓',
    description: '账号解绑',
  },
  bind_user: {
    eventType: 'bind_user',
    logIcon: '👤',
    description: '用户绑定',
  },
  message: {
    eventType: 'message',
    logIcon: '💬',
    description: '消息接收',
  },
  push_lead: {
    eventType: 'push_lead',
    logIcon: '📊',
    description: '留资推送',
  },
  comment: {
    eventType: 'comment',
    logIcon: '💬',
    description: '意向评论',
  },
}

const PLATFORM_CODE = 'xiaohongshu'

const PLATFORM_DB_CODE = 'xiaohongshuopen'

/**
 * 查找关联的应用
 */
async function findApplicationByUserId(platformUserId: string) {
  try {
    const authAccount = await db.authAccount.findFirst({
      where: {
        platformCode: PLATFORM_DB_CODE,
        platformUserId,
      },
      include: {
        application: true,
      },
    })

    // 使用显式的null检查来避免TypeScript警告
    if (!authAccount) {
      return null
    }

    return {
      authAccount,
      application: authAccount.application,
    }
  } catch (error) {
    logger.error('查找小红书关联应用失败', {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      platformUserId,
      error: error instanceof Error ? error.message : error,
    })
    return null
  }
}

/**
 * 解密和解析小红书数据
 */
function decryptAndParseData(body: XiaohongshuWebhookBaseBody): unknown {
  if (body.content) {
    try {
      // 使用环境变量中的密钥解密
      const decryptedJson = decryptXiaohongshuData(body.content, env.XIAOHONGSHU_SECRET)
      return JSON.parse(decryptedJson)
    } catch (error) {
      logger.error('小红书数据解密失败', {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        error: error instanceof Error ? error.message : error,
      })
      throw new Error('数据解密失败')
    }
  }
  return body
}

/**
 * 转发webhook到应用
 */
async function forwardWebhookToApplication(
  webhookUrl: string,
  webhookSecret: string,
  data: ForwardedWebhookData,
): Promise<boolean> {
  try {
    // 发送通知
    return await WebhookService.sendNotification(
      webhookUrl,
      webhookSecret,
      PLATFORM_CODE,
      data.original_data,
      data.event,
    )
  } catch (error) {
    logger.error('转发webhook到应用失败', {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      webhookUrl,
      eventType: data.event,
      error: error instanceof Error ? error.message : error,
    })
    return false
  }
}

/**
 * 处理需要解密的 webhook 事件（bind_account, unbind_account, bind_user）
 */
async function handleEncryptedWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  logger.info(`收到小红书${eventConfig.description}webhook`, {
    type: 'webhook',
    platform: PLATFORM_DB_CODE,
    event: eventConfig.eventType,
    requestId: request.id,
    body: request.body,
  })

  try {
    const body = request.body as XiaohongshuWebhookBaseBody
    const decryptedData = decryptAndParseData(body)

    let platformUserId = ''
    let parentPlatformUserId = ''

    // 根据解密后的数据获取用户ID
    if (eventConfig.eventType === 'bind_account') {
      const data = decryptedData as XiaohongshuBindAccountDecryptedData
      platformUserId = data.user_id
    } else if (eventConfig.eventType === 'unbind_account') {
      const data = decryptedData as XiaohongshuUnbindAccountDecryptedData
      platformUserId = data.user_id
    } else if (eventConfig.eventType === 'bind_user') {
      const data = decryptedData as XiaohongshuBindUserDecryptedData
      platformUserId = data.kos_user_id
      parentPlatformUserId = data.user_id
    }

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.application.webhookUrl) {
      logger.warn('未找到小红书用户关联的应用或webhook地址', {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        platformUserId,
        requestId: request.id,
      })
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 如果是绑定kos用户，则添加账号
    let accountId: string | undefined
    if (eventConfig.eventType === 'bind_account') {
      if (platformUserId) {
        await db.authAccount.updateMany({
          where: {
            platformCode: PLATFORM_DB_CODE,
            OR: [
              {
                platformUserId,
              },
              {
                parentPlatformUserId: platformUserId,
              },
            ],
          },
          data: {
            binded: true,
          },
        })

        logger.info('小红书账号绑定成功', {
          type: 'webhook',
          platform: PLATFORM_DB_CODE,
          platformUserId,
        })
      }
    }

    if (eventConfig.eventType === 'unbind_account') {
      if (platformUserId) {
        await db.authAccount.updateMany({
          where: {
            platformCode: PLATFORM_DB_CODE,
            OR: [
              {
                platformUserId,
              },
              {
                parentPlatformUserId: platformUserId,
              },
            ],
          },
          data: {
            binded: false,
          },
        })

        logger.info('小红书账号解绑成功', {
          type: 'webhook',
          platform: PLATFORM_DB_CODE,
          platformUserId,
        })
      }
    }

    if (eventConfig.eventType === 'bind_user') {
      const { auth_status: authStatus, kos_nick_name: name, kos_avatar_img: avatar } = decryptedData as {
        kos_user_id: string
        auth_status?: string
        kos_nick_name?: string
        kos_avatar_img?: string
      }

      const parentPlatformAccount = await db.authAccount.findFirst({
        where: {
          platformCode: PLATFORM_DB_CODE,
          platformUserId: parentPlatformUserId,
        },
      })
      
      if (!parentPlatformAccount) {
        logger.info('小红书子账号绑定失败，未找到父账号', {
          type: 'webhook',
          platform: PLATFORM_DB_CODE,
          parentPlatformUserId,
        })
        return reply.send({ success: true, message: '小红书子账号绑定失败，未找到父账号' })
      }

      const kosAccount = await db.authAccount.findFirst({
        where: {
          platformCode: PLATFORM_DB_CODE,
          platformUserId,
        },
      })

      accountId = kosAccount?.id

      if (parentPlatformAccount && platformUserId && authStatus === '2') {
        // 子账号绑定
        const authAccount = {
          platformCode: PLATFORM_DB_CODE,
          platformUserId: platformUserId || '',
          platformUserName: name || '',
          platformAvatar: avatar || '',
          platformTokenExpiresAt: parentPlatformAccount.platformTokenExpiresAt || new Date(),
          platformRefreshTokenExpiresAt: parentPlatformAccount.platformRefreshTokenExpiresAt || new Date(),
          platformCookieHash: parentPlatformAccount.platformCookieHash,
          binded: true,
          parentPlatformUserId,
        }

        if (!kosAccount) {
          const account = await MediaAccountServies.createOpenPlatformAccount(
            authAccount,
            parentPlatformAccount.applicationId,
          )

          accountId = account.id

          logger.info('小红书子账号绑定成功', {
            type: 'webhook',
            platform: PLATFORM_DB_CODE,
            platformUserId,
            parentPlatformUserId,
          })

          await WebhookService.sendNotification(
            appInfo.application.webhookUrl,
            appInfo.application.webhookSecret,
            PLATFORM_CODE,
            {
              id: account.id,
              platformCode: PLATFORM_CODE,
              applicationId: parentPlatformAccount.applicationId,
              platformUserId: account.platformUserId,
              platformUserName: account.platformUserName,
              platformUserAvatar: account.platformAvatar,
            },
            'bind_sub_account',
          )
        } else {
          await db.authAccount.update({
            where: {
              id: kosAccount.id,
            },
            data: authAccount,
          })
          logger.info('小红书子账号更新成功', {
            type: 'webhook',
            platform: PLATFORM_DB_CODE,
            platformUserId,
            parentPlatformUserId,
          })
        }
      }

      // 如果是解绑kos用户，则解绑账号
      if (parentPlatformAccount && platformUserId && authStatus === '4') {
        await db.authAccount.updateMany({
          where: {
            platformCode: PLATFORM_DB_CODE,
            OR: [
              {
                platformUserId,
              },
              {
                parentPlatformUserId: platformUserId,
              },
            ],
          },
          data: {
            binded: false,
          },
        })
      }
    }

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: PLATFORM_CODE,
      event: eventConfig.eventType,
      original_data: decryptedData,
      user_info: {
        platform_user_id: platformUserId,
        user_id: parentPlatformUserId,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.application.webhookUrl,
      appInfo.application.webhookSecret,
      forwardData,
    )

    if (success) {
      logger.info(`成功转发小红书${eventConfig.description}webhook`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    } else {
      logger.warn(`转发小红书${eventConfig.description}webhook失败`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    }

    return reply.send({ success: true })
  } catch (error) {
    logger.error(`处理小红书${eventConfig.description}webhook失败`, {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      event: eventConfig.eventType,
      requestId: request.id,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 处理消息 webhook 事件
 */
async function handleMessageWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  logger.info(`收到小红书${eventConfig.description}webhook`, {
    type: 'webhook',
    platform: PLATFORM_DB_CODE,
    event: eventConfig.eventType,
    requestId: request.id,
    body: request.body,
  })

  try {
    const body = request.body as XiaohongshuMessageWebhookBody
    const platformUserId = body.message_source === 3 ? body.from_user_id : body.to_user_id

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.application.webhookUrl) {
      logger.warn('未找到小红书用户关联的应用或webhook地址', {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        platformUserId,
        requestId: request.id,
      })
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 解密content
    const decryptedData = decryptAndParseData(body)
    body.content = decryptedData as string

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: PLATFORM_CODE,
      event: eventConfig.eventType,
      original_data: body,
      user_info: {
        platform_user_id: platformUserId,
        user_id: body.from_user_id,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.application.webhookUrl,
      appInfo.application.webhookSecret,
      forwardData,
    )

    if (success) {
      logger.info(`成功转发小红书${eventConfig.description}webhook`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    } else {
      logger.warn(`转发小红书${eventConfig.description}webhook失败`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    }

    return reply.send({ success: true })
  } catch (error) {
    logger.error(`处理小红书${eventConfig.description}webhook失败`, {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      event: eventConfig.eventType,
      requestId: request.id,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 处理留资推送 webhook 事件
 */
async function handlePushLeadWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  logger.info(`收到小红书${eventConfig.description}webhook`, {
    type: 'webhook',
    platform: PLATFORM_DB_CODE,
    event: eventConfig.eventType,
    requestId: request.id,
    body: request.body,
  })

  try {
    const body = request.body as XiaohongshuPushLeadWebhookBody
    const platformUserId = body.kos_user_id ? body.kos_user_id : body.brand_user_id

    if (body.wechat) {
      body.wechat = decryptXiaohongshuData(body.wechat, env.XIAOHONGSHU_SECRET)
    }

    if (body.phone_num) {
      body.phone_num = decryptXiaohongshuData(body.phone_num, env.XIAOHONGSHU_SECRET)
    }

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.application.webhookUrl) {
      logger.warn('未找到小红书用户关联的应用或webhook地址', {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        platformUserId,
        requestId: request.id,
      })
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: PLATFORM_CODE,
      event: eventConfig.eventType,
      original_data: body,
      user_info: {
        platform_user_id: platformUserId,
        user_id: body.user_id,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.application.webhookUrl,
      appInfo.application.webhookSecret,
      forwardData,
    )

    if (success) {
      logger.info(`成功转发小红书${eventConfig.description}webhook`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    } else {
      logger.warn(`转发小红书${eventConfig.description}webhook失败`, {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        event: eventConfig.eventType,
        applicationId: appInfo.application.id,
        applicationName: appInfo.application.name,
        platformUserId,
        requestId: request.id,
      })
    }

    return reply.send({ success: true })
  } catch (error) {
    logger.error(`处理小红书${eventConfig.description}webhook失败`, {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      event: eventConfig.eventType,
      requestId: request.id,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 处理意向评论 webhook 事件
 */
async function handleCommentWebhook(request: FastifyRequest, reply: FastifyReply, eventConfig: WebhookEventConfig) {
  logger.info(`收到小红书${eventConfig.description}webhook`, {
    type: 'webhook',
    platform: PLATFORM_DB_CODE,
    event: eventConfig.eventType,
    requestId: request.id,
    body: request.body,
  })

  try {
    const body = request.body as XiaohongshuCommentWebhookBody

    // 从请求体中提取用户ID（笔记作者用户ID）
    const platformUserId = body.note_author_user_id

    if (!platformUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID信息',
      })
    }

    // 查找关联应用
    const appInfo = await findApplicationByUserId(platformUserId)
    if (!appInfo?.application.webhookUrl) {
      console.warn(`未找到用户 ${platformUserId} 关联的应用或webhook地址`)
      return reply.send({ success: true, message: '未找到关联应用' })
    }

    // 转发到应用
    const forwardData: ForwardedWebhookData = {
      platform: PLATFORM_CODE,
      event: eventConfig.eventType,
      original_data: body,
      user_info: {
        platform_user_id: platformUserId,
      },
      timestamp: Date.now(),
    }

    const success = await forwardWebhookToApplication(
      appInfo.application.webhookUrl,
      appInfo.application.webhookSecret,
      forwardData,
    )

    if (success) {
      console.log(`✅ 成功转发${eventConfig.description}webhook到应用: ${appInfo.application.name}`)
    } else {
      console.warn(`❌ 转发${eventConfig.description}webhook失败: ${appInfo.application.name}`)
    }

    return reply.send({ success: true })
  } catch (error) {
    console.error(`处理小红书${eventConfig.description}webhook失败:`, error)
    return reply.status(500).send({
      success: false,
      message: '内部服务器错误',
    })
  }
}

/**
 * 创建小红书webhook路由
 */
function createXiaohongshuWebhookRoute(app: FastifyInstance, path: string, eventKey: string) {
  const eventConfig = WEBHOOK_EVENTS[eventKey]
  if (!eventConfig) {
    throw new Error(`未知的webhook事件类型: ${eventKey}`)
  }

  app.post(path, async (request, reply) => {
    // 根据事件类型选择合适的处理函数
    switch (eventKey) {
      case 'bind_account':
      case 'unbind_account':
      case 'bind_user':
        return handleEncryptedWebhook(request, reply, eventConfig)
      case 'message':
        return handleMessageWebhook(request, reply, eventConfig)
      case 'push_lead':
        return handlePushLeadWebhook(request, reply, eventConfig)
      case 'comment':
        return handleCommentWebhook(request, reply, eventConfig)
      default:
        return reply.status(400).send({
          success: false,
          message: `不支持的事件类型: ${eventKey}`,
        })
    }
  })
}

/**
 * 小红书Webhook路由注册
 * bindAccount: '/open/im/third/bind_account',     // 聚光绑定
  unbindAccount: '/open/im/third/unbind_account',  // 聚光解绑
  bindUser: '/open/im/auth/bind_user/event',       // kos用户绑定
  message: '/open/im/send',                        // 消息接收
  pushLead: '/open/im/push_lead'                   // 留资和广告归隐数据推送
  pushLead: '/open/intent/comment'                 // 小红书意向评论
 */
export async function xiaohongshuWebhookRoutes(app: FastifyInstance) {
  // 注册各个webhook端点
  createXiaohongshuWebhookRoute(app, '/open/im/third/bind_account', 'bind_account')
  createXiaohongshuWebhookRoute(app, '/open/im/third/unbind_account', 'unbind_account')
  createXiaohongshuWebhookRoute(app, '/open/im/auth/bind_user/event', 'bind_user')
  createXiaohongshuWebhookRoute(app, '/open/im/send', 'message')
  createXiaohongshuWebhookRoute(app, '/open/im/push_lead', 'push_lead')
  createXiaohongshuWebhookRoute(app, '/open/intent/comment', 'comment')

  // webhook健康检查
  app.get('/health', async (_, reply) => {
    return reply.send({
      success: true,
      message: '小红书webhook服务正常',
      timestamp: Date.now(),
      platform: PLATFORM_CODE,
      supported_events: Object.keys(WEBHOOK_EVENTS),
    })
  })
}
