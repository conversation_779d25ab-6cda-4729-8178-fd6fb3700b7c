import { z } from 'zod'

// 应用状态枚举
export const ApplicationStatusEnum = z.enum(['ACTIVE', 'SUSPENDED', 'DELETED'])

// 应用相关的 Zod Schema
export const CreateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
})

export const UpdateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符').optional(),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('Webhook URL格式错误').optional(),
  status: z.enum(['ACTIVE', 'SUSPENDED']).optional(), // 不允许直接设置为 DELETED
})

export const SelectApplicationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  appId: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  secret: z.string(), // 注意：在实际使用中应该过滤掉
  status: ApplicationStatusEnum,
  webhookUrl: z.string().nullable(),
  webhookSecret: z.string(), // 注意：在实际使用中应该过滤掉
  creditsQuota: z.number(),
  creditsUsed: z.number(),
  creditsExpireDate: z.date().nullable(),
  platformCreditsConfig: z.record(z.number()).nullable(),
  trafficQuotaGB: z.number(),
  trafficUsedGB: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 应用列表查询参数
export const ApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  userId: z.string().optional(),
  status: ApplicationStatusEnum.optional(),
  search: z.string().optional(), // 搜索应用名称
})

// 管理员应用列表查询参数
export const AdminApplicationListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  userId: z.string().optional(), // 可选的用户ID过滤
})

export const ApplicationUpdateExpireDateSchema = z.object({
  expireDate: z.number().min(new Date().getTime(), { message: '过期时间必须在当前时间之后' }),
})

export const ApplicationUpdateOemStatusSchema = z.object({
  oemEnabled: z.boolean(),
})

// 配额概览Schema
export const QuotaOverviewSchema = z.object({
  credits: z.object({
    quota: z.number(),
    used: z.number(),
    available: z.number(),
    expireDate: z.date().nullable(),
    expired: z.boolean(),
  }),
  account: z.object({
    used: z.number(),
  }),
  traffic: z.object({
    quotaGB: z.number(),
    usedGB: z.number(),
    availableGB: z.number(),
    usagePercent: z.number(),
  }),
})

// 平台配置Schema
export const PlatformConfigSchema = z.object({
  platformCode: z.string(),
  platformName: z.string(),
  platformType: z.enum(['NORMAL', 'VERTICAL']),
  defaultCredits: z.number(),
})

// 应用点数配置Schema
export const ApplicationCreditsConfigSchema = z.object({
  NORMAL: z.number(),
  VERTICAL: z.number(),
})

// 更新应用点数配置Schema
export const UpdateApplicationCreditsConfigSchema = z.object({
  NORMAL: z.number().min(1, '普通平台点数必须大于0').optional(),
  VERTICAL: z.number().min(1, '垂直平台点数必须大于0').optional(),
})

// 类型推导
export type CreateApplicationInput = z.infer<typeof CreateApplicationSchema>
export type UpdateApplicationInput = z.infer<typeof UpdateApplicationSchema>
export type SelectApplicationOutput = z.infer<typeof SelectApplicationSchema>
export type ApplicationListParams = z.infer<typeof ApplicationListSchema>
export type AdminApplicationListParams = z.infer<typeof AdminApplicationListSchema>
export type ApplicationUpdateExpireDateInput = z.infer<typeof ApplicationUpdateExpireDateSchema>
export type ApplicationUpdateOemStatusInput = z.infer<typeof ApplicationUpdateOemStatusSchema>
export type QuotaOverviewOutput = z.infer<typeof QuotaOverviewSchema>
export type PlatformConfigOutput = z.infer<typeof PlatformConfigSchema>
export type ApplicationCreditsConfigOutput = z.infer<typeof ApplicationCreditsConfigSchema>
export type UpdateApplicationCreditsConfigInput = z.infer<typeof UpdateApplicationCreditsConfigSchema>
