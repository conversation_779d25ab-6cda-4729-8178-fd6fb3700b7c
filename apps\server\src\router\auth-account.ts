import { z } from 'zod'
import { router } from '@/trpc'
import { applicationProcedure } from '@/procedure'
import { AccountListQuerySchema } from '@coozf/zod'
import { MediaAccountServies, PlatformCreditsService } from '@/lib'

export const authAccountRouter = router({
  // 获取授权账号列表
  list: applicationProcedure.input(AccountListQuerySchema).query(async ({ input, ctx }) => {
    return MediaAccountServies.getMediaAccountOrders({ ...input, applicationId: ctx.applicationId })
  }),

  // 删除账号
  delete: applicationProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    // 使用事务删除账号并返还点数
    await ctx.db.$transaction(async (tx) => {
      // 先获取账号信息以计算返还的点数
      const account = await tx.authAccount.findUnique({
        where: { id: input.id, applicationId: ctx.applicationId },
        select: { platformCode: true, applicationId: true }
      })

      if (!account) {
        throw new Error('账号不存在')
      }

      // 计算返还的点数
      const returnCredits = await PlatformCreditsService.getCreditsPerAccount(account.applicationId, account.platformCode)

      // 删除账号
      await tx.authAccount.delete({
        where: { id: input.id, applicationId: ctx.applicationId },
      })

      // 返还点数
      await tx.application.update({
        where: { id: ctx.applicationId },
        data: {
          creditsUsed: {
            decrement: returnCredits,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId: ctx.applicationId,
          endpoint: '/auth-accounts',
          method: 'DELETE',
          costType: 'CREDITS',
          costAmount: -returnCredits, // 负数表示返还
          statusCode: 200,
        },
      })
    })

    return { success: true, message: '账号删除成功' }
  }),
})
