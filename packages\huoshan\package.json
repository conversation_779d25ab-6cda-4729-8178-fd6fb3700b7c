{"name": "@coozf/huoshan", "version": "1.0.0", "description": "火山引擎服务集成包", "main": "src/index.ts", "types": "src/index.ts", "author": "", "license": "ISC", "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@volcengine/openapi": "^1.30.1", "@volcengine/tos-sdk": "^2.7.4", "axios": "^1.10.0", "fastify": "catalog:", "@coozf/zod": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.3"}}