import { db, Prisma, Application } from '@coozf/db'
import type { QuotaOverview } from '../types/quota'
import { PlatformCreditsService } from './platform-credits'

/**
 * 配额业务逻辑服务
 */
export class QuotaService {
  /**
   * 获取应用配额信息
   */
  static async getApplicationQuota(applicationId: string) {
    const application = await db.application.findUnique({
      where: { id: applicationId },
      select: {
        creditsQuota: true,
        creditsUsed: true,
        creditsExpireDate: true,
        trafficQuotaGB: true,
        trafficUsedGB: true,
      },
    })

    if (!application) {
      throw new Error('应用不存在')
    }

    return {
      creditsQuota: application.creditsQuota || 0,
      creditsUsed: application.creditsUsed || 0,
      creditsExpireDate: application.creditsExpireDate,
      trafficQuotaGB: application.trafficQuotaGB || new Prisma.Decimal(0),
      trafficUsedGB: application.trafficUsedGB || new Prisma.Decimal(0),
    }
  }

  /**
   * 获取应用已使用的账号数量
   */
  static async getApplicationAccountUsage(applicationId: string): Promise<number> {
    return await db.authAccount.count({
      where: { applicationId, isDeleted: false },
    })
  }

  /**
   * 检查点数配额是否足够
   */
  static async checkCreditsQuota(applicationId: string, requiredCredits: number): Promise<boolean> {
    return await PlatformCreditsService.checkCreditsQuota(applicationId, requiredCredits)
  }

  /**
   * 检查账号是否可以添加（基于点数配额）
   */
  static async checkAccountQuota(
    applicationId: string,
    platformCode: string,
    requiredCredits?: number,
  ): Promise<boolean> {
    const includeCredits =
      requiredCredits || (await PlatformCreditsService.getCreditsPerAccount(applicationId, platformCode))
    return await this.checkCreditsQuota(applicationId, includeCredits)
  }

  /**
   * 检查流量配额是否足够
   */
  static async checkTrafficQuota(applicationId: string, requiredTrafficGB: number): Promise<boolean> {
    const quota = await this.getApplicationQuota(applicationId)
    const requiredDecimal = new Prisma.Decimal(requiredTrafficGB)
    const usedPlusRequired = quota.trafficUsedGB.add(requiredDecimal)
    return usedPlusRequired.lte(quota.trafficQuotaGB)
  }

  /**
   * 消费流量配额
   */
  static async consumeTrafficQuota(applicationId: string, trafficGB: number, endpoint: string): Promise<boolean> {
    const trafficDecimal = new Prisma.Decimal(trafficGB)

    // 检查配额是否足够
    const canConsume = await this.checkTrafficQuota(applicationId, trafficGB)
    if (!canConsume) {
      return false
    }

    // 使用事务更新流量使用量并记录API调用
    await db.$transaction(async (tx) => {
      // 更新应用流量使用量
      await tx.application.update({
        where: { id: applicationId },
        data: {
          trafficUsedGB: {
            increment: trafficDecimal,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId,
          endpoint,
          method: 'POST',
          costType: 'TRAFFIC',
          costAmount: trafficDecimal,
          statusCode: 200,
        },
      })
    })

    return true
  }

  /**
   * 增加点数配额
   */
  static async addCreditsQuota(applicationId: string, additionalCredits: number): Promise<void> {
    await db.application.update({
      where: { id: applicationId },
      data: {
        creditsQuota: {
          increment: additionalCredits,
        },
      },
    })
  }

  /**
   * 增加流量配额
   */
  static async addTrafficQuota(applicationId: string, additionalQuotaGB: number): Promise<void> {
    const quotaDecimal = new Prisma.Decimal(additionalQuotaGB)
    await db.application.update({
      where: { id: applicationId },
      data: {
        trafficQuotaGB: {
          increment: quotaDecimal,
        },
      },
    })
  }

  /**
   * 获取配额使用概览
   */
  static async getQuotaOverview(applicationId: string): Promise<QuotaOverview> {
    const quota = await this.getApplicationQuota(applicationId)
    const accountUsage = await this.getApplicationAccountUsage(applicationId)

    const trafficQuotaNumber = quota.trafficQuotaGB.toNumber()
    const trafficUsedNumber = quota.trafficUsedGB.toNumber()
    const trafficAvailable = quota.trafficQuotaGB.sub(quota.trafficUsedGB).toNumber()

    return {
      credits: {
        quota: quota.creditsQuota,
        used: quota.creditsUsed,
        available: quota.creditsQuota - quota.creditsUsed,
        expireDate: quota.creditsExpireDate,
        expired: quota.creditsExpireDate ? new Date() > quota.creditsExpireDate : false,
      },
      account: {
        used: accountUsage,
      },
      traffic: {
        quotaGB: trafficQuotaNumber,
        usedGB: trafficUsedNumber,
        availableGB: trafficAvailable,
        usagePercent: trafficQuotaNumber > 0 ? (trafficUsedNumber / trafficQuotaNumber) * 100 : 0,
      },
    }
  }

  /**
   * 判断应用是否有足够的点数配额
   */
  static async hasSufficientCreditsQuota(applicationId: string, requiredCredits: number): Promise<boolean> {
    return await this.checkCreditsQuota(applicationId, requiredCredits)
  }

  /**
   * 消费点数配额
   */
  static async consumeCredits(applicationId: string, credits: number, endpoint: string): Promise<boolean> {
    return await PlatformCreditsService.consumeCredits(applicationId, credits, endpoint)
  }
}
