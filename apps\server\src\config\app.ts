import Fastify from 'fastify'
import { loggerConfig } from './logger'
import { createLoggerService, setGlobalLogger } from '../lib/services/logger'
import { isDev } from '@/env'

export const createApp = () => {
  const app = Fastify({
    logger: isDev ? loggerConfig : false,
    allowErrorHandlerOverride: false,
  })

  // 初始化 LoggerService，根据环境变量配置
  const loggerService = createLoggerService({
    enableTls: true,
    enableConsole: true,
    minLevel: 'info',
  })

  // 设置 pino logger 实例
  loggerService.setPinoLogger(app.log)

  // 设置全局日志服务实例
  setGlobalLogger(loggerService)

  // 将 loggerService 挂载到 app 上，便于在路由中使用
  app.decorate('loggerService', loggerService)

  return app
}
