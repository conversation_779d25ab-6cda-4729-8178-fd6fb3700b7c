import type { FastifyInstance } from 'fastify'
import { oauth2Routes } from './oauth2'
import webhookRoutes from './webhook'
import { crawlerProxyRoutes } from './crawler-porxy'
import { xiaohongshuProxyRoutes } from './xiaohongshu-porxy'

const routes = (app: FastifyInstance) => {
  app.register(oauth2Routes, { prefix: '/oauth2' })
  app.register(webhookRoutes, { prefix: '/webhook' })
  app.register(crawlerProxyRoutes, { prefix: '/web' })
  app.register(xiaohongshuProxyRoutes, { prefix: '/xiaohongshu' })
}

export default routes
