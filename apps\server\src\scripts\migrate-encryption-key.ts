/**
 * 平台Cookie加密密钥迁移脚本
 *
 * 使用方式:
 * npx tsx src/scripts/migrate-encryption-key.ts --old-key="旧密钥" --new-key="新密钥" [--batch-size=100] [--dry-run]
 *
 * 参数说明:
 * --old-key: 旧的加密密钥
 * --new-key: 新的加密密钥
 * --batch-size: 每批处理的记录数，默认100
 * --dry-run: 只检查，不实际更新数据
 */

import crypto from 'crypto'
import { PrismaClient } from '@prisma/client'

// 支持新旧密钥的加密服务类
class MigrationCookieService {
  /**
   * 使用指定密钥加密cookie字符串
   */
  static async encryptWithKey(cookieString: string, userProvidedKey: string): Promise<string> {
    // 生成随机的数据加密密钥（256位）
    const dataEncryptionKey = crypto.randomBytes(32)

    // 生成初始化向量（128位）
    const iv = crypto.randomBytes(16)

    // 第一层：使用 DEK 加密实际的 cookie 数据
    const cipher = crypto.createCipheriv('aes-256-gcm', dataEncryptionKey, iv)

    // 加密数据，注意这里我们明确指定了输入输出编码
    let encrypted = cipher.update(cookieString, 'utf8', 'hex')
    encrypted += cipher.final('hex')

    // 获取 GCM 模式的认证标签
    const authTag = cipher.getAuthTag()

    // 第二层：保护数据加密密钥
    // 从用户密钥派生密钥加密密钥（KEK）
    const keyEncryptionKey = crypto.createHash('sha256').update(userProvidedKey).digest() // 返回 Buffer，正好是 32 字节（256位）

    // 使用 ECB 模式加密 DEK
    // 注意：ECB 模式不需要 IV，所以第三个参数是 null
    const keyCipher = crypto.createCipheriv('aes-256-ecb', keyEncryptionKey, null)

    // 修正：正确处理 Buffer 类型的加密
    const encryptedDEK = Buffer.concat([keyCipher.update(dataEncryptionKey), keyCipher.final()]).toString('hex')

    // 组合所有组件
    const combined = {
      version: 1, // 版本号，便于未来升级
      encryptedData: encrypted,
      encryptedKey: encryptedDEK,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex'),
    }

    // 最终编码为 Base64 返回
    return Buffer.from(JSON.stringify(combined)).toString('base64')
  }

  /**
   * 使用指定密钥解密cookie字符串
   */
  static decryptWithKey(encryptedData: string, userProvidedKey: string): string {
    // 解析存储的数据
    const combined = JSON.parse(Buffer.from(encryptedData, 'base64').toString())

    // 快速生成密钥加密密钥（只需要一次哈希）
    const keyEncryptionKey = crypto.createHash('sha256').update(userProvidedKey).digest()

    // 解密数据加密密钥
    const keyDecipher = crypto.createDecipheriv('aes-256-ecb', keyEncryptionKey, null)

    // 从十六进制字符串恢复加密的 DEK
    const encryptedDEKBuffer = Buffer.from(combined.encryptedKey, 'hex')

    // 解密得到原始的 DEK
    const dataEncryptionKey = Buffer.concat([keyDecipher.update(encryptedDEKBuffer), keyDecipher.final()])

    // 恢复 IV 和认证标签
    const iv = Buffer.from(combined.iv, 'hex')
    const authTag = Buffer.from(combined.authTag, 'hex')

    // 使用 DEK 解密实际数据
    const decipher = crypto.createDecipheriv('aes-256-gcm', dataEncryptionKey, iv)
    decipher.setAuthTag(authTag)

    // 解密并返回原始字符串
    let decrypted = decipher.update(combined.encryptedData, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }
}

// 迁移统计信息
interface MigrationStats {
  totalRecords: number
  processedRecords: number
  successRecords: number
  failedRecords: number
  skippedRecords: number
  errors: Array<{
    id: string
    field: string
    error: string
  }>
}

// 迁移选项
interface MigrationOptions {
  oldKey: string
  newKey: string
  batchSize: number
  dryRun: boolean
}

class EncryptionKeyMigrator {
  private prisma: PrismaClient
  private stats: MigrationStats = {
    totalRecords: 0,
    processedRecords: 0,
    successRecords: 0,
    failedRecords: 0,
    skippedRecords: 0,
    errors: [],
  }

  constructor() {
    this.prisma = new PrismaClient()
  }

  /**
   * 开始迁移过程
   */
  async migrate(options: MigrationOptions): Promise<void> {
    const { oldKey, newKey, batchSize, dryRun } = options

    console.log('🚀 开始加密密钥迁移...')
    console.log(`📊 迁移模式: ${dryRun ? '试运行（不会修改数据）' : '正式迁移'}`)
    console.log(`📦 批处理大小: ${batchSize}`)
    console.log('='.repeat(50))

    try {
      // 统计总记录数
      await this.countTotalRecords()

      if (this.stats.totalRecords === 0) {
        console.log('✅ 没有找到需要迁移的记录')
        return
      }

      console.log(`📋 总共需要处理 ${this.stats.totalRecords} 条记录`)

      // 分批处理记录
      let offset = 0
      let hasMore = true

      while (hasMore) {
        const records = await this.fetchBatch(offset, batchSize)

        if (records.length === 0) {
          hasMore = false
          break
        }

        console.log(`\n🔄 处理第 ${offset + 1}-${offset + records.length} 条记录...`)

        for (const record of records) {
          await this.processRecord(record, oldKey, newKey, dryRun)
        }

        offset += batchSize

        // 显示进度
        const progress = Math.round((this.stats.processedRecords / this.stats.totalRecords) * 100)
        console.log(`📈 进度: ${this.stats.processedRecords}/${this.stats.totalRecords} (${progress}%)`)
      }

      // 输出最终统计
      this.printFinalStats(dryRun)
    } catch (error) {
      console.error('❌ 迁移过程中发生错误:', error)
      throw error
    } finally {
      await this.prisma.$disconnect()
    }
  }

  /**
   * 统计需要迁移的总记录数
   */
  private async countTotalRecords(): Promise<void> {
    this.stats.totalRecords = await this.prisma.authAccount.count({
      where: {
        isDeleted: false,
        OR: [{ platformCookieHash: { not: '' } }, { platformLocalStorageHash: { not: '' } }],
      },
    })
  }

  /**
   * 获取一批记录
   */
  private async fetchBatch(offset: number, batchSize: number) {
    return await this.prisma.authAccount.findMany({
      where: {
        isDeleted: false,
        OR: [{ platformCookieHash: { not: '' } }, { platformLocalStorageHash: { not: '' } }],
      },
      select: {
        id: true,
        platformCookieHash: true,
        platformLocalStorageHash: true,
      },
      skip: offset,
      take: batchSize,
      orderBy: { id: 'asc' },
    })
  }

  /**
   * 处理单条记录
   */
  private async processRecord(
    record: { id: string; platformCookieHash: string; platformLocalStorageHash: string | null },
    oldKey: string,
    newKey: string,
    dryRun: boolean,
  ): Promise<void> {
    this.stats.processedRecords++

    try {
      const updates: { platformCookieHash?: string; platformLocalStorageHash?: string } = {}

      // 处理 platformCookieHash（必须字段）
      if (record.platformCookieHash) {
        try {
          const decryptedCookieData = MigrationCookieService.decryptWithKey(record.platformCookieHash, oldKey)
          const newEncryptedCookieData = await MigrationCookieService.encryptWithKey(decryptedCookieData, newKey)
          updates.platformCookieHash = newEncryptedCookieData
        } catch (error) {
          this.stats.errors.push({
            id: record.id,
            field: 'platformCookieHash',
            error: error instanceof Error ? error.message : '解密失败',
          })
        }
      }

      // 处理 platformLocalStorageHash（可选字段）
      if (record.platformLocalStorageHash) {
        try {
          const decryptedLocalStorageData = MigrationCookieService.decryptWithKey(
            record.platformLocalStorageHash,
            oldKey,
          )
          const newEncryptedLocalStorageData = await MigrationCookieService.encryptWithKey(
            decryptedLocalStorageData,
            newKey,
          )
          updates.platformLocalStorageHash = newEncryptedLocalStorageData
        } catch (error) {
          this.stats.errors.push({
            id: record.id,
            field: 'platformLocalStorageHash',
            error: error instanceof Error ? error.message : '解密失败',
          })
        }
      }

      // 检查是否有成功的更新
      if (Object.keys(updates).length === 0) {
        this.stats.failedRecords++
        return
      }

      // 如果是试运行模式，不实际更新数据库
      if (dryRun) {
        console.log(`  ✓ [试运行] ID: ${record.id} - 将更新字段: ${Object.keys(updates).join(', ')}`)
        this.stats.successRecords++
        return
      }

      // 更新数据库记录
      await this.prisma.authAccount.update({
        where: { id: record.id },
        data: updates,
      })

      this.stats.successRecords++

      // 验证更新结果
      await this.verifyRecord(record.id, updates, newKey)
    } catch (error) {
      this.stats.failedRecords++
      this.stats.errors.push({
        id: record.id,
        field: 'general',
        error: error instanceof Error ? error.message : '处理失败',
      })
      console.error(`  ❌ ID: ${record.id} - 处理失败:`, error instanceof Error ? error.message : error)
    }
  }

  /**
   * 验证记录更新结果
   */
  private async verifyRecord(
    id: string,
    updates: { platformCookieHash?: string; platformLocalStorageHash?: string },
    newKey: string,
  ): Promise<void> {
    try {
      const updatedRecord = await this.prisma.authAccount.findUnique({
        where: { id },
        select: {
          platformCookieHash: true,
        },
      })

      if (!updatedRecord) {
        throw new Error('验证失败：记录不存在')
      }

      // 验证能否用新密钥正确解密
      if (updates.platformCookieHash && updatedRecord.platformCookieHash) {
        MigrationCookieService.decryptWithKey(updatedRecord.platformCookieHash, newKey)
      }

      console.log(`  ✅ ID: ${id} - 迁移并验证成功`)
    } catch (error) {
      console.warn(`  ⚠️  ID: ${id} - 验证失败:`, error instanceof Error ? error.message : error)
    }
  }

  /**
   * 打印最终统计信息
   */
  private printFinalStats(dryRun: boolean): void {
    console.log('\n' + '='.repeat(50))
    console.log('📊 迁移完成统计:')
    console.log(`总记录数: ${this.stats.totalRecords}`)
    console.log(`已处理: ${this.stats.processedRecords}`)
    console.log(`成功: ${this.stats.successRecords}`)
    console.log(`失败: ${this.stats.failedRecords}`)

    if (this.stats.errors.length > 0) {
      console.log('\n❌ 失败记录详情:')
      this.stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ID: ${error.id}, 字段: ${error.field}, 错误: ${error.error}`)
      })
    }

    if (dryRun) {
      console.log('\n💡 这是试运行结果，实际数据未被修改')
      console.log('请检查统计信息，确认无误后去掉 --dry-run 参数执行正式迁移')
    } else {
      if (this.stats.failedRecords === 0) {
        console.log('\n🎉 所有记录迁移成功！')
      } else {
        console.log(`\n⚠️  迁移完成，但有 ${this.stats.failedRecords} 条记录失败`)
        console.log('请检查失败记录详情并手动处理')
      }
    }
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {
    oldKey: '',
    newKey: '',
    batchSize: 100,
    dryRun: false,
  }

  for (let i = 0; i < args.length; i++) {
    const arg = args[i] ?? ''

    if (arg.startsWith('--old-key=')) {
      options.oldKey = arg.split('=')[1] || ''
    } else if (arg === '--old-key' && i + 1 < args.length) {
      options.oldKey = args[++i] || ''
    } else if (arg.startsWith('--new-key=')) {
      options.newKey = arg.split('=')[1] || ''
    } else if (arg === '--new-key' && i + 1 < args.length) {
      options.newKey = args[++i] || ''
    } else if (arg.startsWith('--batch-size=')) {
      const size = parseInt(arg.split('=')[1] || '100')
      if (!isNaN(size) && size > 0) {
        options.batchSize = size
      }
    } else if (arg === '--batch-size' && i + 1 < args.length) {
      const size = parseInt(args[++i] || '100')
      if (!isNaN(size) && size > 0) {
        options.batchSize = size
      }
    } else if (arg === '--dry-run') {
      options.dryRun = true
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
使用方式:
  npx tsx src/scripts/migrate-encryption-key.ts --old-key="旧密钥" --new-key="新密钥" [选项]

参数:
  --old-key <key>      旧的加密密钥 (必需)
  --new-key <key>      新的加密密钥 (必需)
  --batch-size <size>  每批处理的记录数 (默认: 100)
  --dry-run           只检查，不实际更新数据
  --help, -h          显示帮助信息

示例:
  # 试运行
  npx tsx src/scripts/migrate-encryption-key.ts --old-key="old123" --new-key="new456" --dry-run
  
  # 正式迁移
  npx tsx src/scripts/migrate-encryption-key.ts --old-key="old123" --new-key="new456"
      `)
      process.exit(0)
    }
  }

  return options
}

// 主函数
async function main() {
  const options = parseArgs()

  // 验证参数
  if (!options.oldKey || !options.newKey) {
    console.error('❌ 错误: 必须提供旧密钥和新密钥')
    console.error('使用 --help 查看使用方法')
    process.exit(1)
  }

  if (options.oldKey === options.newKey) {
    console.error('❌ 错误: 新旧密钥不能相同')
    process.exit(1)
  }

  if (options.batchSize <= 0) {
    console.error('❌ 错误: 批处理大小必须大于0')
    process.exit(1)
  }

  // 确认迁移操作
  if (!options.dryRun) {
    console.log('⚠️  警告: 此操作将修改数据库中的加密数据')
    console.log('建议先运行 --dry-run 进行测试')
    console.log('按 Ctrl+C 取消，或等待5秒后自动继续...\n')

    await new Promise((resolve) => setTimeout(resolve, 5000))
  }

  try {
    const migrator = new EncryptionKeyMigrator()
    await migrator.migrate(options)
  } catch (error) {
    console.error('❌ 迁移失败:', error)
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 程序异常退出:', error)
    process.exit(1)
  })
}

export { MigrationCookieService, EncryptionKeyMigrator }
