'use client'
import * as React from 'react'
import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons'

import { cn } from '@coozf/ui/lib/utils'
import { Button } from '@coozf/ui/components/button'
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from '@coozf/ui/components/command'
import { Popover, PopoverContent, PopoverTrigger } from '@coozf/ui/components/popover'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { LoadingContainer } from '@coozf/ui/components/loading'
import { useControllableState } from '@coozf/ui/lib/utils'

interface SelectItemProps<T extends Record<string, any>, TData = any> {
  value?: string
  defaultValue?: string
  onChange: (value: string) => void
  queryOptions: any
  dataExtractor: (data: TData) => T[] | undefined
  labelKey: keyof T
  valueKey: keyof T
  placeholder?: string
  emptyText?: string
  className?: string
  width?: string
}

export function SelectItem<T extends Record<string, any>, TData = any>({
  value: valueProp,
  onChange,
  defaultValue,
  queryOptions,
  dataExtractor,
  labelKey,
  valueKey,
  placeholder = '请选择',
  emptyText = '暂无数据',
  className,
  width = 'w-[200px]',
}: SelectItemProps<T, TData>) {
  const [open, setOpen] = React.useState(false)
  const { data, isLoading } = useQuery(queryOptions)

  const [value, setValue] = useControllableState({
    prop: valueProp,
    defaultProp: defaultValue ?? '',
    onChange: onChange,
  })
  // @ts-expect-error
  const items = useMemo(() => dataExtractor(data), [data, dataExtractor])

  const currentItem = useMemo(() => {
    if (value && items) {
      return items.find((item) => `${item[valueKey]}` === value)
    }
    return undefined
  }, [value, items, valueKey])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(width, 'justify-between whitespace-normal', className)}
        >
          {currentItem ? (
            <span className="line-clamp-1 flex-1 text-start">{currentItem[labelKey] as string}</span>
          ) : (
            <>{placeholder}</>
          )}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn('p-0', width)}>
        <Command shouldFilter={false}>
          <CommandList>
            {isLoading ? (
              <div className="py-6">
                <LoadingContainer className="w-5 h-5" />
              </div>
            ) : (
              <CommandEmpty>{emptyText}</CommandEmpty>
            )}
            <CommandGroup>
              {items &&
                items.map((item) => (
                  <CommandItem
                    key={item[valueKey] as string}
                    value={`${item[valueKey]}`}
                    className="w-full flex items-center gap-2 justify-between"
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? '' : currentValue)
                      setOpen(false)
                    }}
                  >
                    <span>{item[labelKey] as string}</span>
                    <CheckIcon
                      className={cn('ml-auto h-4 w-4', value === `${item[valueKey]}` ? 'opacity-100' : 'opacity-0')}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
