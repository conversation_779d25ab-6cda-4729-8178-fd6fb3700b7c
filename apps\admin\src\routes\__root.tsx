import { Outlet, createRootRouteWithContext } from '@tanstack/react-router'
import type { AuthState } from '../lib/auth/auth-context'

interface MyRouterContext {
  auth: AuthState
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: RootComponent,
  notFoundComponent: () => <div>404 页面未找到</div>,
})

function RootComponent() {
  return (
    <div className="min-h-screen h-full overflow-hidden bg-background dark:bg-background">
      <Outlet />
    </div>
  )
} 