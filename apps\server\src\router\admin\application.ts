import { router } from '@/trpc'
import {
  AdminApplicationListSchema,
  ApplicationUpdateExpireDateSchema,
  ApplicationUpdateOemStatusSchema,
  PaginationSchema,
} from '@coozf/zod'
import { adminApplicationProcedure, adminProtectedProcedure } from '@/procedure'
import { ApplicationService } from '@/lib/business/application'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: adminProtectedProcedure.input(AdminApplicationListSchema.and(PaginationSchema)).query(async ({ input }) => {
    return ApplicationService.getApplicationList({ ...input, isUser: true })
  }),

  // 获取单个应用详情
  byId: adminApplicationProcedure.query(async ({ ctx }) => {
    return ctx.application
  }),

  // 获取应用统计数据
  getApplicationStats: adminApplicationProcedure.query(async ({ ctx }) => {
    return ApplicationService.getApplicationStats(ctx.applicationId)
  }),

  // 更新应用过期时间
  updateExpireDate: adminApplicationProcedure
    .input(ApplicationUpdateExpireDateSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.application.update({
        where: { id: ctx.applicationId },
        data: {
          creditsExpireDate: new Date(input.expireDate),
        },
      })
    }),

  // 切换OEM开关状态
  updateOemStatus: adminApplicationProcedure
    .input(ApplicationUpdateOemStatusSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.application.update({
        where: { id: ctx.applicationId },
        data: {
          oemEnabled: input.oemEnabled,
        },
      })
    }),
})
