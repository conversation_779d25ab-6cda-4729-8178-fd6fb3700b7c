import { createFileRoute } from '@tanstack/react-router'
import { useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { trpc } from '@/lib/trpc'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import { useMutation, useQuery } from '@tanstack/react-query'
import { LoadingButton } from '@coozf/ui/components/loading'

const profileSchema = z.object({
  name: z.string().min(1, '姓名不能为空'),
  avatar: z.string().url('请输入有效的头像地址').optional(),
})

type ProfileFormData = z.infer<typeof profileSchema>

function ProfilePage() {
  const { data: user, isLoading } = useQuery(trpc.user.me.queryOptions())
  const updateProfileMutation = useMutation(trpc.user.update.mutationOptions())

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
  })

  useEffect(() => {
    if (user) {
      reset({
        name: user.name || '',
        avatar: user.image || '',
      })
    }
  }, [user, reset])

  const onSubmit = (data: ProfileFormData) => {
    updateProfileMutation.mutate(data)
  }

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto mt-8">
        <CardHeader>
          <CardTitle>个人信息</CardTitle>
          <CardDescription>加载中...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>个人信息</CardTitle>
        <CardDescription>查看和编辑你的个人信息</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="flex items-center space-x-4">
            <Avatar className="w-20 h-20">
              <AvatarImage src={user?.image || ''} />
              <AvatarFallback>{user?.name?.[0]?.toUpperCase() || '?'}</AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="text-sm text-muted-foreground">用户ID: {user?.id}</div>
              <div className="text-sm text-muted-foreground">
                邮箱: {user?.email || '未设置'} {user?.emailVerified && '(已验证)'}
              </div>
              <div className="text-sm text-muted-foreground">
                手机: {user?.phoneNumber || '未设置'} {user?.phoneNumberVerified && '(已验证)'}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">姓名</Label>
            <Input id="name" {...register('name')} placeholder="请输入姓名" />
            {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="avatar">头像地址</Label>
            <Input id="avatar" {...register('avatar')} placeholder="请输入头像URL地址" />
            {errors.avatar && <p className="text-sm text-destructive">{errors.avatar.message}</p>}
          </div>

          <LoadingButton
            type="submit"
            isPending={updateProfileMutation.isPending}
            disabled={updateProfileMutation.isPending}
          >
            保存修改
          </LoadingButton>
        </form>
      </CardContent>
    </Card>
  )
}

export const Route = createFileRoute('/_authenticated/profile')({
  component: ProfilePage,
})
