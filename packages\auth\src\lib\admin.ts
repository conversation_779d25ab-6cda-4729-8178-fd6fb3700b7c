import { betterAuth } from "better-auth"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { db } from "@coozf/db"
import { twoFactor, username, admin } from "better-auth/plugins"
import { generateAvatarUrl } from "./utils"
import { env } from "../env"

export const auth = betterAuth({
  appName: 'coozf-open-platform-admin',
  emailAndPassword: { 
    enabled: true,
    requireEmailVerification: false,
  },
  user: {
    modelName: "admin_user",
  },
  session: {
    modelName: "admin_session",
  },
  account: {
    modelName: "admin_account",
  },
  plugins: [
    twoFactor(
    {
      issuer: `lj-${env.MODE}-admin`,
      otpOptions: {
        digits: 6,
        period: 30,
      },
    }
  ), 
  username(),
  admin({
      // 配置管理员用户ID（可选）
      adminUserIds: [], // 如果你有特定的管理员用户ID可以在这里添加
      
      // 默认角色
      defaultRole: "user",
      
      // 管理员角色
      adminRoles: ["admin"],
      
      // 模拟会话持续时间（默认1小时）
      impersonationSessionDuration: 3600,
      
      // 默认封禁原因
      defaultBanReason: "违反平台规定",
      
      // 封禁用户登录时的提示消息
      bannedUserMessage: "您的账户已被封禁，如有疑问请联系管理员",
    })],
  trustedOrigins: env.TRUSTED_ORIGINS.split(','),
  database: prismaAdapter(db, {
    provider: "mysql",
  }),
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          return {
            data: {
              ...user,
              image: generateAvatarUrl(user.name),
            }
          }
        }
      }
    },
  },
  onAPIError: {
    throw: true
  },
  disabledPaths: [
     '/sign-in/email','sign-up/username'
  ],
  advanced: {
    cookiePrefix: "admin",
  },
})