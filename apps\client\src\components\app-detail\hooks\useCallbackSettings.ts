import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'sonner'
import type { CallbackFormData } from '../types'

const callbackSchema = z.object({ 
  url: z.string().url('请输入有效的URL') 
})

export function useCallbackSettings() {
  const [callbackUrl, setCallbackUrl] = useState('')
  const [isEditingCallback, setIsEditingCallback] = useState(false)
  const [showCallbackConfirm, setShowCallbackConfirm] = useState(false)
  const [pendingCallbackUrl, setPendingCallbackUrl] = useState('')

  const callbackForm = useForm<CallbackFormData>({
    resolver: zodResolver(callbackSchema),
    defaultValues: { url: '' },
  })

  const onCallbackSubmit = (data: CallbackFormData) => {
    setPendingCallbackUrl(data.url)
    setShowCallbackConfirm(true)
  }

  const confirmCallbackSave = () => {
    setCallbackUrl(pendingCallbackUrl)
    setIsEditingCallback(false)
    setShowCallbackConfirm(false)
    callbackForm.reset()
    setPendingCallbackUrl('')
    toast.success('回调URL 保存成功')
  }

  const cancelCallbackSave = () => {
    setShowCallbackConfirm(false)
    setPendingCallbackUrl('')
  }

  const handleCancelCallback = () => {
    setIsEditingCallback(false)
    callbackForm.reset()
  }

  return {
    callbackUrl,
    setCallbackUrl,
    isEditingCallback,
    setIsEditingCallback,
    showCallbackConfirm,
    setShowCallbackConfirm,
    pendingCallbackUrl,
    callbackForm,
    onCallbackSubmit,
    confirmCallbackSave,
    cancelCallbackSave,
    handleCancelCallback,
  }
}