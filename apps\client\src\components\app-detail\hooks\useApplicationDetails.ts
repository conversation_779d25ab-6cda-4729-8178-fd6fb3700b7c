import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'

export function useApplicationDetails(applicationId: string) {
  // 获取应用详情
  const { data: application, isLoading: appLoading } = useQuery(
    trpc.application.byId.queryOptions({ applicationId })
  )

  // 获取近30日趋势数据
  const { data: trendsData, isLoading: trendsLoading } = useQuery(
    trpc.application.getApplicationTrends.queryOptions(
      { applicationId, days: 30 }, 
      { enabled: !!applicationId }
    )
  )

  return {
    application,
    trendsData,
    isLoading: appLoading,
    isTrendsLoading: trendsLoading,
  }
}