import { Job, Queue, Worker } from 'bullmq'
import { config, env } from '@/env'
import { WebhookService, logger } from '@/lib'
import { db } from '@coozf/db'
import type { VersionType } from '@prisma/client'
import axios from 'axios'
import { ApiError, OpenPlatformTokenData } from '../utils'
import { OpenPlatformAccountInfo } from './account-refresh-token'

/**
 * BullMQ 消息发布器
 * 用于在版本创建后通知 API 服务推送更新消息给客户端
 */
export class AccountRefreshTokenSubscriber {
  private worker: Worker<OpenPlatformAccountInfo> | null = null
  private douyinWorker: Worker<OpenPlatformAccountInfo> | null = null
  private douyinRefreshTokenWorker: Worker<OpenPlatformAccountInfo> | null = null
  private isStarted = false

  /**
   * 启动消息处理器
   */
  async start(): Promise<void> {
    if (this.isStarted) {
      logger.warn('小红书处理器已经启动', {
        type: 'account_refresh_token_subscriber',
        event: 'already_started',
      })
      return
    }

    try {
      // 创建 Worker 来处理队列中的任务
      this.worker = new Worker<OpenPlatformAccountInfo>(
        config.REFRESH_XIAOHONGSHU_TOKEN, // 队列名称
        async (job: Job<OpenPlatformAccountInfo>) => {
          // 处理版本更新任务
          await this.onXhsRefreshToken(job.data)
        },
        {
          connection: {
            host: config.REDIS_HOST,
            port: config.REDIS_PORT,
            password: config.REDIS_PASSWORD,
            db: config.REDIS_DB,
          },
          concurrency: 5, // 同时处理的任务数
          autorun: true, // 自动开始处理
        },
      )

      this.douyinWorker = new Worker<OpenPlatformAccountInfo>(
        config.REFRESH_DOUYIN_TOKEN, // 队列名称
        async (job: Job<OpenPlatformAccountInfo>) => {
          // 处理版本更新任务
          await this.onDouyinRefreshToken(job.data)
        },
        {
          connection: {
            host: config.REDIS_HOST,
            port: config.REDIS_PORT,
            password: config.REDIS_PASSWORD,
            db: config.REDIS_DB,
          },
          concurrency: 5, // 同时处理的任务数
          autorun: true, // 自动开始处理
        },
      )

      this.douyinRefreshTokenWorker = new Worker<OpenPlatformAccountInfo>(
        config.REFRESH_DOUYIN_REFRESH_TOKEN, // 队列名称
        async (job: Job<OpenPlatformAccountInfo>) => {
          // 处理版本更新任务
          await this.onDouyinRefreshRefreshToken(job.data)
        },
        {
          connection: {
            host: config.REDIS_HOST,
            port: config.REDIS_PORT,
            password: config.REDIS_PASSWORD,
            db: config.REDIS_DB,
          },
          concurrency: 5, // 同时处理的任务数
          autorun: true, // 自动开始处理
        },
      )

      // 设置事件监听
      this.setupWorkerEvents()

      this.isStarted = true

      logger.info('消息处理器启动成功', {
        type: 'account_refresh_token_subscriber',
        event: 'worker_started',
        workerId: this.worker.id,
      })
    } catch (error) {
      logger.error('消息处理器启动失败', {
        type: 'account_refresh_token_subscriber',
        event: 'start_failed',
        error: error instanceof Error ? error.message : error,
      })
      throw error
    }
  }

  /**
   * 设置 Worker 事件监听
   */
  private setupWorkerEvents(): void {
    if (!this.worker || !this.douyinWorker || !this.douyinRefreshTokenWorker) return

    // 任务完成事件
    this.worker.on('completed', (job: Job<OpenPlatformAccountInfo>) => {
      logger.info('版本更新任务处理完成', {
        type: 'account_refresh_token_subscriber',
        event: 'job_completed',
        jobId: job.id,
        applicationId: job.data.applicationId,
        platfromUserId: job.data.platfromUserId,
      })
    })

    // 任务失败事件
    this.worker.on('failed', (job: Job<OpenPlatformAccountInfo> | undefined, error: Error) => {
      logger.error('版本更新任务处理失败', {
        type: 'account_refresh_token_subscriber',
        event: 'job_failed',
        jobId: job?.id,
        applicationId: job?.data.applicationId,
        platfromUserId: job?.data.platfromUserId,
        error: error.message,
      })
    })

    // Worker 错误事件
    this.worker.on('error', (error: Error) => {
      logger.error('Worker 错误', {
        type: 'account_refresh_token_subscriber',
        event: 'worker_error',
        error: error.message,
      })
    })

    // 任务停滞事件
    this.worker.on('stalled', (jobId: string) => {
      logger.warn('任务停滞', {
        type: 'account_refresh_token_subscriber',
        event: 'job_stalled',
        jobId,
      })
    })
  }

  async onXhsRefreshToken(platformAccount: OpenPlatformAccountInfo) {
    try {
      logger.info('小红书 token 刷新任务开始', {
        type: 'token-refresh',
        platform: 'xiaohongshu',
        accountId: platformAccount.id,
        platfromUserId: platformAccount.platfromUserId,
        applicationId: platformAccount.applicationId,
        platformCookieHash: platformAccount.platformCookieHash,
      })
      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(platformAccount.platformCookieHash)
      } catch {
        throw new Error('Token 数据格式错误')
      }

      if (!tokenData.refresh_token) {
        throw new Error('缺少刷新令牌')
      }

      // 调用平台 API 刷新 token
      const refreshResult = await this.refreshXiaohongshuToken(tokenData.refresh_token)

      if (!refreshResult.success) {
        throw new Error(refreshResult.error || '刷新失败')
      }

      // 刷新成功，更新 token 信息
      const newTokenExpiresAt = new Date(Date.now() + refreshResult.data!.expires_in * 1000)
      const newRefreshTokenExpiresAt = new Date(Date.now() + refreshResult.data!.refresh_token_expires_in * 1000)

      // 构建新的 token 数据
      const newTokenData: OpenPlatformTokenData = {
        ...tokenData,
        access_token: refreshResult.data!.access_token,
        refresh_token: refreshResult.data!.refresh_token || tokenData.refresh_token,
        expires_in: refreshResult.data!.expires_in,
        refresh_token_expires_in: refreshResult.data!.refresh_token_expires_in,
        scope: refreshResult.data!.scope || tokenData.scope,
      }

      // 保存新的 token 数据
      const tokenJsonStr = JSON.stringify(newTokenData)

      await db.authAccount.update({
        where: { id: platformAccount.id },
        data: {
          platformCookieHash: tokenJsonStr,
          platformTokenExpiresAt: newTokenExpiresAt,
          platformRefreshTokenExpiresAt: newRefreshTokenExpiresAt,
          updatedAt: new Date(),
        },
      })

      await db.authAccount.updateMany({
        where: {
          applicationId: platformAccount.applicationId,
          parentPlatformUserId: platformAccount.platfromUserId,
        },
        data: {
          platformCookieHash: tokenJsonStr,
          platformTokenExpiresAt: newTokenExpiresAt,
          platformRefreshTokenExpiresAt: newRefreshTokenExpiresAt,
          updatedAt: new Date(),
        },
      })

      const application = await db.application.findUnique({ where: { id: platformAccount.applicationId } })

      if (!application) {
        logger.error('应用不存在', {
          type: 'token-refresh',
          platform: 'application',
          accountId: platformAccount.applicationId,
          error: '应用不存在',
          stack: undefined,
        })
        return
      }

      await WebhookService.sendNotification(
        application.webhookUrl || '',
        application.webhookSecret || '',
        'xiaohongshu',
        {
          id: platformAccount.id,
          platformCode: 'xiaohongshu',
          type: 'refresh_token',
          platformUserId: platformAccount.platfromUserId,
          applicationId: platformAccount.applicationId,
          expiresIn: refreshResult.data!.expires_in,
        },
        'refresh_token',
      )

      return {
        success: true,
        message: '刷新成功',
        newTokenExpiresAt,
      }
    } catch (error) {
      logger.error('小红书 token 刷新失败', {
        type: 'token-refresh',
        platform: 'xiaohongshu',
        accountId: platformAccount.id,
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })
    }
  }

  /**
   * 刷新小红书 token
   * 参考 qdy-service 中的实现
   */
  private async refreshXiaohongshuToken(refreshToken: string) {
    try {
      // 小红书 token 刷新 API
      const authorizeAccountRefreshApi = 'https://adapi.xiaohongshu.com/api/open/oauth2/refresh_token'

      const response = await axios.post(authorizeAccountRefreshApi, {
        app_id: env.XIAOHONGSHU_CLIENT_KEY,
        secret: env.XIAOHONGSHU_CLIENT_SECRET,
        refresh_token: refreshToken,
      })

      logger.info('小红书 token 刷新响应', {
        type: 'token-refresh',
        platform: 'xiaohongshuopen',
        success: response.data.success,
        code: response.data.code,
        msg: response.data.msg,
      })

      if (response.data.success) {
        return {
          success: true,
          data: {
            access_token: response.data.data.access_token,
            refresh_token: response.data.data.refresh_token,
            expires_in: response.data.data.access_token_expires_in,
            refresh_token_expires_in: response.data.data.refresh_token_expires_in,
            scope: response.data.data.scope,
          },
        }
      } else {
        return {
          success: false,
          error: `[小红书官方]:${response.data.msg}` || '小红书 token 刷新失败',
        }
      }
    } catch (error) {
      logger.error('小红书 token 刷新 API 调用失败', {
        type: 'token-refresh',
        platform: 'xiaohongshu',
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '小红书 API 调用失败',
      }
    }
  }

  /**
   * 刷新抖音 token
   */
  async onDouyinRefreshToken(platformAccount: OpenPlatformAccountInfo) {
    try {
      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(platformAccount.platformCookieHash)
      } catch {
        throw new Error('Token 数据格式错误')
      }

      if (!tokenData.refresh_token) {
        throw new Error('缺少刷新令牌')
      }

      // 调用平台 API 刷新 token
      const refreshResult = await this.refreshDouyinToken(tokenData.refresh_token)

      if (!refreshResult.success) {
        throw new Error(refreshResult.error || '刷新失败')
      }

      // 刷新成功，更新 token 信息
      const newTokenExpiresAt = new Date(Date.now() + refreshResult.data!.expires_in * 1000)
      const newRefreshTokenExpiresAt = new Date(Date.now() + refreshResult.data!.refresh_token_expires_in * 1000)

      // 构建新的 token 数据
      const newTokenData: OpenPlatformTokenData = {
        ...tokenData,
        access_token: refreshResult.data!.access_token,
        refresh_token: refreshResult.data!.refresh_token || tokenData.refresh_token,
        expires_in: refreshResult.data!.expires_in,
        refresh_token_expires_in: refreshResult.data!.refresh_token_expires_in,
        scope: refreshResult.data!.scope || tokenData.scope,
      }

      // 保存新的 token 数据
      const tokenJsonStr = JSON.stringify(newTokenData)

      await db.authAccount.update({
        where: { id: platformAccount.id },
        data: {
          platformCookieHash: tokenJsonStr,
          platformTokenExpiresAt: newTokenExpiresAt,
          platformRefreshTokenExpiresAt: newRefreshTokenExpiresAt,
          updatedAt: new Date(),
        },
      })

      const application = await db.application.findUnique({ where: { id: platformAccount.applicationId } })

      if (!application) {
        logger.error('应用不存在', {
          type: 'token-refresh',
          platform: 'application',
          accountId: platformAccount.applicationId,
          error: '应用不存在',
          stack: undefined,
        })
        return
      }

      await WebhookService.sendNotification(
        application.webhookUrl || '',
        application.webhookSecret || '',
        'douyin',
        {
          id: platformAccount.id,
          platformUserId: platformAccount.platfromUserId,
          platformCode: 'douyin',
          applicationId: platformAccount.applicationId,
          type: 'refresh_token',
          expiresIn: refreshResult.data!.expires_in,
        },
        'refresh_token',
      )

      return {
        success: true,
        message: '刷新成功',
        newTokenExpiresAt,
      }
    } catch (error) {
      logger.error('抖音 token 刷新失败', {
        type: 'token-refresh',
        platform: 'douyin',
        accountId: platformAccount.id,
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })
    }
  }

  private async refreshDouyinToken(refreshToken: string) {
    try {
      // 抖音 token 刷新 API
      const authorizeAccountRefreshApi = 'https://open.douyin.com/oauth/refresh_token/'

      const response = await axios.post(authorizeAccountRefreshApi, {
        client_key: env.DOUYIN_CLIENT_KEY,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      })

      logger.info('抖音 token 刷新响应', {
        type: 'token-refresh',
        platform: 'douyin',
        success: response.data.data.error_code === 0,
        code: response.data.data.error_code,
        msg: response.data.data.description,
      })

      if (response.data.data.error_code === 0) {
        return {
          success: true,
          data: {
            access_token: response.data.data.access_token,
            refresh_token: response.data.data.refresh_token,
            expires_in: response.data.data.expires_in,
            refresh_token_expires_in: response.data.data.refresh_expires_in,
            scope: response.data.data.scope,
          },
        }
      } else {
        return {
          success: false,
          error: `[抖音官方]:${response.data.data.description}` || '抖音 token 刷新失败',
        }
      }
    } catch (error) {
      logger.error('抖音 token 刷新 API 调用失败', {
        type: 'token-refresh',
        platform: 'douyin',
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '抖音 API 调用失败',
      }
    }
  }

  /**
   * 刷新抖音 refresh_token
   */
  async onDouyinRefreshRefreshToken(platformAccount: OpenPlatformAccountInfo) {
    try {
      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(platformAccount.platformCookieHash)
      } catch {
        throw new Error('Token 数据格式错误')
      }

      if (!tokenData.refresh_token) {
        throw new Error('缺少刷新令牌')
      }

      // 调用平台 API 刷新 token
      const refreshResult = await this.refreshDouyinRefreshToken(tokenData.refresh_token)

      if (!refreshResult.success) {
        throw new Error(refreshResult.error || '刷新失败')
      }

      // 刷新成功，更新 refresh_token 信息
      const newRefreshTokenExpiresAt = new Date(Date.now() + refreshResult.data!.refresh_token_expires_in * 1000)

      // 构建新的 token 数据
      const newTokenData: OpenPlatformTokenData = {
        ...tokenData,
        refresh_token: refreshResult.data!.refresh_token || tokenData.refresh_token,
        refresh_token_expires_in: refreshResult.data!.refresh_token_expires_in,
      }

      // 保存新的 token 数据
      const tokenJsonStr = JSON.stringify(newTokenData)

      await db.authAccount.update({
        where: { id: platformAccount.id },
        data: {
          platformCookieHash: tokenJsonStr,
          platformRefreshTokenExpiresAt: newRefreshTokenExpiresAt,
          updatedAt: new Date(),
        },
      })

      return {
        success: true,
        message: '刷新成功',
        newRefreshTokenExpiresAt,
      }
    } catch (error) {
      logger.error('抖音刷新令牌刷新失败', {
        type: 'token-refresh',
        platform: 'douyin',
        accountId: platformAccount.id,
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })
    }
  }

  private async refreshDouyinRefreshToken(refreshToken: string) {
    try {
      // 抖音刷新令牌刷新 API
      const authorizeAccountRefreshApi = 'https://open.douyin.com/oauth/renew_refresh_token/'

      const response = await axios.post(authorizeAccountRefreshApi, {
        client_key: env.DOUYIN_CLIENT_KEY,
        refresh_token: refreshToken,
      })

      logger.info('抖音刷新令牌刷新响应', {
        type: 'token-refresh',
        platform: 'douyin',
        success: response.data.data.error_code === 0,
        code: response.data.data.error_code,
        msg: response.data.data.description,
      })

      if (response.data.data.error_code === 0) {
        return {
          success: true,
          data: {
            refresh_token: response.data.data.refresh_token,
            refresh_token_expires_in: response.data.data.expires_in,
          },
        }
      } else {
        return {
          success: false,
          error: `[抖音官方]:${response.data.description}` || '抖音刷新令牌刷新失败',
        }
      }
    } catch (error) {
      logger.error('抖音刷新令牌刷新 API 调用失败', {
        type: 'token-refresh',
        platform: 'douyin',
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '抖音 API 调用失败',
      }
    }
  }
}

// 导出单例实例
export const accountRefreshTokenSubscriber = new AccountRefreshTokenSubscriber()
