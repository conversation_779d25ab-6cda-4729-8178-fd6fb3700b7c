// components/providers/alert-dialog-provider.tsx
import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/alert-dialog'
import { setGlobalAlertDialog } from '@/lib/alert-dialog'

interface AlertDialogOptions {
  title?: string
  description?: string | ReactNode
  content?: ReactNode
  cancelText?: string
  confirmText?: string
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  showCancel?: boolean
  showConfirm?: boolean
  confirmButtonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
}

interface AlertDialogContextType {
  show: (options: AlertDialogOptions) => Promise<boolean>
  close: () => void
}

const AlertDialogContext = createContext<AlertDialogContextType | undefined>(undefined)

function AlertDialogProvider({ children }: { children: ReactNode }) {
  const [open, setOpen] = useState(false)
  const [options, setOptions] = useState<AlertDialogOptions>({})
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null)

  const show = useCallback((newOptions: AlertDialogOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setOptions(newOptions)
      setOpen(true)
      setResolvePromise(() => resolve)
    })
  }, [])

  const close = useCallback(() => {
    setOpen(false)
    resolvePromise?.(false)
  }, [resolvePromise])

  const handleConfirm = useCallback(async () => {
    try {
      if (options.onConfirm) {
        await options.onConfirm()
      }
      setOpen(false)
      resolvePromise?.(true)
    } catch (error) {
      console.error('Error in confirm handler:', error)
    }
  }, [options, resolvePromise])

  const handleCancel = useCallback(() => {
    if (options.onCancel) {
      options.onCancel()
    }
    setOpen(false)
    resolvePromise?.(false)
  }, [options, resolvePromise])

  return (
    <AlertDialogContext.Provider value={{ show, close }}>
      {children}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            {options.title && <AlertDialogTitle>{options.title}</AlertDialogTitle>}
            {options.description && <AlertDialogDescription>{options.description}</AlertDialogDescription>}
          </AlertDialogHeader>

          {options.content && <div className="py-4">{options.content}</div>}

          <AlertDialogFooter>
            {options.showCancel !== false && (
              <AlertDialogCancel onClick={handleCancel}>{options.cancelText || 'Cancel'}</AlertDialogCancel>
            )}
            {options.showConfirm !== false && (
              <AlertDialogAction
                onClick={handleConfirm}
                className={
                  options.confirmButtonVariant === 'destructive'
                    ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
                    : ''
                }
              >
                {options.confirmText || 'Continue'}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AlertDialogContext.Provider>
  )
}

const useAlertDialog = () => {
  const context = useContext(AlertDialogContext)
  if (!context) {
    throw new Error('useAlertDialog must be used within AlertDialogProvider')
  }
  return context
}

function AlertDialogInitializer() {
  const { show, close } = useAlertDialog()

  useEffect(() => {
    setGlobalAlertDialog(show, close)
  }, [show, close])

  return null
}

// 组合的 Provider 组件 - 这是主要的修改
function GlobalAlertDialogProvider({ children }: { children: ReactNode }) {
  return (
    <AlertDialogProvider>
      <AlertDialogInitializer />
      {children}
    </AlertDialogProvider>
  )
}
export { AlertDialogProvider, useAlertDialog, AlertDialogInitializer, GlobalAlertDialogProvider }
export type { AlertDialogOptions }
