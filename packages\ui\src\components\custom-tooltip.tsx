import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip'

export const CustomTooltip = ({
  children,
  tooltip,
  delayDuration = 200,
  ...props
}: {
  children: React.ReactNode
  tooltip: React.ReactNode
  delayDuration?: number
} & React.ComponentProps<typeof Tooltip>) => (
  <TooltipProvider delayDuration={delayDuration}>
    <Tooltip {...props}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side="top" className="max-w-60">
        {tooltip}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
)
