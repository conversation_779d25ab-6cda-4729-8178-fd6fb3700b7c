import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useQuery, useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/orders/table-search'
import { OrderDetails } from '@/components/orders/order-details'
import { CreateOrderDialog } from '@/components/orders/create-order-dialog'
import { getTableColumns } from '@/components/orders/columns'
import { Button } from '@coozf/ui/components/button'
import { Plus } from 'lucide-react'

type OrderItem = RouterOutput['order']['list']['data'][number]

type OrderSearchInput = {
  search?: string
  status?: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  startDate?: string
  endDate?: string
}

type Search = {
  search?: string
  status?: string
}

export const Route = createFileRoute('/_authenticated/orders')({
  component: OrdersPage,
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      search: search?.search as string,
      status: search?.status as string,
    }
  },
})

function OrdersPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  })

  const { search, status } = useSearch({ strict: false })

  const [filter, setFilter] = useState<OrderSearchInput>({
    search,
    status: status as 'PENDING' | 'COMPLETED' | 'CANCELLED' | undefined,
  })

  const [selectedOrder, setSelectedOrder] = useState<OrderItem | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // 获取订单列表
  const {
    data: ordersData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.order.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...filter,
    }),
  )

  // 创建订单
  const createOrderMutation = useMutation(
    trpc.order.create.mutationOptions({
      onSuccess: () => {
        toast.success('订单创建成功')
        setIsCreateDialogOpen(false)
        refetch()
      },
      onError: (error) => {
        toast.error(error.message || '创建失败')
      },
    }),
  )

  // 开通转账
  const bankTransferMutation = useMutation(
    trpc.order.bankTransfer.mutationOptions({
      onSuccess: () => {
        toast.success('开通转账成功')
        refetch()
      },
      onError: (error) => {
        toast.error(error.message || '操作失败')
      },
    }),
  )

  // 取消订单
  const cancelOrderMutation = useMutation(
    trpc.order.cancelOrder.mutationOptions({
      onSuccess: () => {
        toast.success('订单已取消')
        refetch()
      },
      onError: (error) => {
        toast.error(error.message || '操作失败')
      },
    }),
  )

  const orders = ordersData?.data || []

  const handleViewDetails = (order: OrderItem) => {
    setSelectedOrder(order)
    setIsDetailsDialogOpen(true)
  }

  const handleCancelOrder = (orderId: string) => {
    if (window.confirm('确定要取消该订单吗？')) {
      cancelOrderMutation.mutate({ orderId })
    }
  }

  const handleBankTransfer = (orderId: string) => {
    if (window.confirm('确定要开通转账吗？')) {
      bankTransferMutation.mutate({ orderId })
    }
  }

  const onCreateSubmit = (values: any) => {
    createOrderMutation.mutate(values)
  }

  const columns = useMemo(
    () => getTableColumns(handleViewDetails, handleCancelOrder, handleBankTransfer),
    [handleViewDetails, handleCancelOrder, handleBankTransfer],
  )

  return (
    <div className="flex flex-1 flex-col gap-6 overflow-hidden">
      {/* 搜索组件 */}
      <div className="flex items-center justify-between">
        <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 })
            setFilter(value)
          }}
        />
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4" />
          创建订单
        </Button>
      </div>

      {/* 订单表格 */}
      <DataTable
        columns={columns}
        data={orders}
        rowCount={ordersData?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />

      {/* 订单详情弹窗 */}
      <OrderDetails open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen} selectedOrder={selectedOrder} />

      {/* 创建订单弹窗 */}
      <CreateOrderDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        isPending={createOrderMutation.isPending}
        onSubmit={onCreateSubmit}
      />
    </div>
  )
}
