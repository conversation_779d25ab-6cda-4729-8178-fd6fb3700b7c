import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { queryClient, trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { SecretManagement } from '../settings/secret-management'
import { WebhookSettings } from '../settings/webhook-settings'
import { OEMConfiguration } from '../oem/oem-configuration'
import { OEMConfigDialog } from '../oem/oem-configDialog'
import type { TabComponentProps } from '../types'
import type { OEMConfig } from '@coozf/zod'

export function SettingsTab({ applicationId, application }: TabComponentProps) {
  const [showOEMConfigDialog, setShowOEMConfigDialog] = useState(false)

  // 从应用数据中获取OEM配置
  const oemConfig = (application as any)?.oemConfig
  const oemEnabled = (application as any)?.oemEnabled || false

  // 更新OEM配置
  const updateOEMConfigMutation = useMutation(
    trpc.clientVersion.updateOEMConfig.mutationOptions({
      onSuccess: () => {
        toast.success('OEM配置更新成功')
        setShowOEMConfigDialog(false)
        queryClient.invalidateQueries({
          queryKey: trpc.application.byId.queryKey({ applicationId }),
        })
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }),
  )

  const handleOEMConfigSave = (config: OEMConfig) => {
    updateOEMConfigMutation.mutate({
      ...config,
      applicationId,
    })
  }

  return (
    <>
      <div className="space-y-6">
        <SecretManagement applicationId={applicationId} />
        <WebhookSettings applicationId={applicationId} application={application} />

        {/* OEM品牌配置 */}
        {oemEnabled && <OEMConfiguration oemConfig={oemConfig} onConfigureClick={() => setShowOEMConfigDialog(true)} />}
      </div>

      {/* OEM配置对话框 */}
      <OEMConfigDialog
        open={showOEMConfigDialog}
        onOpenChange={setShowOEMConfigDialog}
        initialConfig={oemConfig}
        onSave={handleOEMConfigSave}
        applicationId={applicationId}
      />
    </>
  )
}
