import { z } from 'zod'
import { router } from '@/trpc'
import { adminApplicationProcedure, adminProtectedProcedure } from '@/procedure'
import { OrderService, QuotaService, PlatformCreditsService } from '@/lib'
import { AdminCreditsQuotaOrderSchema, AdminTrafficQuotaOrderSchema } from '@coozf/zod'

export const quotaRouter = router({
  // 获取应用配额概览
  getQuotaOverview: adminApplicationProcedure.query(async ({ ctx }) => {
    return await QuotaService.getQuotaOverview(ctx.applicationId)
  }),

  // 获取平台点数配置
  getPlatformConfigs: adminProtectedProcedure.query(async () => {
    return await PlatformCreditsService.getAllPlatformConfigs()
  }),

  // 获取应用的点数配置
  getApplicationCreditsConfig: adminApplicationProcedure.query(async ({ ctx }) => {
    return await PlatformCreditsService.getApplicationCreditsConfig(ctx.applicationId)
  }),

  // 更新应用的点数配置
  updateApplicationCreditsConfig: adminApplicationProcedure
    .input(z.object({
      NORMAL: z.number().min(1, '普通平台点数必须大于0').optional(),
      VERTICAL: z.number().min(1, '垂直平台点数必须大于0').optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      await PlatformCreditsService.updateApplicationCreditsConfig(ctx.applicationId, input)
      // 自动重新计算点数使用量
      const newCreditsUsed = await PlatformCreditsService.recalculateCreditsUsed(ctx.applicationId)
      return { 
        success: true, 
        message: '应用点数配置更新成功，已重新计算使用量', 
        creditsUsed: newCreditsUsed 
      }
    }),

  // 设置点数配额到期时间
  setCreditsExpireDate: adminApplicationProcedure
    .input(z.object({
      expireDate: z.date(),
    }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.application.update({
        where: { id: ctx.applicationId },
        data: {
          creditsExpireDate: input.expireDate,
        },
      })

      return { success: true, message: '点数配额到期时间设置成功' }
    }),

  // 重置已使用点数
  resetCreditsUsed: adminApplicationProcedure
    .mutation(async ({ ctx }) => {
      await ctx.db.application.update({
        where: { id: ctx.applicationId },
        data: {
          creditsUsed: 0,
        },
      })

      return { success: true, message: '已使用点数已重置为0' }
    }),

  // 重新计算点数使用量
  recalculateCreditsUsed: adminApplicationProcedure
    .mutation(async ({ ctx }) => {
      const newCreditsUsed = await PlatformCreditsService.recalculateCreditsUsed(ctx.applicationId)
      return { success: true, message: `已重新计算点数使用量: ${newCreditsUsed}`, creditsUsed: newCreditsUsed }
    }),

  // 获取配额历史记录
  getQuotaHistory: adminApplicationProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      pageSize: z.number().min(1).max(100).default(10),
    }))
    .query(async ({ ctx, input }) => {
      return await OrderService.getQuotaOrderHistory(ctx.applicationId, input.page, input.pageSize)
    }),
})