import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'

export function useSecretManagement(applicationId: string) {
  const [showSecret, setShowSecret] = useState(false)
  const [showRegenerateConfirm, setShowRegenerateConfirm] = useState(false)
  const [showNewSecretDialog, setShowNewSecretDialog] = useState(false)
  const [newSecret, setNewSecret] = useState('')

  // 重新生成密钥
  const regenerateSecretMutation = useMutation(
    trpc.application.regenerateSecret.mutationOptions({
      onSuccess: (data) => {
        setNewSecret(data.secret)
        setShowRegenerateConfirm(false)
        setShowNewSecretDialog(true)
        toast.success('密钥重新生成成功')
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })
  )

  const handleRegenerateSecret = () => {
    regenerateSecretMutation.mutate({ applicationId })
  }

  const handleNewSecretDialogClose = () => {
    setShowNewSecretDialog(false)
    setNewSecret('')
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  return {
    showSecret,
    setShowSecret,
    showRegenerateConfirm,
    setShowRegenerateConfirm,
    showNewSecretDialog,
    setShowNewSecretDialog,
    newSecret,
    regenerateSecretMutation,
    handleRegenerateSecret,
    handleNewSecretDialogClose,
    copyToClipboard,
  }
}