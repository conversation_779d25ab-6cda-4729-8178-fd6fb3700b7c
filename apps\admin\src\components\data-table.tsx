import { TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Table } from '@/components/table-scroll-area'
// import { ScrollArea } from '@coozf/ui/components/scroll-area';
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  type PaginationState,
  type OnChangeFn,
} from '@tanstack/react-table'
import { DataTablePagination } from '@/components/data-table-pagination'
import { LoadingContainer } from '@coozf/ui/components/loading'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data?: TData[]
  rowCount?: number
  pagination?: PaginationState
  setPagination?: OnChangeFn<PaginationState>
}

export function DataTable<TData, TValue>({
  // setAccount,
  columns,
  data,
  rowCount,
  pagination,
  setPagination,
  isloading = false,
}: DataTableProps<TData, TValue> & { isloading?: boolean }) {
  const table = useReactTable({
    data: data ?? [],
    columns,
    rowCount,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    // debugTable: true,
    // defaultColumn: {
    //   size: 200,
    //   minSize: 200,
    //   maxSize: 200,
    // },
    // autoResetPageIndex: true,
  })
  return (
    <div className="flex flex-col flex-1 overflow-hidden justify-between gap-2 border rounded-md">
      {data && !isloading ? (
        <Table>
          <TableHeader className="sticky top-0 bg-muted z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-muted-foreground">
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  style={{
                    height: `50px`,
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      align="left"
                      key={cell.id}
                      style={{
                        width: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      ) : (
        <LoadingContainer />
      )}
      {pagination && (
        <div className="flex-shrink-0 pb-2">
          <DataTablePagination table={table} />
        </div>
      )}
    </div>
  )
}

// 常用的列定义工具函数
export function createActionColumn<TData>(actions: (row: TData) => React.ReactNode): ColumnDef<TData> {
  return {
    id: 'actions',
    header: () => <div className="text-right">操作</div>,
    cell: ({ row }) => <div className="text-right">{actions(row.original)}</div>,
  }
}

export function createSelectColumn<TData>(): ColumnDef<TData> {
  return {
    id: 'select',
    header: ({ table }) => (
      <input
        type="checkbox"
        checked={table.getIsAllPageRowsSelected()}
        onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
        aria-label="全选"
      />
    ),
    cell: ({ row }) => (
      <input
        type="checkbox"
        checked={row.getIsSelected()}
        onChange={(e) => row.toggleSelected(e.target.checked)}
        aria-label="选择行"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }
}
