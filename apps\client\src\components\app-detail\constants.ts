import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, Smartphone } from 'lucide-react'
import type { MenuItem } from './types'

export const MENU_ITEMS: MenuItem[] = [
  { id: 'overview', label: '概览', icon: BarChart3 },
  { id: 'settings', label: '应用设置', icon: Settings },
  { id: 'client-management', label: 'OEM客户端', icon: Smartphone },
]

export const CHART_CONFIG = {
  accountCount: {
    label: '新增账号',
    color: 'hsl(var(--chart-1))',
  },
  trafficGB: {
    label: '使用流量',
    color: 'hsl(var(--chart-2))',
  },
} as const

export const VERSION_REGEX = /^\d+\.\d+\.\d+$/
export const COLOR_REGEX = /^#[0-9A-Fa-f]{6}$/