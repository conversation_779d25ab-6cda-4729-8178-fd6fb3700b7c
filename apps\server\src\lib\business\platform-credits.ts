import { db } from '@coozf/db'
import { systemConfigCache } from '../services/cache'

/**
 * 平台点数配置服务
 */
export class PlatformCreditsService {
  /**
   * 获取平台类型
   */
  static async getPlatformType(platformCode: string): Promise<string> {
    const config = await systemConfigCache.getPlatformConfigs()
    const platformConfig = config[platformCode]
    return platformConfig?.platformType || 'NORMAL'
  }

  /**
   * 获取平台类型的默认点数
   */
  static async getDefaultCreditsPerType(platformType: string): Promise<number> {
    const config = await systemConfigCache.getCreditsConfigs()
    return config[platformType]?.creditsPerAccount || 5
  }

  /**
   * 获取应用在指定平台的点数配置
   */
  static async getCreditsPerAccount(applicationId: string, platformCode: string): Promise<number> {
    // 1. 获取平台类型
    const platformType = await this.getPlatformType(platformCode)

    // 2. 先查应用级配置覆盖（按账号类型）
    const app = await db.application.findUnique({
      where: { id: applicationId },
      select: { platformCreditsConfig: true },
    })

    const appConfig = (app?.platformCreditsConfig as Record<string, number>) || {}
    if (appConfig[platformType]) {
      return appConfig[platformType]
    }

    // 3. 查系统默认配置（按账号类型）
    return await this.getDefaultCreditsPerType(platformType)
  }

  /**
   * 批量计算多个账号的点数消耗
   */
  static async calculateTotalCredits(applicationId: string, accounts: { platformCode: string }[]): Promise<number> {
    let totalCredits = 0

    for (const account of accounts) {
      const credits = await this.getCreditsPerAccount(applicationId, account.platformCode)
      totalCredits += credits
    }

    return totalCredits
  }

  /**
   * 获取应用的点数配置（含默认值）
   */
  static async getApplicationCreditsConfig(applicationId: string): Promise<{
    NORMAL: number
    VERTICAL: number
  }> {
    const app = await db.application.findUnique({
      where: { id: applicationId },
      select: { platformCreditsConfig: true },
    })

    const appConfig = (app?.platformCreditsConfig as Record<string, number>) || {}

    // 获取系统默认配置
    const defaultNormal = await this.getDefaultCreditsPerType('NORMAL') // 5
    const defaultVertical = await this.getDefaultCreditsPerType('VERTICAL') // 10

    return {
      NORMAL: appConfig.NORMAL || defaultNormal,
      VERTICAL: appConfig.VERTICAL || defaultVertical,
    }
  }

  /**
   * 更新应用的点数配置
   */
  static async updateApplicationCreditsConfig(
    applicationId: string,
    config: { NORMAL?: number; VERTICAL?: number },
  ): Promise<void> {
    const currentApp = await db.application.findUnique({
      where: { id: applicationId },
      select: { platformCreditsConfig: true },
    })

    const currentConfig = (currentApp?.platformCreditsConfig as Record<string, number>) || {}
    const newConfig = { ...currentConfig, ...config }

    await db.application.update({
      where: { id: applicationId },
      data: { platformCreditsConfig: newConfig },
    })
  }

  /**
   * 获取所有平台配置（用于前端显示）
   */
  static async getAllPlatformConfigs() {
    const platforms = await db.systemConfig.findMany({
      where: { category: 'PLATFORM_CONFIG', isEnabled: true },
    })

    const creditsConfigs = await db.systemConfig.findMany({
      where: { category: 'CREDITS_CONFIG', isEnabled: true },
    })

    const creditsMap = creditsConfigs.reduce(
      (acc, config) => {
        const { creditsPerAccount } = config.value as { creditsPerAccount: number }
        acc[config.key.replace('_PLATFORM_CREDITS', '')] = creditsPerAccount
        return acc
      },
      {} as Record<string, number>,
    )

    return platforms.map((platform) => {
      const { platformName, platformType } = platform.value as any
      return {
        platformCode: platform.key,
        platformName,
        platformType,
        defaultCredits: creditsMap[platformType] || 5,
      }
    })
  }

  /**
   * 检查应用点数是否足够
   */
  static async checkCreditsQuota(applicationId: string, requiredCredits: number): Promise<boolean> {
    const application = await db.application.findUnique({
      where: { id: applicationId },
      select: {
        creditsQuota: true,
        creditsUsed: true,
        creditsExpireDate: true,
      },
    })

    if (!application) {
      return false
    }

    // 检查配额是否过期
    if (application.creditsExpireDate && new Date() > application.creditsExpireDate) {
      return false
    }

    return application.creditsUsed + requiredCredits <= application.creditsQuota
  }

  /**
   * 消费点数配额
   */
  static async consumeCredits(applicationId: string, credits: number, endpoint: string): Promise<boolean> {
    // 检查配额是否足够
    const canConsume = await this.checkCreditsQuota(applicationId, credits)
    if (!canConsume) {
      return false
    }

    // 使用事务更新点数使用量并记录API调用
    await db.$transaction(async (tx) => {
      // 更新应用点数使用量
      await tx.application.update({
        where: { id: applicationId },
        data: {
          creditsUsed: {
            increment: credits,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId,
          endpoint,
          method: 'POST',
          costType: 'CREDITS',
          costAmount: credits,
          statusCode: 200,
        },
      })
    })

    return true
  }

  /**
   * 重新计算应用的点数使用量
   * 基于授权账号表和当前点数配置重新计算
   */
  static async recalculateCreditsUsed(applicationId: string): Promise<number> {
    // 获取应用的所有授权账号（未删除的）
    const authAccounts = await db.authAccount.findMany({
      where: {
        applicationId,
        isDeleted: false,
      },
      select: {
        platformCode: true,
      },
    })

    // 根据当前点数配置重新计算总使用量
    let totalCreditsUsed = 0
    for (const account of authAccounts) {
      const credits = await this.getCreditsPerAccount(applicationId, account.platformCode)
      totalCreditsUsed += credits
    }

    // 更新应用的creditsUsed字段
    await db.application.update({
      where: { id: applicationId },
      data: {
        creditsUsed: totalCreditsUsed,
      },
    })

    return totalCreditsUsed
  }
}
