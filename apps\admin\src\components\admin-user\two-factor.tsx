import { useState } from 'react'
import { <PERSON><PERSON> } from '@coozf/ui/components/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@coozf/ui/components/input-otp'
import { Input } from '@coozf/ui/components/input'
import { Badge } from '@coozf/ui/components/badge'
import { Separator } from '@coozf/ui/components/separator'
import { LoadingButton } from '@coozf/ui/components/loading'
import { authClient } from '@/lib/auth/auth-client'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { CheckIcon, CopyIcon, QrCodeIcon } from 'lucide-react'
import QRCode from 'react-qr-code'

interface TwoFactorProps {
  open: boolean
  onClose?: () => void
  onSuccess: () => void
}

export const TwoFactor = ({ open, onClose, onSuccess }: TwoFactorProps) => {
  const [step, setStep] = useState(1)
  const [otpCode, setOtpCode] = useState('')
  const [password, setPassword] = useState('')
  const [secretCopied, setSecretCopied] = useState(false)

  const [totpSetup, setTotpSetup] = useState<{ totpURI: string } | null>(null)

  // 启用2FA并获取TOTP URI
  const enableTotpMutation = useMutation({
    mutationFn: async (password: string) => {
      const response = await authClient.twoFactor.enable({
        password,
      })
      return response.data
    },
    onSuccess: (data) => {
      setTotpSetup(data)
      setStep(2)
    },
    onError: (error) => {
      toast.error('启用双重认证失败，请检查密码')
      console.error(error)
    },
  })

  // 验证TOTP码完成绑定
  const verifyTotpMutation = useMutation({
    mutationFn: async (code: string) => {
      const response = await authClient.twoFactor.verifyTotp({ code })
      return response
    },
    onSuccess: () => {
      toast.success('TOTP绑定成功！')
      onSuccess()
    },
    onError: (error) => {
      toast.error('验证码错误，请重试')
      console.error(error)
    },
  })

  const handleStartSetup = () => {
    if (!password) {
      toast.error('请输入密码')
      return
    }
    enableTotpMutation.mutate(password)
  }

  const handleVerifyCode = () => {
    if (otpCode.length !== 6) {
      toast.error('请输入6位验证码')
      return
    }
    verifyTotpMutation.mutate(otpCode)
  }

  const handleCopySecret = async () => {
    if (totpSetup?.totpURI) {
      try {
        // 从URI中提取secret
        const url = new URL(totpSetup.totpURI)
        const secret = url.searchParams.get('secret')
        if (secret) {
          await navigator.clipboard.writeText(secret)
          setSecretCopied(true)
          toast.success('密钥已复制到剪贴板')
          setTimeout(() => setSecretCopied(false), 3000)
        }
      } catch (error) {
        toast.error('复制失败')
      }
    }
  }

  if (!open) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCodeIcon className="h-5 w-5" />
            设置双因素认证
          </CardTitle>
          <CardDescription>通过认证器应用增强账户安全性</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 步骤指示器 */}
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((num) => (
              <div key={num} className="flex items-center space-x-2">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= num ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {step > num ? <CheckIcon className="h-4 w-4" /> : num}
                </div>
                {num < 3 && <div className={`w-16 h-px ${step > num ? 'bg-primary' : 'bg-muted'}`} />}
              </div>
            ))}
          </div>

          {/* Step 1: 介绍说明 */}
          {step === 1 && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">第1步：准备认证器应用</h3>
                <p className="text-sm text-muted-foreground mb-3">请在您的手机上下载并安装以下任一认证器应用：</p>
                <div className="space-y-2">
                  <Badge variant="outline">Google Authenticator</Badge>
                  <Badge variant="outline">Microsoft Authenticator</Badge>
                  <Badge variant="outline">Authy</Badge>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">请输入您的密码以继续：</label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="输入密码"
                />
              </div>
              <LoadingButton onClick={handleStartSetup} isPending={enableTotpMutation.isPending} className="w-full">
                开始设置
              </LoadingButton>
            </div>
          )}

          {/* Step 2: 扫描二维码 */}
          {step === 2 && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">第2步：扫描二维码</h3>
                <p className="text-sm text-muted-foreground mb-4">使用认证器应用扫描下方二维码，或手动输入密钥：</p>
              </div>

              {/* 二维码区域 */}
              <div className="flex flex-col items-center space-y-4">
                {totpSetup?.totpURI ? (
                  <div className="p-4 bg-white rounded-lg border">
                    <QRCode value={totpSetup.totpURI} size={192} />
                  </div>
                ) : (
                  <div className="w-48 h-48 bg-muted flex items-center justify-center rounded-lg border-2 border-dashed">
                    <div className="text-center">
                      <QrCodeIcon className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">二维码加载中...</p>
                    </div>
                  </div>
                )}
              </div>

              {totpSetup && (
                <>
                  <Separator />

                  {/* 手动输入密钥 */}
                  <div>
                    <p className="text-sm font-medium mb-2">或手动输入密钥：</p>
                    <div className="flex items-center space-x-2 p-3 bg-muted rounded-md">
                      <code className="flex-1 text-xs font-mono break-all">
                        {(() => {
                          try {
                            const url = new URL(totpSetup.totpURI)
                            return url.searchParams.get('secret') || 'N/A'
                          } catch {
                            return 'N/A'
                          }
                        })()}
                      </code>
                      <Button variant="outline" size="sm" onClick={handleCopySecret}>
                        {secretCopied ? <CheckIcon className="h-4 w-4" /> : <CopyIcon className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </>
              )}

              <Button onClick={() => setStep(3)} className="w-full" disabled={!totpSetup}>
                下一步：验证设置
              </Button>
            </div>
          )}

          {/* Step 3: 验证码验证 */}
          {step === 3 && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">第3步：输入验证码</h3>
                <p className="text-sm text-muted-foreground mb-4">请输入认证器应用中显示的6位验证码：</p>
              </div>

              <div className="flex justify-center">
                <InputOTP maxLength={6} value={otpCode} onChange={setOtpCode}>
                  <InputOTPGroup>
                    {[0, 1, 2, 3, 4, 5].map((index) => (
                      <InputOTPSlot key={index} index={index} />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
              </div>

              <div className="flex space-x-3">
                <Button variant="outline" onClick={() => setStep(2)} className="flex-1">
                  上一步
                </Button>
                <LoadingButton onClick={handleVerifyCode} isPending={verifyTotpMutation.isPending} className="flex-1">
                  完成设置
                </LoadingButton>
              </div>
            </div>
          )}

          {/* 取消按钮 */}
          {!!onClose && (
            <div className="pt-4 border-t">
              <Button variant="ghost" onClick={onClose} className="w-full">
                取消
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
