import { isDev } from '../env'

export const loggerConfig = {
  level: 'info',
  serializers: {
    req: (req: any) => ({
      method: req.method,
      url: req.url.split('?')[0], // 移除查询参数
    }),
    res: (res: any) => ({
      statusCode: res.statusCode,
    }),
  },
  transport: {
    target: 'pino-pretty',
    options: {
      translateTime: 'HH:MM:ss',
      ignore: 'pid,hostname,reqId,responseTime',
      colorize: true,
      levelFirst: false,
      singleLine: true,
      messageFormat: '{msg} {req.method} {req.url} {res.statusCode}',
    },
  },
}
