import { router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import {
  AuthorizeRoleSchema,
  AuthorizeRoleResponseSchema,
  SaveRetainConsultCardSchema,
  SaveRetainConsultCardResponseSchema,
  GetRetainConsultCardSchema,
  UploadImageResponseSchema,
  DouyinThirdSendMessageSchema,
  RecallMessageSchema,
} from '@coozf/zod'
import { TRPCError } from '@trpc/server'
import axios from 'axios'
import { OpenPlatformTokenData } from '@/lib/utils'
import { oauthService } from '@/lib/services/oauth'
import { env } from '@/env'
import { AccountAuthType } from '@prisma/client'
import z from 'zod'

export const openDouyinRouter: ReturnType<typeof router> = router({
  // POST /douyin/role/check - 获取抖音账号角色
  postAuthorizeRole: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/douyin/role/check',
        protect: true,
        tags: ['抖音'],
        summary: '获取账号角色',
        description: '获取账号角色',
      },
    })
    .input(AuthorizeRoleSchema)
    .output(AuthorizeRoleResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      const url = 'https://open.douyin.com/api/douyin/v1/role/check/'

      const requestData = {
        open_id: account.platformUserId,
        role_labels: input.roleLabels,
      }

      const applicationAccessToken = await oauthService.getDouyinAppToken(env.DOUYIN_CLIENT_KEY)

      const result = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': applicationAccessToken,
        },
      })

      if (result.data.err_no !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.err_msg,
        })
      }

      return {
        match_result: result.data.data.match_result,
        filter_role: result.data.data.filter_role,
      }
    }),

  // GET /douyin/retain-consult-card/list - 获取留资卡列表
  getRetainConsultCardList: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/douyin/retain-consult-card/list',
        protect: true,
        tags: ['抖音'],
        summary: '获取留资卡列表',
        description: '获取留资卡列表',
      },
    })
    .input(z.object({ id: z.string() }))
    .output(GetRetainConsultCardSchema)
    .query(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://open.douyin.com/im/get/retain_consult_card/'

      const requestData = {
        open_id: account.platformUserId,
      }

      const result = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': tokenData.access_token,
        },
        params: requestData,
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return result.data.cards || []
    }),

  // POST /douyin/save/retain-consult-card - 创建/编辑留资卡
  postSaveRetainConsultCard: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/douyin/save/retain-consult-card',
        protect: true,
        tags: ['抖音'],
        summary: '创建/编辑留资卡',
        description: '创建/编辑留资卡',
      },
    })
    .input(SaveRetainConsultCardSchema)
    .output(SaveRetainConsultCardResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://open.douyin.com/im/save/retain_consult_card/'

      const requestData = {
        open_id: account.platformUserId,
        card_id: input.cardId,
        components: input.components,
        media_id: input.mediaId,
        title: input.title,
      }

      const result = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': tokenData.access_token,
        },
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return result.data.data
    }),

  // GET /douyin/del/retain-consult-card - 删除留资卡
  deleteRetainConsultCard: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/douyin/del/retain-consult-card',
        protect: true,
        tags: ['抖音'],
        summary: '删除留资卡',
        description: '删除留资卡',
      },
    })
    .input(z.object({ id: z.string(), cardId: z.string() }))
    .output(z.object({}))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://open.douyin.com/im/del/retain_consult_card/'

      const requestData = {
        open_id: account.platformUserId,
        card_id: input.cardId,
      }

      const result = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': tokenData.access_token,
        },
        params: requestData,
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return {
        cardId: input.cardId,
      }
    }),

  // POST /douyin/upload/image - 上传图片
  postUploadImage: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/douyin/upload/image',
        protect: true,
        tags: ['抖音'],
        summary: '上传图片',
        description: '上传图片',
      },
    })
    .input(
      z.object({
        image: z.instanceof(File).openapi({
          type: 'string',
          format: 'binary', // 关键：指定为二进制文件格式
          description: '要上传的图片文件',
        }),
      }),
    )
    .output(UploadImageResponseSchema)
    .mutation(async ({ ctx, input }) => {
      let imageFile

      // 处理不同环境下的FormData解析
      if (input instanceof FormData) {
        imageFile = input.get('image') as File
      } else {
        // 对于某些环境，input可能已经被解析为对象
        imageFile = input.image
      }

      if (!imageFile || !(imageFile instanceof File)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '请提供有效的图片文件',
        })
      }

      const url = 'https://open.douyin.com/tool/imagex/client_upload/'

      const applicationAccessToken = await oauthService.getDouyinAppToken(env.DOUYIN_CLIENT_KEY)

      const douyinFormData = new FormData()

      douyinFormData.append('image', imageFile)

      const result = await axios.post(url, douyinFormData, {
        headers: {
          'Content-Type': `multipart/form-data`,
          'access-token': applicationAccessToken,
        },
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return {
        media_id: result.data.data.image_id,
        width: result.data.data.width,
        height: result.data.data.height,
        md5: result.data.data.md5,
      }
    }),

  // POST /douyin/send/message - 发送消息
  postSendMessage: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/douyin/send/message',
        protect: true,
        tags: ['抖音'],
        summary: '发送消息',
        description: '发送消息',
      },
    })
    .input(DouyinThirdSendMessageSchema)
    .output(z.object({}))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://open.douyin.com/im/send/msg/'

      const content: any = {}

      switch (input.content.msgType) {
        case 1:
          content.msg_type = 1
          content.text = {
            text: input.content.content,
          }
          break
        case 2:
          content.msg_type = 2
          content.image = {
            media_id: input.content.content,
          }
          break
        case 8:
          content.msg_type = 8
          content.retain_consult_card = {
            card_id: input.content.content,
          }
          break
        default:
          break
      }
      const requestData = {
        msg_id: input.msgId,
        conversation_id: input.conversationId,
        to_user_id: input.toUserId,
        content,
        scene: input.scene || 'im_replay_msg',
        channel: input.channel ? 3 : 1, // 通过策略发送的为自动消息,否则人工发送
      }

      const result = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': tokenData.access_token,
        },
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return result.data
    }),

  // POST /douyin/recall/message - 撤回消息
  postRecallMessage: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/douyin/recall/message',
        protect: true,
        tags: ['抖音'],
        summary: '撤回消息',
        description: '撤回消息',
      },
    })
    .input(RecallMessageSchema)
    .output(z.object({}))
    .mutation(async ({ ctx, input }) => {
      const account = await ctx.db.authAccount.findUnique({
        where: {
          id: input.id,
          authType: AccountAuthType.TOKEN,
          applicationId: ctx.application.id,
        },
      })

      if (!account) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '媒体账号不存在或无权限访问',
        })
      }

      if (account.platformCode !== 'douyinopen') {
        throw new TRPCError({
          code: 'UNSUPPORTED_MEDIA_TYPE',
          message: '媒体账号不是抖音开放平台账号',
        })
      }

      let tokenData: OpenPlatformTokenData
      try {
        tokenData = JSON.parse(account.platformCookieHash!)
      } catch {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '系统错误',
        })
      }

      const url = 'https://open.douyin.com/im/recall/msg/'

      const requestData = {
        msg_id: input.msgId,
        conversation_id: input.conversationId,
        conversation_type: input.conversationType,
      }

      const result = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'access-token': tokenData.access_token,
        },
        params: {
          open_id: account.platformUserId,
        },
      })

      if (result.data.data.error_code !== 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.data.data.description,
        })
      }

      return result.data
    }),
})
