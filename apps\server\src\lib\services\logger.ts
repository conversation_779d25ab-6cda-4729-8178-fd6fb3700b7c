import type { FastifyBaseLogger } from 'fastify'
import { tlsService } from '@coozf/huoshan'
import { isDev } from '@/env'

export interface LogContext {
  [key: string]: any
}

export interface LoggerConfig {
  enableTls?: boolean
  enableConsole?: boolean
  minLevel?: LogLevel
}

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export class LoggerService {
  private pinoLogger?: FastifyBaseLogger
  private config: Required<LoggerConfig>

  constructor(config: LoggerConfig = {}) {
    this.config = {
      enableTls: config.enableTls ?? process.env.ENABLE_TLS_LOGGING === 'true',
      enableConsole: config.enableConsole ?? true,
      minLevel: config.minLevel ?? 'info',
    }
  }

  /**
   * 设置 Pino 日志器实例
   */
  setPinoLogger(logger: FastifyBaseLogger) {
    this.pinoLogger = logger
  }

  /**
   * 检查日志级别是否应该输出
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = ['debug', 'info', 'warn', 'error']
    const minLevelIndex = levels.indexOf(this.config.minLevel)
    const currentLevelIndex = levels.indexOf(level)
    return currentLevelIndex >= minLevelIndex
  }

  /**
   * 核心日志方法
   */
  private async log(level: LogLevel, message: string, context?: LogContext) {
    if (!this.shouldLog(level)) {
      return
    }

    const logData = {
      level,
      message,
      timestamp: new Date().toISOString(),
      ...context,
    }

    // 本地日志 (Pino)
    if (this.config.enableConsole && this.pinoLogger) {
      this.pinoLogger[level](logData, message)
    } else if (this.config.enableConsole) {
      // 回退到 console 输出
      console.log(`[${level.toUpperCase()}] ${message}`, context || '')
    }

    // 远程日志 (火山云 TLS)
    if (this.config.enableTls) {
      try {
        await tlsService[level](message, context)
      } catch (error) {
        // 远程日志失败不应影响业务逻辑，静默处理
        if (this.pinoLogger) {
          this.pinoLogger.warn({ error }, '远程日志发送失败')
        }
      }
    }
  }

  /**
   * 调试日志
   */
  async debug(message: string, context?: LogContext) {
    await this.log('debug', message, context)
  }

  /**
   * 信息日志
   */
  async info(message: string, context?: LogContext) {
    await this.log('info', message, context)
  }

  /**
   * 警告日志
   */
  async warn(message: string, context?: LogContext) {
    await this.log('warn', message, context)
  }

  /**
   * 错误日志
   */
  async error(message: string, context?: LogContext) {
    await this.log('error', message, context)
  }

  /**
   * 请求日志 - 用于 HTTP 请求跟踪
   */
  async request(method: string, url: string, statusCode: number, responseTime: number, context?: LogContext) {
    await this.info(`${method} ${url} ${statusCode}`, {
      type: 'request',
      method,
      url,
      statusCode,
      responseTime,
      ...context,
    })
  }

  /**
   * 数据库操作日志
   */
  async database(operation: string, table: string, duration: number, context?: LogContext) {
    await this.debug(`DB ${operation} ${table}`, {
      type: 'database',
      operation,
      table,
      duration,
      ...context,
    })
  }

  /**
   * 业务逻辑日志
   */
  async business(action: string, context?: LogContext) {
    await this.info(action, {
      type: 'business',
      action,
      ...context,
    })
  }

  /**
   * 安全相关日志
   */
  async security(action: string, context?: LogContext) {
    await this.warn(`Security: ${action}`, {
      type: 'security',
      action,
      ...context,
    })
  }
}

/**
 * 创建日志服务实例
 */
export function createLoggerService(config?: LoggerConfig): LoggerService {
  return new LoggerService(config)
}

/**
 * 默认日志服务实例 - 单例模式
 */
export const logger = createLoggerService({
  enableConsole: isDev,
  enableTls: !isDev,
})

/**
 * 全局日志服务，供整个应用使用
 */
let globalLogger: LoggerService = logger

/**
 * 设置全局日志服务实例
 */
export function setGlobalLogger(loggerInstance: LoggerService) {
  globalLogger = loggerInstance
}

/**
 * 获取全局日志服务实例
 */
export function getGlobalLogger(): LoggerService {
  return globalLogger
}
