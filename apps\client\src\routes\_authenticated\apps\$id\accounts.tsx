import { createFileRout<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { trpc } from '@/lib/trpc'
import { useQuery } from '@tanstack/react-query'
import { LoadingContainer } from '@coozf/ui/components/loading'
import { formatYearMonthDay } from '@coozf/ui/lib/day'
import { Badge } from '@coozf/ui/components/badge'

export const Route = createFileRoute('/_authenticated/apps/$id/accounts')({
  component: AccountsListPage,
})

function AccountsListPage() {
  const { id } = Route.useParams()
  const [page, setPage] = useState(1)
  const [platformFilter, setPlatformFilter] = useState<string>('ALL')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [searchTerm, setSearchTerm] = useState('')
  const pageSize = 10

  const { data, isLoading, isSuccess } = useQuery(
    trpc.authAccount.list.queryOptions({
      applicationId: id,
      page,
      pageSize,
      search: searchTerm || undefined,
    }),
  )

  const resetFilters = () => {
    setPlatformFilter('ALL')
    setStatusFilter('ALL')
    setSearchTerm('')
    setPage(1)
  }

  useEffect(() => {
    setPage(1)
  }, [searchTerm, platformFilter, statusFilter])

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link to="/apps/$id" params={{ id }} className="hover:text-foreground">
          应用详情
        </Link>
        <span>/</span>
        <span className="text-foreground">账号列表</span>
      </div>

      {/* 搜索和筛选 - 紧凑布局 */}
      {/* <Card>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">筛选：</span>
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="search" className="text-sm text-muted-foreground whitespace-nowrap">
                账号名
              </Label>
              <Input
                id="search"
                placeholder="输入账号名搜索"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-40"
              />
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="platform" className="text-sm text-muted-foreground whitespace-nowrap">
                平台
              </Label>
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部平台</SelectItem>
                  <SelectItem value="weibo">微博</SelectItem>
                  <SelectItem value="xiaohongshu">小红书</SelectItem>
                  <SelectItem value="douyin">抖音</SelectItem>
                  <SelectItem value="kuaishou">快手</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="status" className="text-sm text-muted-foreground whitespace-nowrap">
                状态
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部状态</SelectItem>
                  <SelectItem value="active">正常</SelectItem>
                  <SelectItem value="inactive">失效</SelectItem>
                  <SelectItem value="expired">过期</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" size="sm" onClick={resetFilters}>
              搜索
            </Button>
          </div>
        </CardContent>
      </Card> */}

      {isLoading && <LoadingContainer />}

      {/* 账号列表 */}
      {isSuccess && (
        <Card>
          <CardHeader>
            <CardTitle>账号列表</CardTitle>
            <CardDescription>查看和管理您的社交媒体账号</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>账号</TableHead>
                  <TableHead>平台</TableHead>
                  <TableHead>点数</TableHead>
                  <TableHead>添加时间</TableHead>
                  <TableHead>更新时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.length > 0 ? (
                  data.data.map((account) => (
                    <TableRow key={account.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div>
                            <div className="font-medium">{account.platformUserName}</div>
                            <div className="text-sm text-muted-foreground">{account.platformUserId}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge>{account.platformCode}</Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="">
                          <Badge variant={account.creditConfig.type === 'NORMAL' ? 'default' : 'outline'}>
                            {account.creditConfig.type}
                          </Badge>
                          {account.creditConfig.num}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">{formatYearMonthDay(account.createdAt)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">{formatYearMonthDay(account.updatedAt)}</div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center text-muted-foreground">
                      暂无符合条件的账号
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* 分页 */}
            {data.data.length > 0 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  共 {data.total} 条记录，第 {data.page} 页
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled={page <= 1} onClick={() => setPage(page - 1)}>
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={data.totalPages <= page}
                    onClick={() => setPage(page + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
