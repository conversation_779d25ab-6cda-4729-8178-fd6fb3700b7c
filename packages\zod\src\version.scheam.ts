import z from "zod"
// 版本类型枚举 桌面端 浏览器插件 rpa 爬虫脚本
const VersionTypeSchema = z.enum(['DESKTOP', 'BROWSER_PLUGIN', "RPA", "CRAWLER"])
const PlatformSchema = z.enum(['WIN', 'MAC'])

const registerV2Schema = z.object({
      deviceId: z.string().min(1, '设备ID不能为空'),
      name: z.string().min(1, '应用名称不能为空'),
      type: PlatformSchema,
      version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z'),
      appId: z.string().optional(),
    })

const registerSchema = z
  .string()
  .min(1, '设备ID不能为空')
  .or(
    registerV2Schema
  )

// 基础版本创建模式
const CreateVersionBaseSchema = z.object({
  version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z'),
  fileUrl: z.string().url().optional(),
  type: VersionTypeSchema,
  platform: PlatformSchema.optional(),
  forceUpdate: z.boolean(),
  description: z.string().optional(),
})

// 创建版本的输入模式
const CreateVersionSchema = CreateVersionBaseSchema.refine((data) => {
  // 浏览器插件不需要平台，下载地址可选（支持文件上传）
  if (data.type !== 'DESKTOP') {
    return !!data.fileUrl
  }
  return true
}, {
  message: '文件url不能为空'
}).refine((data) => {
  // 桌面端必须指定平台
  if (data.type === 'DESKTOP') {
    return !!data.platform
  }
  return true
}, {
  message: '桌面端必须指定平台'
})

// 更新版本的输入模式
const UpdateVersionSchema = z.object({
  id: z.string(),
  downloadUrl: z.string().url().optional(),
  forceUpdate: z.boolean().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
})

// 版本列表查询模式
const VersionListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  type: VersionTypeSchema.optional(),
  platform: PlatformSchema.optional(),
})

// 检查更新的输入模式
const CheckUpdateSchema = z.object({
  type: VersionTypeSchema,
  platform: PlatformSchema.optional(),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z').optional(),
  appId: z.string().optional(), // 新增：应用ID，用于OEM版本检查
}).refine((data) => {
  // 桌面端必须指定平台
  if (data.type === 'DESKTOP') {
    return !!data.platform
  }
  return true
}, {
  message: '桌面端必须指定平台'
})

export const CheckUpdateWithDesktopSchema = z.object({
  platform: PlatformSchema,
  version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z').optional(),
})

// 检查更新返回数据
const CheckUpdateResSchema = z.object({
  hasUpdate: z.boolean(),
  latestVersion: z.object({
    id: z.string(),
    downloadUrl: z.string().url(),
    forceUpdate: z.boolean(),
    description: z.string().nullable(),
    version: z.string()
  }).nullable(),

  forceUpdate: z.boolean(),
})

// 类型导出
export type VersionType = z.infer<typeof VersionTypeSchema>
export type Platform = z.infer<typeof PlatformSchema>
export type CreateVersionInput = z.infer<typeof CreateVersionSchema>
export type UpdateVersionInput = z.infer<typeof UpdateVersionSchema>
export type VersionListParams = z.infer<typeof VersionListSchema>
export type CheckUpdateParams = z.infer<typeof CheckUpdateSchema>
export type CheckUpdateRes = z.infer<typeof CheckUpdateResSchema>
export type RegisterParams = z.infer<typeof registerSchema>
export type RegisterV2Params = z.infer<typeof registerV2Schema>

export {
  VersionTypeSchema, PlatformSchema, CreateVersionSchema, UpdateVersionSchema, VersionListSchema, CheckUpdateSchema, CheckUpdateResSchema, registerSchema, registerV2Schema
}
