export interface DouyinWebhookBody {
  event: DouyinWebhookEvents
  client_key: string
  from_user_id: string
  to_user_id: string
  content: {
    challenge: string
    user_infos?: {
      open_id: string
      nick_name: string
      avatar: string
    }[]
    card_data?: {
      label: string
      value: string
    }[]
    card_status?: number
    scopes?: [string]
  } & Record<string, unknown>
  create_time: number
  log_id?: string
}

export enum DouyinWebhookEvents {
  /**
   * 验证 webhook
   */
  VerifyWebhook = 'verify_webhook',
  /**
   * 接收私信消息
   */
  IMReceiveMessage = 'im_receive_msg',
  /**
   * 发送私信消息
   */
  IMSendMessage = 'im_send_msg',
  /**
   * 群消息接收
   */
  IMGroupReceiveMessage = 'im_group_receive_msg',
  /**
   * 发送群消息
   */
  IMGroupSendMessage = 'im_group_send_msg',
  /**
   * 用户解除授权
   */
  Unauthorize = 'unauthorize',
  /**
   * 用户授权
   */
  Authorize = 'authorize',
  /**
   * 用户加群申请
   */
  EnterGroupAuditChange = 'enter_group_audit_change',
  /**
   * 用户加群成功
   */
  GroupFansEvent = 'group_fans_event',
  /**
   * 评论回复
   */
  CommentReply = 'item_comment_reply',

  /**
   * 合同授权
   */
  ContractAuthorize = 'contract_authorize',

  /**
   * 合同取消授权
   */
  ContractUnauthorize = 'contract_unauthorize',

  /**
   * 接收用户进入私信会话页事件，用户主动进入私信会话页触发
   */
  ImEnterDirectMessage = 'im_enter_direct_msg',

  /**
   * 视频点赞
   */
  NewVideoDigg = 'new_video_digg',

  /**
   * 用户关注
   */
  NewFollowAction = 'new_follow_action',

  /**
   * 私信撤回
   */
  IMRecallMsg = 'im_recall_msg',
}
