import { db } from '@coozf/db'
import { AccountListQuery, AuthResultCallbackInput, OpenPlatformAuthInput, PlatformCode } from '@coozf/zod'
import { Application, Prisma } from '@prisma/client'
import { paginate } from '../utils'
import { PlatformCreditsService } from './platform-credits'
import { accountCache, systemConfigCache } from '../services/cache'
import { TRPCError } from '@trpc/server'
import { QuotaService } from './quota'
import { CookieService } from '../services/cookie'
import { FastifyInstance, FastifyRequest } from 'fastify'
import cuid from 'cuid'
import { localAuthHandler, remoteAuthHandler } from './crawler-local'

export class MediaAccountServies {
  static async getMediaAccountOrders(params: AccountListQuery & { applicationId?: string }) {
    const { page = 1, pageSize = 10, platformCode, search, applicationId, startDate, endDate } = params

    // 构建查询条件
    const where: Prisma.AuthAccountWhereInput = {
      isDeleted: false,
    }
    if (applicationId) {
      where.applicationId = applicationId
    }

    if (platformCode) {
      where.platformCode = platformCode
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    if (search) {
      where.OR = [
        {
          platformUserId: {
            contains: search,
          },
        },
        {
          platformUserName: {
            contains: search,
          },
        },
      ]
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => db.authAccount.count({ where }),
        getItems: (skip, take) =>
          db.authAccount.findMany({
            where,
            skip,
            take,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              applicationId: true,
              platformCode: true,
              platformUserId: true,
              platformUserName: true,
              platformAvatar: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        transform: async (account) => ({
          ...account,
          platformCode: account.platformCode as PlatformCode,
          creditConfig: {
            num: await PlatformCreditsService.getCreditsPerAccount(account.platformCode, account.platformCode),
            type: await PlatformCreditsService.getPlatformType(account.platformCode),
          },
        }),
      },
    )
  }

  static async getFindFirst(params: Prisma.AuthAccountWhereInput) {
    return db.authAccount.findFirst({
      where: {
        ...params,
        isDeleted: false,
      },
    })
  }

  static async createMediaAccount(
    data: Omit<AuthResultCallbackInput, 'event' | 'status'>,
    application: FastifyRequest['application'],
  ) {
    const { platformCode, userId, userName, avatar, cookie, localStorage } = data

    const requiredCredits = await this.checkApplicationAccountQuota(platformCode, application.id)

    // 对Cookie进行加密处理
    const hashedCookie = await CookieService.encrypt(cookie)
    let hashedLocalStorage = null
    if (localStorage) {
      hashedLocalStorage = await CookieService.encrypt(localStorage)
    }

    // 使用事务创建账号并扣除点数
    const authAccount = await db.$transaction(async (tx) => {
      // 创建媒体账号绑定
      const account = await tx.authAccount.create({
        data: {
          applicationId: application.id,
          platformCode,
          platformUserId: userId,
          platformUserName: userName,
          platformAvatar: avatar,
          platformCookieHash: hashedCookie,
          platformLocalStorageHash: hashedLocalStorage,
        },
      })

      // 扣除对应点数
      await tx.application.update({
        where: { id: application.id },
        data: {
          creditsUsed: {
            increment: requiredCredits,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId: application.id,
          endpoint: '/media-accounts',
          method: 'POST',
          costType: 'CREDITS',
          costAmount: requiredCredits,
          statusCode: 200,
        },
      })

      return account
    })
    const { id, platformUserId, platformUserName, platformAvatar } = authAccount
    return {
      id,
      platformCode,
      platformUserId,
      platformUserName,
      platformAvatar,
    }
  }

  // 更新媒体账号
  static async updateMediaAccount({
    id,
    applicationId,
    platformCode,
    platformUserId,
    platformUserName,
    platformAvatar,
    platformCookie,
  }: {
    id: string
    applicationId: string
    platformCode: string
    platformUserId: string
    platformUserName?: string
    platformAvatar?: string
    platformCookie?: string
  }) {
    // 检查平台账号是否已绑定
    const existingAccount = await MediaAccountServies.getFindFirst({
      id,
      applicationId,
      platformCode,
      platformUserId,
    })

    if (!existingAccount) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: '该平台账号未绑定到当前应用，请先备案。',
      })
    }

    const data: Prisma.AuthAccountUpdateInput = {}

    // 对Cookie进行哈希处理
    if (platformCookie) {
      data.platformCookieHash = await CookieService.encrypt(platformCookie)
    }
    if (platformUserName) {
      data.platformUserName = platformUserName
    }
    if (platformAvatar) {
      data.platformAvatar = platformAvatar
    }
    // 更新数据
    const authAccount = await db.authAccount.update({
      where: {
        id: existingAccount.id,
      },
      data,
    })
    await accountCache.del(existingAccount.id)

    return {
      id: authAccount.id,
    }
  }

  static async createOpenPlatformAccount(data: OpenPlatformAuthInput, applicationId: string) {
    const {
      platformCode,
      platformUserId,
      platformUserName,
      avatar,
      platformCookieHash,
      platformRefreshTokenExpiresAt,
      platformTokenExpiresAt,
      parentPlatformUserId,
    } = data

    const requiredCredits = await this.checkApplicationAccountQuota(platformCode, applicationId)

    // 使用事务创建账号并扣除点数
    const authAccount = await db.$transaction(async (tx) => {
      // 创建媒体账号绑定
      const account = await tx.authAccount.create({
        data: {
          applicationId: applicationId,
          platformCode,
          platformUserId,
          platformUserName,
          platformAvatar: avatar,
          platformCookieHash,
          authType: 'TOKEN',
          platformRefreshTokenExpiresAt,
          platformTokenExpiresAt,
          parentPlatformUserId,
        },
      })

      // 扣除对应点数
      await tx.application.update({
        where: { id: applicationId },
        data: {
          creditsUsed: {
            increment: requiredCredits,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId: applicationId,
          endpoint: '/open-media-accounts',
          method: 'POST',
          costType: 'CREDITS',
          costAmount: requiredCredits,
          statusCode: 200,
        },
      })

      return account
    })
    const { id, platformAvatar } = authAccount
    return {
      id,
      platformCode,
      platformUserId,
      platformUserName,
      platformAvatar,
    }
  }

  // 验证账号额度
  static async checkApplicationAccountQuota(platformCode: string, applicationId: string) {
    const platformConfigs = await systemConfigCache.getPlatformConfigs()
    if (!platformConfigs || !platformConfigs[platformCode]) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '无效的platformCode',
      })
    }

    // 获取该平台需要的点数
    const requiredCredits = await PlatformCreditsService.getCreditsPerAccount(applicationId, platformCode)

    // 验证账号额度
    if (!(await QuotaService.checkAccountQuota(applicationId, platformCode, requiredCredits))) {
      throw new TRPCError({
        code: 'PAYMENT_REQUIRED',
        message: '点数不足，无法绑定媒体账号。',
      })
    }

    return requiredCredits
  }

  // 删除媒体账号
  static async deleteMediaAccount(accountId: string, applicationId: string) {
    const existingAccount = await db.authAccount.findFirst({
      where: {
        id: accountId,
        applicationId: applicationId,
      },
    })

    if (!existingAccount) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '媒体账号不存在或无权限访问',
      })
    }
    // 删除缓存
    await accountCache.del(existingAccount.id)
    // 删除数据库记录和返还点数
    await db.$transaction(async (tx) => {
      await tx.authAccount.delete({
        where: { id: existingAccount.id },
      })

      const requiredCredits = await PlatformCreditsService.getCreditsPerAccount(
        applicationId,
        existingAccount.platformCode,
      )
      await tx.application.update({
        where: { id: applicationId },
        data: {
          creditsUsed: {
            decrement: requiredCredits,
          },
        },
      })
    })
    return { success: true }
  }

  // 媒体账号授权
  static async authorizeMediaAccount(
    authType: 'local' | 'cloud',
    platformCode: string,
    proxy: unknown,
    application: Application,
    fastify: FastifyInstance,
    extra: unknown,
    clientId?: string,
  ) {
    // 生成操作 id
    const operationId = cuid()

    if (authType === 'local') {
      //本地授权：通过Socket 发送授权指令
      if (!clientId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '本地授权需要提供clientId',
        })
      }

      // 发送Socket授权指令
      await localAuthHandler(clientId, platformCode, proxy, operationId, application, fastify, extra)

      return {
        message: '授权指令已发送到客户端，请在客户端完成授权',
        operationId,
      }
    } else {
      // 云授权：调用云授权服务
      const cloudAuthResponse = await remoteAuthHandler(platformCode, proxy, operationId, application, extra)

      return {
        qrCode: cloudAuthResponse.data.processedData,
        sessionId: cloudAuthResponse.data.id,
        operationId,
        message: '请扫描二维码完成授权',
      }
    }
  }
}
