import { generateOpenApiDocument } from 'trpc-to-openapi'
import { extendZodWithOpenApi } from 'zod-openapi'

import { openApiRouter } from './router/open-api'
import { z } from 'zod'

extendZodWithOpenApi(z)

// Generate OpenAPI schema document
export const openApiDocument = generateOpenApiDocument(openApiRouter, {
  title: 'tRPC OpenAPI',
  version: '1.0.0',
  baseUrl: 'http://localhost:3000/api/open',
})
