import { router } from '../../trpc'
import { deviceManager } from '../../lib/device-manager'
import { adminProtectedProcedure } from '@/procedure'
import { PaginationSchema } from '@coozf/zod'

export const deviceRouter = router({
  // 查询在线设备
  // GET /devices/online - 查询设备是否在线
  list: adminProtectedProcedure.input(PaginationSchema).query(async ({ input }) => {
    return await deviceManager.getDevicesPaginated(input.page, input.pageSize)
  }),
})
