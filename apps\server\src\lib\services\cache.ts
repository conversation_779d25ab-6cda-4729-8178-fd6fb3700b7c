import Redis from 'ioredis'
import { config } from '../../env'
import { db, SystemConfigCategory } from '@coozf/db'
import { SystemConfigService } from '../business/system-config'
import { keyBy, map } from 'lodash'

/**
 * 简化的缓存服务
 */
export class CacheService {
  private redis: Redis
  private defaultTTL: number = 3600

  constructor(redisConfig?: any, defaultTTL?: number) {
    this.redis =
      redisConfig ||
      new Redis({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        username: config.redis.username,
        db: config.redis.db,
      })
    if (defaultTTL) this.defaultTTL = defaultTTL
  }

  async get<T = any>(key: string): Promise<T | null> {
    const value = await this.redis.get(key)
    if (!value) return null
    try {
      return JSON.parse(value)
    } catch {
      return null
    }
  }

  async set(key: string, value: any, ttl: number = this.defaultTTL): Promise<void> {
    const serialized = JSON.stringify(value)
    await this.redis.setex(key, ttl, serialized)
  }

  async del(key: string): Promise<boolean> {
    const result = await this.redis.del(key)
    return result > 0
  }

  async exists(key: string): Promise<boolean> {
    return (await this.redis.exists(key)) === 1
  }

  async incr(key: string, ttl?: number): Promise<number> {
    const result = await this.redis.incr(key)
    if (ttl && result === 1) {
      await this.redis.expire(key, ttl)
    }
    return result
  }

  async getOrSet<T>(key: string, factory: () => Promise<T>, ttl: number = this.defaultTTL): Promise<T> {
    const cached = await this.get<T>(key)
    if (cached !== null) return cached

    const value = await factory()
    await this.set(key, value, ttl)
    return value
  }

  async clearPattern(pattern: string): Promise<number> {
    const keys = await this.scanKeys(pattern)
    return keys.length > 0 ? await this.redis.del(...keys) : 0
  }

  /**
   * 使用 SCAN 命令安全地查找匹配模式的键
   * 相比 KEYS 命令，SCAN 不会阻塞 Redis 服务器
   * @param pattern 匹配模式，如 'device:*'
   * @param count 每次扫描的数量，默认 100
   * @returns 匹配的键数组
   */
  async scanKeys(pattern: string, count: number = 100): Promise<string[]> {
    const keys: string[] = []
    let cursor = '0'

    do {
      const result = await this.redis.scan(cursor, 'MATCH', pattern, 'COUNT', count)
      cursor = result[0]
      keys.push(...result[1])
    } while (cursor !== '0')

    return keys
  }

  /**
   * 使用 SCAN 命令获取匹配模式的键值对
   * @param pattern 匹配模式
   * @param count 每次扫描的数量
   * @returns 键值对的 Map
   */
  async scanKeyValues<T = any>(pattern: string, count: number = 100): Promise<Map<string, T | null>> {
    const keys = await this.scanKeys(pattern, count)
    const result = new Map<string, T | null>()

    if (keys.length === 0) {
      return result
    }

    // 批量获取值，使用 pipeline 提高性能
    const pipeline = this.redis.pipeline()
    keys.forEach((key) => pipeline.get(key))

    const values = await pipeline.exec()

    keys.forEach((key, index) => {
      const value = values?.[index]?.[1] as string | null
      if (value) {
        try {
          result.set(key, JSON.parse(value))
        } catch {
          result.set(key, null)
        }
      } else {
        result.set(key, null)
      }
    })

    return result
  }

  async setNX(key: string, value: any, ttl?: number): Promise<boolean> {
    const serialized = JSON.stringify(value)
    if (ttl) {
      const result = await this.redis.set(key, serialized, 'EX', ttl, 'NX')
      return result === 'OK'
    } else {
      const result = await this.redis.setnx(key, serialized)
      return result === 1
    }
  }

  /**
   * 设置JSON数据 (别名方法，兼容现有调用)
   */
  async setJson(key: string, value: any, ttl: number = this.defaultTTL): Promise<void> {
    return this.set(key, value, ttl)
  }

  /**
   * 获取JSON数据 (别名方法，兼容现有调用)
   */
  async getJson<T = any>(key: string): Promise<T | null> {
    return this.get<T>(key)
  }

  /**
   * 获取Redis实例，用于直接操作
   */
  getRedisInstance(): Redis {
    return this.redis
  }
}

/**
 * Application 缓存
 */
export class ApplicationCache {
  private cache: CacheService
  private prefix = 'app:'
  private defaultTTL = 3600

  constructor(cache: CacheService) {
    this.cache = cache
  }

  async get<T>(appId: string): Promise<T | null> {
    const key = `${this.prefix}${appId}`
    return await this.cache.get<T>(key)
  }

  async set(appId: string, data: any, ttl: number = this.defaultTTL): Promise<void> {
    const key = `${this.prefix}${appId}`
    await this.cache.set(key, data, ttl)
  }

  async del(appId: string): Promise<boolean> {
    const key = `${this.prefix}${appId}`
    return await this.cache.del(key)
  }

  async getOrSet<T>(appId: string, factory: () => Promise<T>, ttl: number = this.defaultTTL): Promise<T> {
    const key = `${this.prefix}${appId}`
    return await this.cache.getOrSet(key, factory, ttl)
  }

  async clearAllApps(): Promise<void> {
    await this.cache.clearPattern(`${this.prefix}*`)
  }

  //  getOrSet 默认factory
  async getOrSetDefault(appId: string) {
    const key = `${this.prefix}${appId}`
    return this.cache.getOrSet(
      key,
      () =>
        db.application.findUnique({
          where: { id: appId },
          include: {
            user: true,
          },
        }),
      this.defaultTTL,
    )
  }
}

/**
 * 缓存的账号信息类型
 */
export interface CachedAccountInfo {
  id: string
  platformCode: string
  platformUserId: string
  platformUserName: string
  platformCookie: string
  platformLocalStorage: string | null
}

/**
 * 账号信息缓存
 * 按账号id为维度缓存完整的账号信息，包括解密后的cookie，避免重复查询和解密操作
 */
export class AccountCache {
  private cache: CacheService
  private prefix = 'account:'
  private defaultTTL = 60 * 60 * 24 // 1天

  constructor(cache: CacheService) {
    this.cache = cache
  }

  /**
   * 生成缓存键 - 基于账号id
   */
  private generateCacheKey(accountId: string): string {
    return `${this.prefix}${accountId}`
  }

  /**
   * 获取缓存的账号信息
   */
  async get(accountId: string): Promise<CachedAccountInfo | null> {
    const key = this.generateCacheKey(accountId)
    return await this.cache.get<CachedAccountInfo>(key)
  }

  /**
   * 设置账号信息到缓存
   */
  async set(accountInfo: CachedAccountInfo, ttl?: number): Promise<void> {
    const key = this.generateCacheKey(accountInfo.id)
    await this.cache.set(key, accountInfo, ttl || this.defaultTTL)
  }

  /**
   * 删除指定账号的缓存
   */
  async del(accountId: string): Promise<boolean> {
    const key = this.generateCacheKey(accountId)
    return await this.cache.del(key)
  }

  /**
   * 批量获取账号信息
   */
  async getMultiple(accountIds: string[]): Promise<Map<string, CachedAccountInfo | null>> {
    const results = new Map<string, CachedAccountInfo | null>()

    // 并发获取所有缓存
    const promises = accountIds.map(async (accountId) => {
      const cached = await this.get(accountId)
      results.set(accountId, cached)
      return { accountId, cached }
    })

    await Promise.all(promises)
    return results
  }

  /**
   * 批量设置账号信息
   */
  async setMultiple(accountInfos: CachedAccountInfo[], ttl?: number): Promise<void> {
    const promises = accountInfos.map((accountInfo) => this.set(accountInfo, ttl))
    await Promise.all(promises)
  }

  /**
   * 获取缓存结果，如果不存在则执行查询和解密并缓存
   */
  async getOrProcess(
    accountId: string,
    processFunction: () => Promise<CachedAccountInfo>,
  ): Promise<{ data: CachedAccountInfo; fromCache: boolean }> {
    // 先尝试从缓存获取
    const cached = await this.get(accountId)
    if (cached !== null) {
      return { data: cached, fromCache: true }
    }

    // 缓存未命中，执行查询和解密
    const result = await processFunction()

    // 存入缓存
    await this.set(result)

    return { data: result, fromCache: false }
  }

  /**
   * 清除指定账号列表的缓存
   */
  async clearAccounts(accountIds: string[]): Promise<number> {
    const promises = accountIds.map((accountId) => this.del(accountId))
    const results = await Promise.all(promises)
    return results.filter(Boolean).length
  }

  /**
   * 清除所有账号缓存
   */
  async clearAll(): Promise<number> {
    return await this.cache.clearPattern(`${this.prefix}*`)
  }
}

export class configCache {
  private cache: CacheService
  private prefix = 'systemConfig'
  private defaultTTL = 3600 * 24
  constructor(cache: CacheService) {
    this.cache = cache
  }
  async getPlatformConfigs() {
    const key = `${this.prefix}:PLATFORM_CONFIG`
    return this.cache.getOrSet(
      key,
      async () => {
        const res = await SystemConfigService.getConfigValuesByCategory('PLATFORM_CONFIG')
        return keyBy<{ key: string; platformName: string; platformType: string }>(
          res.map((item) => {
            return { key: item.key, ...(item.value as { platformName: string; platformType: string }) }
          }),
          'key',
        )
      },
      this.defaultTTL,
    )
  }

  async getCreditsConfigs() {
    const key = `${this.prefix}:CREDITS_CONFIG`
    return this.cache.getOrSet(
      key,
      async () => {
        const res = await SystemConfigService.getConfigValuesByCategory('CREDITS_CONFIG')
        return keyBy<{ creditsPerAccount: number; type: string }>(map(res, 'value'), 'type')
      },
      this.defaultTTL,
    )
  }

  async getCrawlerProxys() {
    const key = `${this.prefix}:CRAWLER_PROXY`
    return await this.cache.getOrSet(
      key,
      async () => {
        const res = await SystemConfigService.getConfigValuesByCategory('CRAWLER_PROXY')
        return keyBy<{ value: string[]; key: string }>(
          map(res, (item) => ({ key: item.key, value: item.value })),
          'key',
        )
      },
      this.defaultTTL,
    )
  }

  // 清除分类相关缓存
  async clearCategoryCache(category: SystemConfigCategory) {
    const key = `${this.prefix}:${category}`
    return await this.cache.del(key)
  }
}

// 创建默认实例
export const cacheService = new CacheService()
export const applicationCache = new ApplicationCache(cacheService)
export const accountCache = new AccountCache(cacheService)
export const systemConfigCache = new configCache(cacheService)
