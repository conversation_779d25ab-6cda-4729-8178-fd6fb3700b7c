import { authRouter } from '@/router/auth'
import { userRouter } from '@/router/user'
import { applicationRouter } from '@/router/application'
import { orderRouter } from '@/router/order'
import { deviceRouter } from '@/router/device'
import { publishRouter } from '@/router/publish'
import { ossRouter } from '@/router/oss'
// import { socketRouter } from '@/router/socket'
// import { oauth2Router } from './oauth2'
import { router, t } from '@/trpc'
import { authAccountRouter } from './auth-account'
import { userClientVersionRouter } from '@/router/client-version'
import { xiaohongshuRouter } from './xiaohongshu'

export const appRouter = router({
  auth: authRouter,
  user: userRouter,
  application: applicationRouter,
  order: orderRouter,
  device: deviceRouter,
  publish: publishRouter,
  oss: ossRouter,
  // socket: socketRouter,
  authAccount: authAccountRouter,
  clientVersion: userClientVersionRouter,
  xiaohongshu: xiaohongshuRouter
})
// export type definition of API
export type AppRouter = typeof appRouter

export const createCaller = t.createCallerFactory(appRouter)
