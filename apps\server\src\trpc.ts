import { initTRPC } from '@trpc/server'
import { ZodError } from 'zod'
import type { OpenApiMeta } from 'trpc-to-openapi'
import superjson from 'superjson'
import type { Context } from './context'
import { isDev } from './env'
import { ResponseWrapper } from './lib/utils/response'
import { tlsService } from '@coozf/huoshan'

export const t = initTRPC
  .meta<OpenApiMeta>()
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error } = opts
      tlsService.error(error.message, { error })
      if (error.code === 'INTERNAL_SERVER_ERROR' && !isDev) {
        return { ...shape, message: 'Internal server error' }
      }
      return {
        ...shape,
        data: {
          ...shape.data,
          zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
        },
      }
    },
  })

export const responseWrapperMiddleware = t.middleware(async ({ next, ctx }) => {
  const result = await next()

  // 如果是公共 API 且操作成功，包装响应
  if (result.ok && ctx.isPublicApi) {
    return {
      ok: true,
      data: ResponseWrapper.success(result.data),
      marker: result.marker,
    }
  }

  // 其他情况直接返回原始结果
  return result
})

export const publicProcedure = t.procedure

export const publicApiProcedure = t.procedure.use(responseWrapperMiddleware)
export const router = t.router
