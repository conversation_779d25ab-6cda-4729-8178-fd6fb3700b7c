import { Link } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Users, Zap } from 'lucide-react'
import type { Application } from '../types'

interface DataStatisticsProps {
  application: Application
  applicationId: string
}

export function DataStatistics({ application, applicationId }: DataStatisticsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>数据</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-6">
          {/* 账号配额统计 */}
          <div className="bg-card rounded-lg p-4 border">
            <div className="space-y-1">
              <div className="text-xl font-bold text-foreground">
                {application.creditsUsed} / {application.creditsQuota}
              </div>
              <div className="text-sm text-muted-foreground flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  账号点数（{application.authAccountsCount}）
                </div>
                <Link
                  to="/apps/$id/accounts"
                  params={{ id: applicationId }}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  查看
                </Link>
              </div>
            </div>
          </div>

          {/* 流量配额统计 */}
          <div className="bg-card rounded-lg p-4 border">
            <div className="space-y-1">
              <div className="text-xl font-bold text-foreground">
                {application.trafficUsedGB} / {application.trafficQuotaGB}
              </div>
              <div className="text-sm text-muted-foreground flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Zap className="h-4 w-4" />
                  流量使用情况
                </div>
                <Link
                  to="/apps/$id/content"
                  params={{ id: applicationId }}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  明细
                </Link>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
