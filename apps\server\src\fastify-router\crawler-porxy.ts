import type { FastifyPluginAsync } from 'fastify'
import fastifyHttpProxy from '@fastify/http-proxy'
import { verifyPlatformUserId, verifyToken } from '@/procedure'
import { env } from '@/env'
import { createHash } from 'crypto'
import { ApiError, PublishService, ResponseWrapper, sessionService, logger, SystemConfigService } from '@/lib'
import { clientIdSchema, CreatePublishTaskSchema, CreateVideoTaskSchema } from '@coozf/zod'
import { localLocationHandler, localPublishHandler } from '@/lib/business/crawler-local'

declare module 'fastify' {
  // 扩展 FastifyRequest 接口
  export interface FastifyRequest {
    user: Awaited<ReturnType<typeof verifyToken>>
  }
}

// 注意：配置已移至数据库，通过SystemConfigService动态获取

export const crawlerProxyRoutes: FastifyPluginAsync = async (fastify) => {
  // 注册代理插件，指向真正的目标服务
  fastify.register(fastifyHttpProxy, {
    upstream: env.CRAWLER_URL,
    prefix: '/',
    rewritePrefix: '/',
    disableRequestLogging: false,
    preValidation: async (request, reply) => {
      const userAppData = await verifyToken(request.headers.authorization)
      const timestamp = Date.now()
      const tokenString = `${timestamp}yixiaoer_cloud_publish`
      const md5Hash = createHash('md5').update(tokenString).digest('hex')
      request.headers.token = md5Hash
      request.headers.timestamp = timestamp.toString()
      // 动态获取不需要accountId的接口黑名单
      const accountIdBlackList = await SystemConfigService.getCrawlerProxyAccountIdBlacklist()
      if (!accountIdBlackList.some((blacklistPath) => request.url.includes(blacklistPath))) {
        const platform = await verifyPlatformUserId(request, userAppData.application.id)
        if (request.body && typeof request.body === 'object') {
          ;(request.body as any).platform = platform
        }
      }

      let recordId: string = ''
      // 在这里进行请求的预处理，例如验证请求参数
      if (request.method === 'POST' && request.url.includes('/platform-publish/tasks')) {
        // 检查请求体中的发布渠道
        const body = CreatePublishTaskSchema.parse(request.body)
        const publishChannel = body.publishChannel
        const clientId = body.clientId
        // 如果是本地发布
        if (publishChannel === 'local') {
          if (!clientId) {
            throw new ApiError(40000, '发布渠道为本地时，必须提供 clientId')
          }
          logger.info('开始本地发布', {
            type: 'publish',
            channel: 'local',
            clientId,
            userId: userAppData.user.id,
            applicationId: userAppData.application.id,
          })
          const sessionToken = await sessionService.generateSessionToken(
            userAppData.user.id,
            userAppData.application.id,
            recordId,
          )

          await localPublishHandler(clientId, sessionToken, request.body, fastify)
          reply.send(ResponseWrapper.success('本地发布任务已发送'))
        } else if (body.publishType === 'video') {
          logger.info('开始计费', {
            type: 'publish',
            publishType: 'video',
            tasksCount: body.tasks.length,
            userId: userAppData.user.id,
            applicationId: userAppData.application.id,
          })
          const tasks = body.tasks.map((task) => {
            return CreateVideoTaskSchema.parse(task)
          })
          const res = await PublishService.createPublishRecord(userAppData.application.id, tasks)
          recordId = res.id
        }
      }
      if (request.method === 'POST' && request.url.includes('/config-data/location-tasks')) {
        const body = clientIdSchema.parse(request.body)
        if (body.clientId) {
          logger.info('开始本地定位', {
            type: 'location',
            clientId: body.clientId,
            userId: userAppData.user.id,
            applicationId: userAppData.application.id,
          })
          const res = await localLocationHandler(body.clientId, request.body, fastify)
          if (res.success) {
            reply.send(ResponseWrapper.success(res.data))
          } else {
            reply.send(ResponseWrapper.error(40000, res.error || '未知错误'))
          }
        }
      }
      // 动态获取需要发送session token的接口白名单
      const sessionTokenWhiteList = await SystemConfigService.getCrawlerProxySessionTokenWhitelist()
      if (sessionTokenWhiteList.some((whitelistPath) => request.url.includes(whitelistPath))) {
        const sessionToken = await sessionService.generateSessionToken(
          userAppData.user.id,
          userAppData.application.id,
          recordId,
        )
        request.headers.authorization = sessionToken
      }
    },
  })
}
