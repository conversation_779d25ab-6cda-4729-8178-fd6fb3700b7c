import crypto from 'crypto'
import axios from 'axios'
import { nanoid } from 'nanoid'
import { env, isModePre } from '../../env'
import { logger } from './logger'
import { CacheService } from './cache'

// 支持的OAuth平台
export const SUPPORTED_PLATFORMS = ['xiaohongshu', 'douyin', 'kuaishou', 'weibo'] as const
export type PlatformType = (typeof SUPPORTED_PLATFORMS)[number]

// 平台配置接口
interface PlatformConfig {
  clientId: string
  clientSecret: string
  authorizeUrl: string
  tokenUrl: string
  apiBase: string
  scopes: string
}

// OAuth响应类型
export interface OAuthTokenResponse {
  success: boolean
  data?: {
    user_id: string
    access_token: string
    refresh_token: string
    expires_in: number
    refresh_token_expires_in: number
    scope: string
    name?: string
    avatar?: string
    accountRole?: string
    [key: string]: unknown
  }
  error?: string
  message?: string
}

// 用户信息响应类型
export interface UserInfoResponse {
  success: boolean
  data?: {
    user_id: string
    nickname?: string
    avatar?: string
    [key: string]: unknown
  }
  error?: string
  message?: string
}

/**
 * OAuth服务
 */
export class OAuthService {
  private cacheService: CacheService

  constructor(cacheService?: CacheService) {
    this.cacheService = cacheService || new CacheService()
  }

  private static readonly PLATFORM_CONFIGS: Record<PlatformType, PlatformConfig> = {
    xiaohongshu: {
      clientId: env.XIAOHONGSHU_CLIENT_KEY,
      clientSecret: env.XIAOHONGSHU_CLIENT_SECRET,
      authorizeUrl: 'https://ad-market.xiaohongshu.com/auth',
      tokenUrl: 'https://adapi.xiaohongshu.com/api/open/oauth2/access_token',
      apiBase: 'https://adapi.xiaohongshu.com',
      scopes: '["third_im","three_im_leads_push"]',
    },
    douyin: {
      clientId: env.DOUYIN_CLIENT_KEY, // 暂时留空，待配置
      clientSecret: env.DOUYIN_CLIENT_SECRET,
      authorizeUrl: 'https://open.douyin.com/platform/oauth/connect',
      tokenUrl: 'https://open.douyin.com/oauth/access_token',
      apiBase: 'https://open.douyin.com',
      scopes:
        'user_info,renew_refresh_token,video.list.bind,video.data.bind,item.comment,im.group_fans.create_list,im.direct_message,im.group_message,tool.image.upload,op.business.status,im.recall_message,user.intention,im.message_card',
    },
    kuaishou: {
      clientId: '', // 暂时留空，待配置
      clientSecret: '',
      authorizeUrl: 'https://open.kuaishou.com/oauth2/authorize',
      tokenUrl: 'https://open.kuaishou.com/oauth2/access_token',
      apiBase: 'https://open.kuaishou.com',
      scopes: '["user_info","video.publish"]',
    },
    weibo: {
      clientId: '', // 暂时留空，待配置
      clientSecret: '',
      authorizeUrl: 'https://api.weibo.com/oauth2/authorize',
      tokenUrl: 'https://api.weibo.com/oauth2/access_token',
      apiBase: 'https://api.weibo.com',
      scopes: '["email","direct_messages_read"]',
    },
  }

  /**
   * 获取平台配置
   */
  static getPlatformConfig(platform: PlatformType): PlatformConfig {
    const config = this.PLATFORM_CONFIGS[platform]
    if (!config.clientId || !config.clientSecret) {
      throw new Error(`平台 ${platform} 配置不完整，请检查环境变量`)
    }
    return config
  }

  /**
   * 生成OAuth状态参数
   * 格式: appId:platform:timestamp:random
   */
  static generateOAuthState(appId: string, platform: PlatformType, customState?: string): string {
    const timestamp = Date.now()
    const random = nanoid(8)
    const baseState = `${appId}:${platform}:${timestamp}:${random}`

    if (customState) {
      return `${baseState}:${customState}`
    }

    return baseState
  }

  /**
   * 解析OAuth状态参数
   */
  static parseOAuthState(state: string): {
    appId: string
    platform: PlatformType
    timestamp: number
    random: string
    customState?: string
  } | null {
    const parts = state.split(':')
    if (parts.length < 4) {
      return null
    }

    const [appId, platform, timestampStr, random, ...customStateParts] = parts
    const timestamp = parseInt(timestampStr || '', 10)

    if (
      !appId ||
      !platform ||
      !timestampStr ||
      !random ||
      !SUPPORTED_PLATFORMS.includes(platform as PlatformType) ||
      isNaN(timestamp)
    ) {
      return null
    }

    return {
      appId,
      platform: platform as PlatformType,
      timestamp,
      random,
      customState: customStateParts.length > 0 ? customStateParts.join(':') : undefined,
    }
  }

  /**
   * 生成平台授权URL
   */
  static generateAuthorizeUrl(platform: PlatformType, redirectUri: string, state: string): string {
    const config = this.getPlatformConfig(platform)
    const finalScope = config.scopes || 'basic_info'

    const params = new URLSearchParams()

    // 各平台参数名称可能不同，需要适配
    switch (platform) {
      case 'xiaohongshu':
        params.set('appId', config.clientId)
        params.set('redirectUri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        break

      case 'douyin':
        params.set('client_key', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break

      case 'kuaishou':
        params.set('app_id', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break

      case 'weibo':
        params.set('client_id', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break
    }

    const url = new URL(config.authorizeUrl)
    url.search = params.toString()

    return url.toString()
  }

  /**
   * 获取小红书用户信息
   * 通过授权码获取访问令牌（仅实现小红书，其他平台待后续扩展）
   */
  static async getXiaohongshuUserInfo(platform: PlatformType, authCode: string): Promise<OAuthTokenResponse> {
    if (platform !== 'xiaohongshu' && platform !== 'douyin') {
      return {
        success: false,
        error: `暂不支持 ${platform} 平台`,
      }
    }

    const config = this.getPlatformConfig(platform)

    try {
      const requestData = {
        app_id: config.clientId,
        secret: config.clientSecret,
        auth_code: authCode,
      }

      const response = await axios.post(config.tokenUrl, requestData)

      logger.info('小红书用户信息', {
        type: 'oauth',
        platform,
        data: response.data,
      })

      const advertisers = response.data.data.approval_advertisers
      const userInfo = advertisers.length > 0 ? advertisers[0] : null

      if (response.data.success) {
        return {
          success: true,
          data: {
            user_id: response.data.data.user_id,
            access_token: response.data.data.access_token,
            refresh_token: response.data.data.refresh_token,
            expires_in: response.data.data.access_token_expires_in,
            refresh_token_expires_in: response.data.data.refresh_token_expires_in,
            scope: response.data.data.scope,
            name: userInfo.advertiser_name,
          },
        }
      } else {
        return {
          success: false,
          error: response.data.msg || '获取访问令牌失败',
        }
      }
    } catch (error) {
      logger.error('OAuth获取访问令牌失败', {
        type: 'oauth',
        platform,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
      })
      return {
        success: false,
        error: `获取访问令牌失败: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }

  /**
   * 获取抖音应用token缓存
   */
  async getDouyinAppToken(id: string): Promise<string | null> {
    const cacheKey = `douyin:app_token:${id}`
    const cachedToken = await this.cacheService.get(cacheKey)
    if (cachedToken) {
      return cachedToken
    } else {
      if (isModePre) {
        // 如果是测试环境，则去青豆云官网去获取token
        const random = nanoid(16)
        const saltStr = 'uqLwd3AEudQuVgQwoLfzEYpsaM8bsPRy'
        const encryptedData = crypto.pbkdf2Sync(random, saltStr, 1000, 64, 'sha512').toString('hex')

        const result = await axios.post('https://www.qdy.com/api/users/douyin/client-token', {
          random: random,
          encryptedData,
        })

        if (result.data.data) {
          await this.cacheService.set(cacheKey, result.data.data, 7200000)
          return result.data.data
        }
      } else {
        const result = await axios.post('https://open.douyin.com/oauth/client_token/', {
          app_key: env.DOUYIN_CLIENT_KEY,
          app_secret: env.DOUYIN_CLIENT_SECRET,
          grant_type: 'client_credential',
        })

        if (result.data.data.access_token) {
          await this.cacheService.set(cacheKey, result.data.data.access_token, result.data.data.expires_in * 1000)
          return result.data.access_token
        }
      }
    }

    return null
  }

  /**
   * 获取抖音用户信息
   */
  static async getDouyinUserInfo(platform: PlatformType, authCode: string): Promise<OAuthTokenResponse> {
    if (platform !== 'douyin') {
      return {
        success: false,
        error: `暂不支持 ${platform} 平台`,
      }
    }

    const config = this.getPlatformConfig(platform)

    try {
      const requestData = {
        client_key: config.clientId,
        client_secret: config.clientSecret,
        code: authCode,
        grant_type: 'authorization_code',
      }

      const response = await axios.post(config.tokenUrl, requestData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      })

      logger.info('OAuth获取抖音访问令牌成功', {
        type: 'oauth',
        platform,
        data: response.data,
      })

      if (response.data.data.error_code === 0) {
        const userInfo = await axios.post('https://open.douyin.com/oauth/userinfo/', {
          access_token: response.data.data.access_token,
          open_id: response.data.data.open_id,
        })

        logger.info('OAuth获取用户信息成功', {
          type: 'oauth',
          platform,
          data: userInfo.data,
        })

        if (userInfo.data.data.error_code === 0) {
          return {
            success: true,
            data: {
              user_id: response.data.data.open_id,
              access_token: response.data.data.access_token,
              refresh_token: response.data.data.refresh_token,
              expires_in: response.data.data.expires_in,
              refresh_token_expires_in: response.data.data.refresh_expires_in,
              name: userInfo.data.data.nickname || '',
              avatar: userInfo.data.data.avatar_larger || '',
              accountRole: userInfo.data.data.account_role || '',
              scope: response.data.data.scope,
            },
          }
        } else {
          return {
            success: false,
            error: userInfo.data.data.description || '获取用户信息失败',
          }
        }
      } else {
        return {
          success: false,
          error: response.data.data.description || '获取访问令牌失败',
        }
      }
    } catch (error) {
      logger.error('OAuth获取用户信息失败', {
        type: 'oauth',
        platform,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
      })
      return {
        success: false,
        error: `获取用户信息失败: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }
}

export const oauthService = new OAuthService()
