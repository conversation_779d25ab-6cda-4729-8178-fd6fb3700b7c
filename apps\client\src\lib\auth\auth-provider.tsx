import { useEffect, type <PERSON>actNode } from 'react'
import { router } from '@/router'
import { AuthContext, type AuthContextType } from './auth-context'
import { authClient } from './auth-client'

export function AuthProvider({ children }: { children: ReactNode }) {
  const session = authClient.useSession()

  const logout = async () => {
    await authClient.signOut()
  }

  useEffect(() => {
    if (!session.data && !session.isPending) {
      router.navigate({ to: '/login' })
    }
  }, [session.data])

  const contextValue: AuthContextType = {
    isAuthenticated: !!session.data,
    user: session.data?.user,
    isLoading: session.isPending,
    logout,
  }

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}
