import { CustomTooltip } from './custom-tooltip';
import { cn } from '@/lib/utils';
import { CircleHelp } from 'lucide-react';
export function HelpTooltip({
  title,
  className,
  ...props
}: { title: string } & React.ComponentProps<typeof CircleHelp>) {
  return (
    <CustomTooltip tooltip={title}>
      <CircleHelp
        {...props}
        className={cn('w-4 h-4 text-muted-foreground', className)}
      />
    </CustomTooltip>
  );
}
