import { useState } from 'react'
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@coozf/ui/components/chart'
import { AreaChart, Area, XAxis, YAxis, CartesianGrid } from 'recharts'
import type { TrendData } from '../types'
import { CHART_CONFIG } from '../constants'

interface TrendsChartProps {
  data: TrendData[]
}

export function TrendsChart({ data }: TrendsChartProps) {
  const [activeChart, setActiveChart] = useState<keyof typeof CHART_CONFIG | "all">("all")

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[400px] text-muted-foreground">
        <p>暂无数据</p>
      </div>
    )
  }

  // 数据处理：确保数值正确
  const chartData = data.map((item) => ({
    ...item,
    accountCount: Number(item.accountCount) || 0,
    trafficGB: Number(item.trafficGB) || 0,
    apiCalls: Number(item.apiCalls) || 0,
  }))

  const total = {
    accountCount: chartData.reduce((acc, curr) => acc + curr.accountCount, 0),
    trafficGB: chartData.reduce((acc, curr) => acc + curr.trafficGB, 0),
    apiCalls: chartData.reduce((acc, curr) => acc + curr.apiCalls, 0),
  }

  return (
    <div className="space-y-6">
      {/* 标题和统计概览 */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium">30日数据趋势</h3>
        <div className="flex flex-wrap gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[hsl(var(--chart-1))]" />
            <span className="text-muted-foreground">总新增账号:</span>
            <span className="font-medium">{total.accountCount} 个</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[hsl(var(--chart-2))]" />
            <span className="text-muted-foreground">总使用流量:</span>
            <span className="font-medium">{total.trafficGB.toFixed(2)} GB</span>
          </div>
          {total.apiCalls > 0 && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[hsl(var(--chart-3))]" />
              <span className="text-muted-foreground">总API调用:</span>
              <span className="font-medium">{total.apiCalls} 次</span>
            </div>
          )}
        </div>
      </div>

      {/* 图表容器 */}
      <ChartContainer config={CHART_CONFIG}>
        <AreaChart
          accessibilityLayer
          data={chartData}
          margin={{
            left: 12,
            right: 12,
            top: 12,
          }}
        >
          <defs>
            <linearGradient id="fillAccountCount" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--chart-2)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--chart-2)"
                stopOpacity={0.1}
              />
            </linearGradient>
            <linearGradient id="fillTrafficGB" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--chart-1)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--chart-1)"
                stopOpacity={0.1}
              />
            </linearGradient>
          </defs>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="date"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            minTickGap={32}
            tickFormatter={(value) => {
              const date = new Date(value)
              return `${date.getMonth() + 1}/${date.getDate()}`
            }}
          />
          <YAxis tickLine={false} axisLine={false} tickMargin={8} />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent hideLabel />}
            labelFormatter={(label) => {
              const date = new Date(label)
              return `${date.getMonth() + 1}月${date.getDate()}日`
            }}
          />
          <Area
            dataKey="accountCount"
            type="natural"
            fill="url(#fillAccountCount)"
            fillOpacity={activeChart === "accountCount" || activeChart === "all" ? 0.6 : 0.1}
            stroke="var(--color-accountCount)"
            strokeWidth={2}
            stackId="a"
          />
          <Area
            dataKey="trafficGB"
            type="natural"
            fill="url(#fillTrafficGB)"
            fillOpacity={activeChart === "trafficGB" || activeChart === "all" ? 0.6 : 0.1}
            stroke="var(--color-trafficGB)"
            strokeWidth={2}
            stackId="b"
          />
          <ChartLegend content={<ChartLegendContent />} />
        </AreaChart>
      </ChartContainer>

      {/* 交互按钮 */}
      <div className="flex flex-wrap gap-2">
        <button
          className={`px-3 py-1.5 text-xs rounded-md border transition-colors ${
            activeChart === "all"
              ? "bg-primary text-primary-foreground border-primary"
              : "bg-background hover:bg-muted border-border"
          }`}
          onClick={() => setActiveChart("all")}
        >
          全部显示
        </button>
        <button
          className={`px-3 py-1.5 text-xs rounded-md border transition-colors ${
            activeChart === "accountCount"
              ? "bg-primary text-primary-foreground border-primary"
              : "bg-background hover:bg-muted border-border"
          }`}
          onClick={() => setActiveChart("accountCount")}
        >
          新增账号
        </button>
        <button
          className={`px-3 py-1.5 text-xs rounded-md border transition-colors ${
            activeChart === "trafficGB"
              ? "bg-primary text-primary-foreground border-primary"
              : "bg-background hover:bg-muted border-border"
          }`}
          onClick={() => setActiveChart("trafficGB")}
        >
          使用流量
        </button>
      </div>
    </div>
  )
}