import { z } from 'zod'
import { router } from '@/trpc'
import { VersionService } from '@/lib/business/version'
import { adminProtectedProcedure } from '@/procedure'
import { publicProcedure } from '@/trpc'
import { CheckUpdateSchema, CreateVersionSchema, UpdateVersionSchema, VersionListSchema } from '@coozf/zod'
import { applicationCache } from '@/lib/services/cache'
import { ClientVersionService } from '@/lib/business/client-version'

export const versionRouter = router({
  // 创建新版本（需要管理员权限）
  create: adminProtectedProcedure.input(CreateVersionSchema).mutation(async ({ ctx, input }) => {
    return await VersionService.createVersion(input, ctx.user.id)
  }),

  // 获取版本列表（需要管理员权限）
  list: adminProtectedProcedure.input(VersionListSchema).query(async ({ input }) => {
    return await VersionService.getVersionList(input)
  }),

  // 更新版本信息（需要管理员权限）
  update: adminProtectedProcedure.input(UpdateVersionSchema).mutation(async ({ input }) => {
    const { id, ...updateData } = input
    return await VersionService.updateVersion(id, updateData)
  }),

  // 删除版本
  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ input }) => {
    return await VersionService.deleteVersion(input.id)
  }),

  // 检查更新（公开接口）
  checkUpdate: publicProcedure.input(CheckUpdateSchema).query(async ({ input }) => {
    return await VersionService.checkForUpdate(input)
  }),

  // 重新构建 oem
  buildOem: adminProtectedProcedure
    .input(
      z.object({
        clientVersionId: z.string().min(1, '客户端版本ID不能为空'),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const clientVersion = await ctx.db.clientVersion.findUnique({
        where: { id: input.clientVersionId },
        select: {
          platform: true,
          forceUpdate: true,
          description: true,
          application: {
            select: { id: true, name: true, oemConfig: true, brandName: true },
          },
          baseVersion: {
            select: { id: true, version: true },
          },
        },
      })
      if (!clientVersion) throw new Error('客户端版本不存在')

      const { forceUpdate, description, platform, baseVersion, application } = clientVersion
      ClientVersionService.createClientVersion(
        {
          applicationId: application.id,
          baseVersionId: baseVersion.id,
          platform,
          forceUpdate,
          description: description!,
        },
        application,
      )
    }),
  // 发布版本
  publish: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    return await VersionService.publishDesktopVersion(input.id, ctx.user.id)
  }),
})
