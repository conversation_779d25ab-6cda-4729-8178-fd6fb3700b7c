import { useController, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { Button } from '@coozf/ui/components/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@coozf/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { Textarea } from '@coozf/ui/components/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Switch } from '@coozf/ui/components/switch'
import { CreateVersionSchema, type CreateVersionInput } from '@coozf/zod'
import { LoadingButton } from '@coozf/ui/components/loading'
import { trpcClient } from '@/lib/trpc'

interface CreateVersionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateVersionInput) => void
  isPending?: boolean
}

export function CreateVersionDialog({ open, onOpenChange, onSubmit, isPending = false }: CreateVersionDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const form = useForm<CreateVersionInput>({
    resolver: zodResolver(CreateVersionSchema),
    defaultValues: {
      forceUpdate: false,
      type: 'DESKTOP',
    },
  })

  const fileUrlControl = useController({
    control: form.control,
    name: 'fileUrl',
  })

  const handleSubmit = async (data: CreateVersionInput) => {
    if (data.type !== 'DESKTOP' && selectedFile) {
      const { serviceUrl, url } = await trpcClient.oss.getUploadSignatureUrl.query({
        name: selectedFile.name,
        version: data.version,
        type: data.type,
      })
      const res = await fetch(serviceUrl, {
        method: 'PUT',
        body: selectedFile,
      })
      if (!res.ok) {
        throw new Error('上传文件失败')
      }
      data.fileUrl = url
    }
    onSubmit(data)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      fileUrlControl.field.onChange(URL.createObjectURL(file))
      setSelectedFile(file)
    }
  }

  const watchedType = form.watch('type')

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>发布新版本</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="version"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>版本号</FormLabel>
                  <FormControl>
                    <Input placeholder="1.0.1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>类型</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="DESKTOP">桌面端</SelectItem>
                      <SelectItem value="BROWSER_PLUGIN">浏览器插件</SelectItem>
                      <SelectItem value="RPA">RPA</SelectItem>
                      <SelectItem value="CRAWLER">爬虫脚本</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedType === 'DESKTOP' && (
              <FormField
                control={form.control}
                name="platform"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>平台</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择平台" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="WIN">Windows</SelectItem>
                        <SelectItem value="MAC">macOS</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {watchedType !== 'DESKTOP' && (
              <div className="space-y-4">
                <div>
                  <div className="space-y-2">
                    <FormLabel>选择文件</FormLabel>
                    <Input type="file" onChange={handleFileChange} />
                    {selectedFile && <p className="text-sm text-muted-foreground">已选择: {selectedFile.name}</p>}
                  </div>
                </div>
              </div>
            )}

            {watchedType === 'DESKTOP' && (
              <div className="text-sm text-muted-foreground bg-blue-50 p-3 rounded-md">
                <p>桌面端版本会自动检测OSS中的构建产物并生成下载地址</p>
              </div>
            )}

            <FormField
              control={form.control}
              name="forceUpdate"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>强制更新</FormLabel>
                    <div className="text-sm text-muted-foreground">客户端必须更新到此版本</div>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>版本公告</FormLabel>
                  <FormControl>
                    <Textarea placeholder="此版本的更新内容..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <LoadingButton isPending={isPending} type="submit" disabled={isPending}>
                发布
              </LoadingButton>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
