import { Button } from '@coozf/ui/components/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@coozf/ui/components/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Separator } from '@coozf/ui/components/separator'
import { CheckCircle, Loader2, XCircle, Clock, Calendar, CreditCard, Package, Hash, User, Building2 } from 'lucide-react'
import type { RouterOutput } from '@/lib/trpc'

type OrderItem = RouterOutput['order']['list']['data'][number]

export const OrderDetails = ({
  open,
  onOpenChange,
  selectedOrder,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedOrder: OrderItem | null
}) => {
  if (!selectedOrder) return null

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default'
      case 'PENDING':
        return 'secondary'
      case 'CANCELLED':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-orange-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return '已完成'
      case 'PENDING':
        return '待处理'
      case 'CANCELLED':
        return '已取消'
      default:
        return status
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'BANK_TRANSFER':
        return '银行转账'
      case 'ALIPAY':
        return '支付宝'
      case 'WECHAT':
        return '微信支付'
      default:
        return method
    }
  }

  const getQuotaTypeText = (type: string) => {
    switch (type) {
      case 'ACCOUNT':
        return '账号配额'
      case 'TRAFFIC':
        return '流量配额'
      default:
        return type
    }
  }

  const getOrderTypeText = (type: string) => {
    switch (type) {
      case 'PURCHASE':
        return '购买'
      case 'GIFT':
        return '赠送'
      default:
        return type
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>订单详情</DialogTitle>
          <DialogDescription>订单号: {selectedOrder.orderNo}</DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hash className="h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">订单号</p>
                    <p className="text-sm text-muted-foreground">{selectedOrder.orderNo}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(selectedOrder.status)}
                    <div>
                      <p className="text-sm font-medium">状态</p>
                      <Badge variant={getStatusBadgeVariant(selectedOrder.status)}>
                        {getStatusText(selectedOrder.status)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">应用ID</p>
                    <p className="text-sm text-muted-foreground">{selectedOrder.applicationId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">订单类型</p>
                    <p className="text-sm text-muted-foreground">{getOrderTypeText(selectedOrder.type)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 配额信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                配额信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">配额类型</p>
                  <p className="text-sm text-muted-foreground">{getQuotaTypeText(selectedOrder.quotaType)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">配额数量</p>
                  <p className="text-sm text-muted-foreground">{selectedOrder.quotaAmount?.toString() || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 支付信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                支付信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">金额</p>
                  <p className="text-lg font-bold">¥{selectedOrder.amount.toString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">支付方式</p>
                  <p className="text-sm text-muted-foreground">{getPaymentMethodText(selectedOrder.paymentMethod)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                时间信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">创建时间</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(selectedOrder.createdAt).toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">更新时间</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(selectedOrder.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 备注信息 */}
          {selectedOrder.remarks && (
            <Card>
              <CardHeader>
                <CardTitle>备注</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{selectedOrder.remarks}</p>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}