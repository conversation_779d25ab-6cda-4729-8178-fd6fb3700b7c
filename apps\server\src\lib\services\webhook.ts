import crypto from 'crypto'
import axios, { AxiosError } from 'axios'
import { logger } from './logger'

export interface WebhookPayload {
  type: string
  event?: string
  data: unknown
  timestamp: string
}

/**
 * Webhook通知服务
 */
export class WebhookService {
  /**
   * 发送带有HMAC签名的Webhook通知
   * @param webhookUrl 目标URL
   * @param webhookSecret 用于签名的密钥
   * @param type 平台类型
   * @param data 发送的数据
   * @param event 事件类型
   * @param headers 自定义请求头
   * @returns 是否发送成功
   */
  static async sendNotification(
    webhookUrl: string,
    webhookSecret: string,
    type: string,
    data: unknown,
    event?: string,
    headers?: Record<string, string>,
  ): Promise<boolean> {
    if (!webhookUrl || !webhookSecret) {
      logger.warn('Webhook URL或Secret未配置，跳过发送', {
        type: 'webhook',
        hasUrl: !!webhookUrl,
        hasSecret: !!webhookSecret,
        eventType: type,
        event,
      })
      return false
    }

    try {
      const timestamp = Math.floor(Date.now() / 1000).toString()
      const payload: WebhookPayload = {
        type,
        event,
        data,
        timestamp,
      }

      const payloadString = JSON.stringify(payload)

      // 签名
      const stringToSign = `${timestamp}.${payloadString}`
      const signature = crypto.createHmac('sha256', webhookSecret).update(stringToSign).digest('hex')

      const response = await axios.post(webhookUrl, payloadString, {
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Timestamp': timestamp,
          'X-Webhook-Signature': signature,
          ...headers,
        },
        timeout: 10000, // 10秒超时
      })

      return response.status >= 200 && response.status < 300
    } catch (error) {
      if (error instanceof AxiosError) {
        logger.error('Webhook通知发送失败', {
          type: 'webhook',
          webhookUrl,
          eventType: type,
          event,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data,
        })
        return false
      }
      logger.error('Webhook通知发送失败', {
        type: 'webhook',
        webhookUrl,
        eventType: type,
        event,
        error: error instanceof Error ? error.message : error,
      })
      return false
    }
  }

  /**
   * 验证Webhook签名
   */
  static verifySignature(payload: string, timestamp: string, signature: string, secret: string): boolean {
    try {
      const stringToSign = `${timestamp}.${payload}`
      const expectedSignature = crypto.createHmac('sha256', secret).update(stringToSign).digest('hex')

      return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'))
    } catch {
      return false
    }
  }

  /**
   * 批量发送Webhook通知
   */
  static async sendBatchNotifications(
    notifications: Array<{
      webhookUrl: string
      webhookSecret: string
      type: string
      data: unknown
      event?: string
    }>,
  ): Promise<{ success: number; failed: number; results: boolean[] }> {
    const results = await Promise.all(
      notifications.map((notification) =>
        this.sendNotification(
          notification.webhookUrl,
          notification.webhookSecret,
          notification.type,
          notification.data,
          notification.event,
        ),
      ),
    )

    const success = results.filter((r) => r).length
    const failed = results.length - success

    return { success, failed, results }
  }
}
