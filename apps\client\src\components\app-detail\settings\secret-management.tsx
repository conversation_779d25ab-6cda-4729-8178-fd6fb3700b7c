import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Label } from '@coozf/ui/components/label'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@coozf/ui/components/alert-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { Key, RotateCcw, AlertTriangle, Copy } from 'lucide-react'
import { useSecretManagement } from '../hooks/useSecretManagement'

interface SecretManagementProps {
  applicationId: string
}

export function SecretManagement({ applicationId }: SecretManagementProps) {
  const {
    showRegenerateConfirm,
    setShowRegenerateConfirm,
    showNewSecretDialog,
    setShowNewSecretDialog,
    newSecret,
    regenerateSecretMutation,
    handleRegenerateSecret,
    handleNewSecretDialogClose,
    copyToClipboard,
  } = useSecretManagement(applicationId)

  return (
    <>
      {/* 密钥管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            密钥管理
          </CardTitle>
          <CardDescription>管理您的应用密钥，用于API调用认证</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>当前密钥状态</Label>
            <div className="flex items-center justify-between p-3 bg-muted rounded-md">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">密钥已配置</span>
                <code className="text-sm text-muted-foreground">sk_••••••••••••••••</code>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRegenerateConfirm(true)}
                disabled={regenerateSecretMutation.isPending}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                {regenerateSecretMutation.isPending ? '生成中...' : '重新生成'}
              </Button>
            </div>
          </div>
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-amber-500 mt-0.5" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">重新生成注意事项：</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>重新生成后，旧密钥将立即失效</li>
                  <li>请确保更新所有使用该密钥的应用和服务</li>
                  <li>新密钥仅在生成时显示一次，请妥善保存</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 重新生成密钥确认对话框 */}
      <AlertDialog open={showRegenerateConfirm} onOpenChange={setShowRegenerateConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-amber-500" />
              确认重新生成密钥
            </AlertDialogTitle>
            <AlertDialogDescription>
              此操作将重新生成应用密钥，旧密钥将立即失效。请确保您已准备好更新所有使用该密钥的应用和服务。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRegenerateSecret}
              className="bg-destructive text-white hover:bg-destructive/90"
            >
              确认重新生成
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 新密钥展示对话框 */}
      <Dialog open={showNewSecretDialog} onOpenChange={setShowNewSecretDialog}>
        <DialogContent className="max-w-md" onPointerDownOutside={(e: Event) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="w-5 h-5 text-green-500" />
              新密钥已生成
            </DialogTitle>
            <DialogDescription>密钥重新生成成功！请立即复制并保存以下密钥，此密钥将不会再次显示。</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>新的应用密钥 (Secret)</Label>
              <div className="flex items-center gap-2 p-3 bg-muted rounded-md border">
                <code className="flex-1 text-sm font-mono break-all">{newSecret}</code>
                <Button variant="ghost" size="sm" onClick={() => copyToClipboard(newSecret)}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
                <div className="text-sm text-red-800">
                  <p className="font-medium mb-1">重要提醒：</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>旧密钥已失效，请立即更新您的应用配置</li>
                    <li>请妥善保管新密钥，避免泄露</li>
                    <li>关闭此对话框后将无法再次查看</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleNewSecretDialogClose} className="w-full">
              我已保存新密钥
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
