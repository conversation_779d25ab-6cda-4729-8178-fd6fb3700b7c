import type { FastifyInstance } from 'fastify'
import { config } from './env'
import { logger } from './lib'

export const startServer = async (app: FastifyInstance, isAdmin = false) => {
  const port = isAdmin ? Number(config.ADMIN_PORT) : Number(config.PORT)
  await app.listen({
    port,
    host: '0.0.0.0',
  })

  logger.info('服务器启动成功', {
    type: 'system',
    environment: config.NODE_ENV,
    port,
    isAdmin,
    host: '0.0.0.0',
  })

  return { port }
}
