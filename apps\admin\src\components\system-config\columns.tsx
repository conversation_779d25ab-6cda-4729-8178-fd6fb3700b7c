import { Badge } from '@coozf/ui/components/badge'
import { Switch } from '@coozf/ui/components/switch'
import DropdownMenuComponent from '@/components/dropdown-menu-component'
import { type ColumnDef } from '@tanstack/react-table'
import { type RouterOutput } from '@/lib/trpc'
import { formatDate } from '@coozf/ui/lib/day'
import { DropdownMenuItem } from '@coozf/ui/components/dropdown-menu'
import { categoryMap } from '@coozf/zod'

type SystemConfigItem = RouterOutput['systemConfig']['list']['items'][number]

export function getTableColumns(
  onEdit: (config: SystemConfigItem) => void,
  onDelete: (id: string) => void,
  onToggleEnabled: (id: string, isEnabled: boolean) => void,
): ColumnDef<SystemConfigItem>[] {
  return [
    {
      accessorKey: 'key',
      header: '配置键',
      cell: ({ row }) => <div className="font-mono text-sm">{row.original.key}</div>,
    },
    {
      accessorKey: 'name',
      header: '配置名称',
      cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
    },
    {
      accessorKey: 'category',
      header: '分类',
      cell: ({ row }) => {
        const config = categoryMap[row.original.category]
        return <Badge variant={config.variant}>{config.label}</Badge>
      },
    },
    {
      accessorKey: 'description',
      header: '描述',
      cell: ({ row }) => (
        <div className="max-w-xs truncate text-muted-foreground">{row.original.description || '-'}</div>
      ),
    },
    // {
    //   accessorKey: 'value',
    //   header: '配置值',
    //   cell: ({ row }) => {
    //     const value = row.original.value
    //     const displayValue = Array.isArray(value)
    //       ? `[${value.length}项]`
    //       : typeof value === 'object'
    //         ? JSON.stringify(value).slice(0, 50) + '...'
    //         : String(value)

    //     return <div className="max-w-xs truncate font-mono text-sm">{displayValue}</div>
    //   },
    // },
    {
      accessorKey: 'isEnabled',
      header: '状态',
      cell: ({ row }) => (
        <Switch
          checked={row.original.isEnabled}
          onCheckedChange={(checked) => onToggleEnabled(row.original.id, checked)}
        />
      ),
    },
    {
      accessorKey: 'updatedAt',
      header: '更新时间',
      cell: ({ row }) => <div className="text-sm text-muted-foreground">{formatDate(row.original.updatedAt)}</div>,
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => (
        <DropdownMenuComponent>
          <DropdownMenuItem onClick={() => onEdit(row.original)}>编辑</DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive hover:text-destructive"
            onClick={() => onDelete(row.original.id)}
          >
            删除
          </DropdownMenuItem>
        </DropdownMenuComponent>
      ),
    },
  ]
}
