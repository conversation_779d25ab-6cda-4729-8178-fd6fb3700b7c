import { publicProcedure } from '@/trpc'
import { encryptXiaohongshuData } from '@/lib/utils'
import { env } from '@/env'

export const xiaohongshuRouter = {
  // 获取发布记录列表
  // GET /xiaohongshu/open/im/agree-auth - 验证接口
  agreeAuth: publicProcedure.query(async ({ ctx }) => {
    const content = {
      account_code: 'lingjing',
    }

    const token = encryptXiaohongshuData(JSON.stringify(content), env.XIAOHONGSHU_SECRET)

    return {
      appId: env.XIAOHONGSHU_CLIENT_KEY,
      token,
    }
  }),
}
