import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useMutation, useQuery } from '@tanstack/react-query'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/applications/table-search'
import { ExpireDateDialog } from '@/components/applications/expire-date-dialog'
import { CreditsRulesDialog } from '@/components/applications/credits-rules-dialog'
import type { ApplicationListParams } from '@coozf/zod'
import { getTableColumns } from '@/components/applications/columns'
import { toast } from 'sonner'

type ApplicationItem = RouterOutput['application']['list']['data'][number]

type Search = {
  search?: string
  status?: string
  userId?: string
}

export const Route = createFileRoute('/_authenticated/applications')({
  component: ApplicationsPage,
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      search: search?.search as string,
      status: search?.status as string,
      userId: search?.userId as string,
    }
  },
})

function ApplicationsPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  })

  // 过期时间对话框状态
  const [expireDateDialogOpen, setExpireDateDialogOpen] = useState(false)
  const [selectedApplication, setSelectedApplication] = useState<ApplicationItem | null>(null)

  // 点数规则对话框状态
  const [creditsRulesDialogOpen, setCreditsRulesDialogOpen] = useState(false)
  const [selectedCreditsApplication, setSelectedCreditsApplication] = useState<ApplicationItem | null>(null)

  const { search, status, userId } = useSearch({ strict: false })

  const [filter, setFilter] = useState<Omit<ApplicationListParams, 'page' | 'pageSize'>>({
    search,
    status: status as 'ACTIVE' | 'SUSPENDED' | 'DELETED' | undefined,
    userId,
  })

  // 获取应用列表
  const {
    data: applicationsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.application.list.queryOptions({
      ...filter,
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    }),
  )

  // 获取应用点数配置
  const { data: creditsConfig, refetch: refetchCreditsConfig } = useQuery(
    trpc.quota.getApplicationCreditsConfig.queryOptions(
      { applicationId: selectedCreditsApplication?.id || '' },
      {
        enabled: !!selectedCreditsApplication?.id,
      },
    ),
  )

  // 更新过期时间
  const updateExpireDateMutation = useMutation(
    trpc.application.updateExpireDate.mutationOptions({
      onSuccess: () => {
        // 刷新数据
        refetch()
        setExpireDateDialogOpen(false)
        setSelectedApplication(null)
        toast.success('过期时间更新成功')
      },
      onError: (error) => {
        toast.error(`更新失败: ${error.message}`)
      },
    }),
  )

  // 切换OEM状态
  const updateOemStatusMutation = useMutation(
    trpc.application.updateOemStatus.mutationOptions({
      onSuccess: () => {
        // 刷新数据
        refetch()
        toast.success('OEM状态更新成功')
      },
      onError: (error) => {
        toast.error(`更新失败: ${error.message}`)
      },
    }),
  )

  // 更新点数配置
  const updateCreditsConfigMutation = useMutation(
    trpc.quota.updateApplicationCreditsConfig.mutationOptions({
      onSuccess: () => {
        refetch()
        refetchCreditsConfig()
        toast.success('点数配置更新成功')
      },
      onError: (error) => {
        toast.error(`更新失败: ${error.message}`)
      },
    }),
  )

  const applications = applicationsData?.data || []
  const total = applicationsData?.total || 0

  const columns = useMemo(
    () =>
      getTableColumns(
        (app) => {
          setSelectedApplication(app)
          setExpireDateDialogOpen(true)
        },
        () => {
          // TODO: Implement overview logic
        },
        (app, oemEnabled) => {
          updateOemStatusMutation.mutate({
            applicationId: app.id,
            oemEnabled,
          })
        },
        (app) => {
          setSelectedCreditsApplication(app)
          setCreditsRulesDialogOpen(true)
        },
      ),
    [updateOemStatusMutation],
  )

  return (
    <div className="flex flex-1 flex-col gap-2 overflow-hidden">
      <TableSearch
        values={filter}
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 })
          setFilter(value)
        }}
      />

      <DataTable
        columns={columns}
        data={applications}
        rowCount={total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />

      {selectedApplication && (
        <ExpireDateDialog
          application={selectedApplication}
          open={expireDateDialogOpen}
          onOpenChange={setExpireDateDialogOpen}
          onUpdateExpireDate={(expireDate) => {
            updateExpireDateMutation.mutate({
              applicationId: selectedApplication.id,
              expireDate,
            })
          }}
          isLoading={updateExpireDateMutation.isPending}
        />
      )}

      {selectedCreditsApplication && (
        <CreditsRulesDialog
          application={selectedCreditsApplication}
          creditsConfig={creditsConfig}
          open={creditsRulesDialogOpen}
          onOpenChange={(open) => {
            setCreditsRulesDialogOpen(open)
            if (!open) {
              setSelectedCreditsApplication(null)
            }
          }}
          onUpdateCreditsConfig={(config) => {
            updateCreditsConfigMutation.mutate({
              applicationId: selectedCreditsApplication.id,
              ...config,
            })
          }}
          isLoading={updateCreditsConfigMutation.isPending}
        />
      )}
    </div>
  )
}
