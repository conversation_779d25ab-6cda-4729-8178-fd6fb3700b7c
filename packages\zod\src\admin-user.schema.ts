import z from "zod";

const password = z.string().min(6, '密码至少6位')

const role = z.enum(['admin', 'user'])

// 创建用户
const createScheam = z.object({
    name: z.string().min(1, '用户名至少1位'),
    password,
    username: z.string().min(4, '账号至少4位'),
    role
})

// 修改密码
const setPasswordScheam = z.object({
    id: z.string(),
    password,
})

// 修改角色
const setRoleScheam = z.object({
    id: z.string(),
    role
})

// 列表查询

type CreateInput = z.infer<typeof createScheam>
type SetPasswordInput = z.infer<typeof setPasswordScheam>
type SetRoleInput = z.infer<typeof setRoleScheam>

export { createScheam, setPasswordScheam, setRoleScheam };
export type { CreateInput, SetPasswordInput, SetRoleInput };
