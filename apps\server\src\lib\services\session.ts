import { randomBytes } from 'crypto'
import { CacheService } from './cache'
import { ApiError } from '../utils/response'
import { generateJWT, verifyJWT, createTokenPayload } from '../utils/token'
import { env } from '../../env'

export interface SessionTokenData {
  userId: string
  applicationId: string
  recordId: string
  createdAt: number
  // 附加信息
  extra?: unknown
}

/**
 * 会话管理服务
 */
export class SessionService {
  private cacheService: CacheService

  constructor(cacheService?: CacheService) {
    this.cacheService = cacheService || new CacheService()
  }

  /**
   * 生成会话Token
   */
  async generateSessionToken(
    userId: string,
    applicationId: string,
    recordId: string,
    extra?: unknown,
  ): Promise<string> {
    const sessionToken = `sess_${randomBytes(32).toString('hex')}`
    const redisKey = `session_token:${sessionToken}`

    const tokenData: SessionTokenData = {
      userId,
      applicationId,
      recordId,
      createdAt: Date.now(),
      extra,
    }

    // 将 session_token 及其关联数据存入缓存，过期时间1小时
    await this.cacheService.setJson(redisKey, tokenData, 3600 * 2)

    return sessionToken
  }

  /**
   * 获取会话Token数据
   */
  async getSessionToken(sessionToken: string): Promise<SessionTokenData> {
    const redisKey = `session_token:${sessionToken}`
    const tokenData = await this.cacheService.getJson<SessionTokenData>(redisKey)

    if (!tokenData) {
      throw new ApiError(40100, 'Session token not found')
    }

    return tokenData
  }

  /**
   * 删除会话Token
   */
  async revokeSessionToken(sessionToken: string): Promise<void> {
    const redisKey = `session_token:${sessionToken}`
    await this.cacheService.del(redisKey)
  }

  /**
   * 刷新会话Token（延长过期时间）
   */
  async refreshSessionToken(sessionToken: string): Promise<void> {
    const tokenData = await this.getSessionToken(sessionToken)
    const redisKey = `session_token:${sessionToken}`

    // 重新设置过期时间
    await this.cacheService.setJson(redisKey, tokenData, 3600)
  }
}

/**
 * JWT Token管理服务
 */
export class TokenService {
  private cacheService: CacheService
  private jwtSecret: string

  constructor(cacheService?: CacheService) {
    this.cacheService = cacheService || new CacheService()
    this.jwtSecret = env.JWT_SECRET
  }

  /**
   * 生成JWT Token
   */
  async generateToken(appId: string): Promise<string> {
    const payload = createTokenPayload(appId, 30 * 24 * 60 * 60) // 30天
    const token = generateJWT(payload, this.jwtSecret)

    // 存储当前有效token的jti
    await this.cacheService.set(`token:${appId}`, payload.jti, 30 * 24 * 60 * 60)

    return token
  }

  /**
   * 验证JWT Token
   */
  async verifyToken(token: string): Promise<string | null> {
    const payload = verifyJWT(token, this.jwtSecret)
    if (!payload) {
      return null
    }

    // 检查是否为当前有效token
    const activeJti = await this.cacheService.get(`token:${payload.appId}`)
    if (activeJti !== payload.jti) {
      return null
    }

    return payload.appId
  }

  /**
   * 撤销Token
   */
  async revokeToken(appId: string): Promise<void> {
    await this.cacheService.del(`token:${appId}`)
  }

  /**
   * 刷新Token
   */
  async refreshToken(appId: string): Promise<string> {
    // 撤销旧token
    await this.revokeToken(appId)
    // 生成新token
    return await this.generateToken(appId)
  }
}

// 创建默认实例
export const sessionService = new SessionService()
export const tokenService = new TokenService()
