import { z } from 'zod'
import { router } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'
import { AccountListQuerySchema } from '@coozf/zod'
import { MediaAccountServies, PlatformCreditsService } from '@/lib'

export const authAccountRouter = router({
  // 获取授权账号列表
  list: adminProtectedProcedure.input(AccountListQuerySchema).query(async ({ input }) => {
    return MediaAccountServies.getMediaAccountOrders(input)
  }),

  // 获取账号详情
  getById: adminProtectedProcedure.input(z.object({ id: z.string() })).query(async ({ ctx, input }) => {
    const authAccount = await ctx.db.authAccount.findUnique({
      where: { id: input.id },
      include: {
        application: {
          select: {
            name: true,
            appId: true,
            userId: true,
          },
        },
      },
    })

    if (!authAccount) {
      throw new Error('授权账号不存在')
    }

    return authAccount
  }),

  // 删除账号
  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    // 使用事务删除账号并返还点数
    await ctx.db.$transaction(async (tx) => {
      // 先获取账号信息以计算返还的点数
      const account = await tx.authAccount.findUnique({
        where: { id: input.id },
        select: { platformCode: true, applicationId: true }
      })

      if (!account) {
        throw new Error('账号不存在')
      }

      // 计算返还的点数
      const returnCredits = await PlatformCreditsService.getCreditsPerAccount(account.applicationId, account.platformCode)

      // 删除账号
      await tx.authAccount.delete({
        where: { id: input.id },
      })

      // 返还点数
      await tx.application.update({
        where: { id: account.applicationId },
        data: {
          creditsUsed: {
            decrement: returnCredits,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId: account.applicationId,
          endpoint: '/admin/auth-accounts',
          method: 'DELETE',
          costType: 'CREDITS',
          costAmount: -returnCredits, // 负数表示返还
          statusCode: 200,
        },
      })
    })

    return { success: true, message: '账号删除成功' }
  }),

  // 批量删除账号
  batchDelete: adminProtectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      let totalReturnCredits = 0
      const appCreditsMap = new Map<string, number>()

      // 使用事务批量删除账号并返还点数
      await ctx.db.$transaction(async (tx) => {
        // 先获取所有账号信息以计算返还的点数
        const accounts = await tx.authAccount.findMany({
          where: {
            id: {
              in: input.ids,
            },
          },
          select: { id: true, platformCode: true, applicationId: true }
        })

        // 计算每个应用需要返还的点数
        for (const account of accounts) {
          const returnCredits = await PlatformCreditsService.getCreditsPerAccount(account.applicationId, account.platformCode)
          totalReturnCredits += returnCredits
          
          const currentCredits = appCreditsMap.get(account.applicationId) || 0
          appCreditsMap.set(account.applicationId, currentCredits + returnCredits)
        }

        // 批量删除账号
        await tx.authAccount.deleteMany({
          where: {
            id: {
              in: input.ids,
            },
          },
        })

        // 为每个应用返还点数
        for (const [applicationId, returnCredits] of appCreditsMap) {
          await tx.application.update({
            where: { id: applicationId },
            data: {
              creditsUsed: {
                decrement: returnCredits,
              },
            },
          })

          // 记录API调用
          await tx.apiCall.create({
            data: {
              applicationId,
              endpoint: '/admin/auth-accounts/batch',
              method: 'DELETE',
              costType: 'CREDITS',
              costAmount: -returnCredits, // 负数表示返还
              statusCode: 200,
            },
          })
        }
      })

      return { success: true, message: `已删除 ${input.ids.length} 个账号，返还 ${totalReturnCredits} 点数` }
    }),
})
