import { db } from '@coozf/db'
import { Prisma, SystemConfigCategory } from '@prisma/client'
import { TRPCError } from '@trpc/server'
import { systemConfigCache } from '../services/cache'

interface CreateSystemConfigInput {
  key: string
  category: SystemConfigCategory
  name: string
  description?: string
  value: any
  isEnabled?: boolean
}

interface UpdateSystemConfigInput {
  key?: string
  category?: SystemConfigCategory
  name?: string
  description?: string
  value?: any
  isEnabled?: boolean
}

interface SystemConfigListParams {
  category?: SystemConfigCategory
  isEnabled?: boolean
  page?: number
  limit?: number
}

/**
 * 系统配置管理业务逻辑
 */
export class SystemConfigService {
  /**
   * 获取配置列表（支持分页和筛选）
   */
  static async getConfigList(params: SystemConfigListParams) {
    const { category, isEnabled, page = 1, limit = 10 } = params
    const skip = (page - 1) * limit

    // 构建查询条件
    const where: Prisma.SystemConfigWhereInput = {}

    if (category !== undefined) {
      where.category = category
    }
    if (isEnabled !== undefined) {
      where.isEnabled = isEnabled
    }

    // 获取总数
    const total = await db.systemConfig.count({ where })

    // 获取分页数据
    const configs = await db.systemConfig.findMany({
      where,
      skip,
      take: limit,
      orderBy: [{ category: 'asc' }, { createdAt: 'desc' }],
    })

    return {
      items: configs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 根据key获取单个配置
   */
  static async getConfigByKey(key: string) {
    const config = await db.systemConfig.findUnique({
      where: { key },
    })

    if (!config) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: `配置项 ${key} 不存在`,
      })
    }

    return config
  }

  /**
   * 根据key获取配置值（如果不存在或未启用返回null）
   */
  static async getConfigValue(key: string): Promise<any> {
    try {
      const config = await db.systemConfig.findUnique({
        where: { key, isEnabled: true },
      })
      return config?.value || null
    } catch {
      return null
    }
  }

  /**
   * 根据category获取配置值列表
   */
  static async getConfigValuesByCategory(category: SystemConfigCategory): Promise<any[]> {
    try {
      return await db.systemConfig.findMany({
        where: { category, isEnabled: true },
      })
    } catch {
      return []
    }
  }

  /**
   * 创建系统配置
   */
  static async createConfig(data: CreateSystemConfigInput) {
    // 检查key是否已存在
    const existingConfig = await db.systemConfig.findUnique({
      where: { key: data.key },
    })

    if (existingConfig) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: `配置项 ${data.key} 已存在`,
      })
    }

    const res = await db.systemConfig.create({
      data: {
        key: data.key,
        category: data.category,
        name: data.name,
        description: data.description,
        value: data.value,
        isEnabled: data.isEnabled ?? true,
      },
    })
    await this.clearCacheWithCategory(data.category)
    return res
  }

  /**
   * 更新系统配置
   */
  static async updateConfig(id: string, data: UpdateSystemConfigInput) {
    // 检查配置是否存在
    const existingConfig = await db.systemConfig.findUnique({
      where: { id },
    })

    if (!existingConfig) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '配置项不存在',
      })
    }

    // 如果更新key，检查新key是否已被占用
    if (data.key && data.key !== existingConfig.key) {
      const duplicateConfig = await db.systemConfig.findUnique({
        where: { key: data.key },
      })

      if (duplicateConfig) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `配置项 ${data.key} 已存在`,
        })
      }
    }

    const res = await db.systemConfig.update({
      where: { id },
      data,
    })
    await this.clearCacheWithCategory(existingConfig.category)
    return res
  }

  /**
   * 删除系统配置
   */
  static async deleteConfig(id: string) {
    // 检查配置是否存在
    const existingConfig = await db.systemConfig.findUnique({
      where: { id },
    })

    if (!existingConfig) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '配置项不存在',
      })
    }

    const res = await db.systemConfig.delete({
      where: { id },
    })
    await this.clearCacheWithCategory(existingConfig.category)
    return res
  }

  /**
   * 批量初始化爬虫代理配置
   */
  static async initCrawlerProxyConfigs() {
    const defaultConfigs: CreateSystemConfigInput[] = [
      {
        key: 'crawler_proxy_session_token_whitelist',
        category: SystemConfigCategory.CRAWLER_PROXY,
        name: 'Session Token 白名单',
        description: '需要发送session token的接口路径白名单',
        value: [
          '/platform-publish/tasks',
          '/platform-account/overview-query-tasks',
          '/platform-account/contents-tasks',
          '/platform-publish/check-tasks',
        ],
        isEnabled: true,
      },
      {
        key: 'crawler_proxy_account_id_blacklist',
        category: SystemConfigCategory.CRAWLER_PROXY,
        name: 'Account ID 黑名单',
        description: '不需要accountId的接口路径黑名单',
        value: ['/content-parse/article-tasks', '/platform-account/pre-info-tasks'],
        isEnabled: true,
      },
    ]

    const results = []
    for (const configData of defaultConfigs) {
      try {
        // 检查是否已存在
        const existingConfig = await db.systemConfig.findUnique({
          where: { key: configData.key },
        })

        if (!existingConfig) {
          // 不存在则创建
          const newConfig = await this.createConfig(configData)
          results.push({ action: 'created', config: newConfig })
        } else {
          // 已存在则跳过
          results.push({ action: 'skipped', key: configData.key, reason: '已存在' })
        }
      } catch (error) {
        results.push({
          action: 'error',
          key: configData.key,
          error: error instanceof Error ? error.message : '未知错误',
        })
      }
    }

    return {
      message: '爬虫代理配置初始化完成',
      results,
    }
  }

  /**
   * 获取爬虫代理Session Token白名单
   */
  static async getCrawlerProxySessionTokenWhitelist(): Promise<string[]> {
    return (await systemConfigCache.getCrawlerProxys())['crawler_proxy_session_token_whitelist']?.value || []
  }

  /**
   * 获取爬虫代理Account ID黑名单
   */
  static async getCrawlerProxyAccountIdBlacklist(): Promise<string[]> {
    return (await systemConfigCache.getCrawlerProxys())['crawler_proxy_account_id_blacklist']?.value || []
  }

  // 删除缓存
  static async clearCacheWithCategory(category: SystemConfigCategory) {
    await systemConfigCache.clearCategoryCache(category)
  }
}
