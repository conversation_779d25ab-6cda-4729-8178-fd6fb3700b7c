import { paginate } from '@/lib/utils'
import { adminProtectedProcedure } from '@/procedure'
import { router } from '@/trpc'
import { authAdmin } from '@coozf/auth'
import { createScheam, PaginationSchema, setPasswordScheam, setRoleScheam } from '@coozf/zod'
import z from 'zod'

export const adminUserRouter = router({
  create: adminProtectedProcedure.input(createScheam).mutation(async ({ input, ctx }) => {
    const { name, password, role } = input
    const res = await authAdmin.api.createUser({
      body: {
        email: `${name}@coozf.com`,
        name: name,
        password,
        role,
      },
      headers: ctx.req.headers,
    })
    return await ctx.db.admin_user.update({
      where: { id: res.user.id },
      data: {
        username: input.username,
      },
    })
  }),

  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(({ input, ctx }) => {
    return authAdmin.api.removeUser({
      body: {
        userId: input.id,
      },
      headers: ctx.req.headers,
    })
  }),

  setPassword: adminProtectedProcedure.input(setPasswordScheam).mutation(({ input }) => {
    return authAdmin.api.setUserPassword({
      body: {
        userId: input.id,
        newPassword: input.password,
      },
    })
  }),

  setRole: adminProtectedProcedure.input(setRoleScheam).mutation(({ input }) => {
    return authAdmin.api.setRole({
      body: {
        userId: input.id,
        role: input.role,
      },
    })
  }),

  list: adminProtectedProcedure.input(PaginationSchema).query(async ({ input, ctx }) => {
    const { page, pageSize } = input
    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => ctx.db.admin_user.count(),
        getItems: (skip, take) =>
          ctx.db.admin_user.findMany({
            skip,
            take,
            orderBy: { createdAt: 'desc' },
          }),
      },
    )
  }),
})
