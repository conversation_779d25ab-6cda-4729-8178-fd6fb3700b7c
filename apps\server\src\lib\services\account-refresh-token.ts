import { Queue } from 'bullmq'
import { config } from '@/env'
import { logger } from '@/lib'
import { db } from '@coozf/db'

export interface OpenPlatformAccountInfo {
  id: string
  platfromUserId: string
  platformCookieHash: string
  applicationId: string
}

/**
 * BullMQ 消息发布器
 * 用于在版本创建后通知 API 服务推送更新消息给客户端
 */
export class AccountRefreshToken {
  private xiaohongshuQueue: Queue
  private douyinQueue: Queue
  private douyinRefreshTokenQueue: Queue

  constructor() {
    // 创建版本更新队列
    this.xiaohongshuQueue = new Queue<OpenPlatformAccountInfo>(config.REFRESH_XIAOHONGSHU_TOKEN, {
      connection: {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD,
        db: config.REDIS_DB,
      },
      defaultJobOptions: {
        // 任务默认配置
        removeOnComplete: {
          age: 3600, // 完成后保留1小时
          count: 100, // 最多保留100个完成的任务
        },
        removeOnFail: {
          age: 24 * 3600, // 失败后保留24小时
        },
        attempts: 3, // 失败重试3次
        backoff: {
          type: 'exponential',
          delay: 2000, // 重试延迟
        },
      },
    })

    this.douyinQueue = new Queue<OpenPlatformAccountInfo>(config.REFRESH_DOUYIN_TOKEN, {
      connection: {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD,
        db: config.REDIS_DB,
      },
      defaultJobOptions: {
        // 任务默认配置
        removeOnComplete: {
          age: 3600, // 完成后保留1小时
          count: 100, // 最多保留100个完成的任务
        },
        removeOnFail: {
          age: 24 * 3600, // 失败后保留24小时
        },
        attempts: 3, // 失败重试3次
        backoff: {
          type: 'exponential',
          delay: 2000, // 重试延迟
        },
      },
    })

    this.douyinRefreshTokenQueue = new Queue<OpenPlatformAccountInfo>(config.REFRESH_DOUYIN_REFRESH_TOKEN, {
      connection: {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD,
        db: config.REDIS_DB,
      },
      defaultJobOptions: {
        // 任务默认配置
        removeOnComplete: {
          age: 3600, // 完成后保留1小时
          count: 100, // 最多保留100个完成的任务
        },
        removeOnFail: {
          age: 24 * 3600, // 失败后保留24小时
        },
        attempts: 3, // 失败重试3次
        backoff: {
          type: 'exponential',
          delay: 2000, // 重试延迟
        },
      },
    })

    // 监听队列事件
    this.setupQueueEvents()
  }

  /**
   * 设置队列事件监听
   */
  private setupQueueEvents(): void {
    this.xiaohongshuQueue.on('error', (error) => {
      logger.error('版本更新队列错误', {
        type: 'message_publisher',
        event: 'queue_error',
        error: error.message,
      })
    })
  }

  async refreshXiaohongshuTokenTask() {
    const accounts = await db.authAccount.findMany({
      where: {
        platformCode: 'xiaohongshuopen',
        platformRefreshTokenExpiresAt: {
          gte: new Date(),
        },
      },
    })

    accounts.forEach((account) => {
      this.xiaohongshuQueue.add(
        config.REFRESH_XIAOHONGSHU_TOKEN,
        {
          id: account.id,
          platfromUserId: account.platformUserId,
          platformCookieHash: account.platformCookieHash,
          applicationId: account.applicationId,
        },
        {
          delay: 5 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `xhs-account-update-move-${account.platformUserId}`,
        },
      )
    })
  }

  // 抖音刷新token
  async refreshDouyinTokenTask() {
    // 查询token过期日期小于36小时数据 并且大于当前时间
    const accounts = await db.authAccount.findMany({
      where: {
        platformCode: 'douyinopen',
        platformTokenExpiresAt: {
          lte: new Date(Date.now() + 36 * 60 * 60 * 1000),
          gte: new Date(),
        },
      },
    })

    accounts.forEach((account) => {
      this.douyinQueue.add(
        config.REFRESH_DOUYIN_TOKEN,
        {
          id: account.id,
          platfromUserId: account.platformUserId,
          platformCookieHash: account.platformCookieHash,
          applicationId: account.applicationId,
        },
        {
          delay: 5 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `douyin-account-update-move-${account.platformUserId}`,
        },
      )
    })
  }

  // 抖音刷新refreshToken
  async refreshDouyinRefreshTokenTask() {
    // 查询refreshToken过期日期大于当前时间并小于36小时的数据
    const accounts = await db.authAccount.findMany({
      where: {
        platformCode: 'douyinopen',
        platformRefreshTokenExpiresAt: {
          lte: new Date(Date.now() + 48 * 60 * 60 * 1000),
          gte: new Date(),
        },
      },
    })

    accounts.forEach((account) => {
      this.douyinRefreshTokenQueue.add(
        config.REFRESH_DOUYIN_REFRESH_TOKEN,
        {
          id: account.id,
          platfromUserId: account.platformUserId,
          platformCookieHash: account.platformCookieHash,
          applicationId: account.applicationId,
        },
        {
          delay: 5 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `douyin-refresh-token-account-update-move-${account.platformUserId}`,
        },
      )
    })
  }
}

// 导出单例实例
export const accountRefreshToken = new AccountRefreshToken()
