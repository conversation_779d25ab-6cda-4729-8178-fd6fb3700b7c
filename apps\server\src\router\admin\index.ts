import { authRouter } from '@/router/admin/auth'
import { router, t } from '@/trpc'
import { orderRouter } from './order'
import { userRouter } from './user'
import { applicationRouter } from './application'
import { authAccountRouter } from './auth-account'
import { versionRouter } from './version'
import { ossRouter } from './oss'
import { adminUserRouter } from './admin-user'
import { systemConfigRouter } from './system-config'
import { quotaRouter } from './quota'
import { deviceRouter } from './device'

export const appRouter = router({
  auth: authRouter,
  order: orderRouter,
  quota: quotaRouter,
  user: userRouter,
  application: applicationRouter,
  authAccount: authAccountRouter,
  version: versionRouter,
  oss: ossRouter,
  adminUser: adminUserRouter,
  systemConfig: systemConfigRouter,
  // 管理端设备相关接口
  device: deviceRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter

export const createCaller = t.createCallerFactory(appRouter)
