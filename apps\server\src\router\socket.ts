import { z } from 'zod'
import { router, publicProcedure } from '../trpc'
import { deviceManager } from '../lib/device-manager'
import { logger } from '@/lib'

export const socketRouter = router({
  // 测试接口 - 简单的 echo
  test: publicProcedure
    .input(z.object({
      message: z.string()
    }))
    .mutation(async ({ input }) => {
      return {
        success: true,
        echo: input.message,
        timestamp: new Date().toISOString()
      }
    }),

  // 向指定设备发送消息
  sendToDevice: publicProcedure
    .input(z.object({
      deviceId: z.string().min(1, '设备ID不能为空'),
      event: z.string().min(1, '事件名称不能为空'),
      data: z.any().optional(),
      timeout: z.number().optional().default(5000)
    }))
    .mutation(async ({ input, ctx }) => {
      const { deviceId, event, data, timeout } = input

      // 检查设备是否在线
      const isOnline = await deviceManager.isOnline(deviceId)
      if (!isOnline) {
        throw new Error(`设备 ${deviceId} 不在线`)
      }

      // 获取设备的 socket ID
      const socketId = await deviceManager.getSocketId(deviceId)
      if (!socketId) {
        throw new Error(`无法获取设备 ${deviceId} 的连接信息`)
      }

      try {
        // 发送消息到指定设备
        ctx.io.to(socketId).emit(event, data)

        logger.info('消息发送成功', {
          type: 'socket',
          event: 'send_to_device',
          deviceId,
          socketId,
          eventName: event,
          data
        })

        return {
          success: true,
          deviceId,
          socketId,
          event,
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        logger.error('消息发送失败', {
          type: 'socket',
          event: 'send_to_device_error',
          deviceId,
          socketId,
          eventName: event,
          error: error instanceof Error ? error.message : error
        })

        throw new Error(`消息发送失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }),

  // 广播消息到所有在线设备
  broadcast: publicProcedure
    .input(z.object({
      event: z.string().min(1, '事件名称不能为空'),
      data: z.any().optional(),
      excludeDevices: z.array(z.string()).optional().default([])
    }))
    .mutation(async ({ input, ctx }) => {
      const { event, data, excludeDevices } = input

      try {
        // 获取所有在线设备
        const onlineDevices = await deviceManager.getAllDevices()
        
        // 过滤掉排除的设备
        const targetDevices = onlineDevices.filter(device => 
          !excludeDevices.includes(device.deviceId)
        )

        if (targetDevices.length === 0) {
          return {
            success: true,
            message: '没有可发送的设备',
            sentCount: 0,
            timestamp: new Date().toISOString()
          }
        }

        // 广播消息
        ctx.io.emit(event, data)

        logger.info('广播消息发送成功', {
          type: 'socket',
          event: 'broadcast',
          eventName: event,
          data,
          targetDeviceCount: targetDevices.length,
          excludeDevices
        })

        return {
          success: true,
          sentCount: targetDevices.length,
          targetDevices: targetDevices.map(d => d.deviceId),
          event,
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        logger.error('广播消息发送失败', {
          type: 'socket',
          event: 'broadcast_error',
          eventName: event,
          error: error instanceof Error ? error.message : error
        })

        throw new Error(`广播消息发送失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }),

  // 向指定房间发送消息
  sendToRoom: publicProcedure
    .input(z.object({
      room: z.string().min(1, '房间名称不能为空'),
      event: z.string().min(1, '事件名称不能为空'),
      data: z.any().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { room, event, data } = input

      try {
        // 发送消息到指定房间
        ctx.io.to(room).emit(event, data)

        logger.info('房间消息发送成功', {
          type: 'socket',
          event: 'send_to_room',
          room,
          eventName: event,
          data
        })

        return {
          success: true,
          room,
          event,
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        logger.error('房间消息发送失败', {
          type: 'socket',
          event: 'send_to_room_error',
          room,
          eventName: event,
          error: error instanceof Error ? error.message : error
        })

        throw new Error(`房间消息发送失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }),
})
