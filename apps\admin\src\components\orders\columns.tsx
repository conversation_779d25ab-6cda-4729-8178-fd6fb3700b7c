import type { RouterOutput } from '@/lib/trpc'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@coozf/ui/components/dropdown-menu'
import type { ColumnDef } from '@tanstack/react-table'
import { CheckCircle, Eye, Loader2, MoreHorizontal, RefreshCw, XCircle, Clock } from 'lucide-react'

type OrderItem = RouterOutput['order']['list']['data'][number]

export function getTableColumns(
  handleViewDetails: (order: OrderItem) => void,
  handleCancelOrder: (orderId: string) => void,
  handleBankTransfer: (orderId: string) => void,
): ColumnDef<OrderItem>[] {
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default'
      case 'PENDING':
        return 'secondary'
      case 'CANCELLED':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-orange-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return '已完成'
      case 'PENDING':
        return '待处理'
      case 'CANCELLED':
        return '已取消'
      default:
        return status
    }
  }

  return [
    {
      accessorKey: 'orderNo',
      header: '订单号',
      cell: ({ row }) => (
        <div className="font-medium">{row.original.orderNo}</div>
      ),
    },
    {
      accessorKey: 'applicationId',
      header: '应用',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">{row.original.application.name}</div>
      ),
    },
    {
      accessorKey: 'amount',
      header: '金额',
      cell: ({ row }) => (
        <div className="font-medium">¥{row.original.amount}</div>
      ),
    },
    {
      accessorKey: 'quotaType',
      header: '配额类型',
      cell: ({ row }) => {
        const quotaType = row.original.quotaType
        return quotaType === 'ACCOUNT' ? '账号' : quotaType === 'TRAFFIC' ? '流量' : 'N/A'
      },
    },
    {
      accessorKey: 'quotaAmount',
      header: '配额数量',
      cell: ({ row }) => (
        <div>{row.original.quotaAmount?.toString() || 'N/A'}</div>
      ),
    },
    {
      accessorKey: 'type',
      header: '订单类型',
      cell: ({ row }) => {
        const type = row.original.type
        return type === 'PURCHASE' ? '购买' : type === 'GIFT' ? '赠送' : 'N/A'
      },
    },
    {
      accessorKey: 'paymentMethod',
      header: '支付方式',
      cell: ({ row }) => {
        const paymentMethod = row.original.paymentMethod
        return paymentMethod === 'BANK_TRANSFER' ? '银行转账' : 'N/A'
      },
    },
    {
      accessorKey: 'status',
      header: '订单状态',
      cell: ({ row }) => {
        const status = row.original.status
        return (
          <div className="flex items-center gap-2">
            {getStatusIcon(status)}
            <Badge variant={getStatusBadgeVariant(status)}>{getStatusText(status)}</Badge>
          </div>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.createdAt).toLocaleString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const order = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleViewDetails(order)}>
                <Eye className="mr-2 h-4 w-4" />
                查看详情
              </DropdownMenuItem>
              {order.status === 'PENDING' && (
                <>
                  <DropdownMenuItem onClick={() => handleBankTransfer(order.id)}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    对公转账开通
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={() => handleCancelOrder(order.id)}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    取消订单
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}