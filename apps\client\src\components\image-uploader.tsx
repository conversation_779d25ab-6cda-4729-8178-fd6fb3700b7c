// components/image-uploader.tsx
import { useState, useEffect } from 'react'
import { Input } from '@coozf/ui/components/input'
import { cn } from '@coozf/ui/lib/utils'
import { Upload, AlertCircle, Check } from 'lucide-react'

export interface ImageConstraints {
  maxSize: number // bytes
  acceptedFormats: readonly string[] // 改为 readonly
  recommendedSize?: { width: number; height: number }
  required?: boolean
}

export interface ImageUploaderProps {
  value?: string | null
  onChange: (file: File | null) => void
  constraints: ImageConstraints
  label?: string
  placeholder?: string
  disabled?: boolean
  error?: string
  className?: string
  previewClassName?: string
}

export function ImageUploader({
  value,
  onChange,
  constraints,
  label,
  placeholder = '选择图片',
  disabled = false,
  error,
  className,
  previewClassName,
}: ImageUploaderProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string>(value || '')
  const [validationError, setValidationError] = useState<string>('')

  // 清理预览 URL
  useEffect(() => {
    return () => {
      if (preview && preview.startsWith('blob:')) {
        URL.revokeObjectURL(preview)
      }
    }
  }, [preview])

  // 当 value 改变时更新预览
  useEffect(() => {
    if (value && !selectedFile) {
      setPreview(value)
    }
  }, [value, selectedFile])

  // 验证图片尺寸
  const validateImageDimensions = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!constraints.recommendedSize) {
        resolve(true)
        return
      }

      const img = new Image()
      const url = URL.createObjectURL(file)
      
      img.onload = () => {
        URL.revokeObjectURL(url)
        const { width, height } = constraints.recommendedSize!
        
        // 允许一定的容差（±10%）
        const tolerance = 0.1
        const widthValid = Math.abs(img.width - width) <= width * tolerance
        const heightValid = Math.abs(img.height - height) <= height * tolerance
        
        if (!widthValid || !heightValid) {
          setValidationError(
            `建议尺寸为 ${width}x${height}px，当前图片为 ${img.width}x${img.height}px`
          )
        }
        resolve(true) // 只是警告，不阻止上传
      }
      
      img.onerror = () => {
        URL.revokeObjectURL(url)
        resolve(false)
      }
      
      img.src = url
    })
  }

  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 重置错误
    setValidationError('')

    // 验证文件格式 - 使用 Array.from 转换 readonly array
    if (!Array.from(constraints.acceptedFormats).includes(file.type)) {
      setValidationError('不支持的文件格式')
      event.target.value = ''
      return
    }

    // 验证文件大小
    if (file.size > constraints.maxSize) {
      const sizeMB = (constraints.maxSize / (1024 * 1024)).toFixed(0)
      setValidationError(`文件大小不能超过 ${sizeMB}MB`)
      event.target.value = ''
      return
    }

    // 验证图片尺寸
    await validateImageDimensions(file)

    // 清理旧预览
    if (preview && preview.startsWith('blob:')) {
      URL.revokeObjectURL(preview)
    }

    // 设置新文件和预览
    const previewUrl = URL.createObjectURL(file)
    setSelectedFile(file)
    setPreview(previewUrl)
    onChange(file)
  }

  const displayError = error || validationError

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="text-sm font-medium">
          {label}
          {constraints.required && <span className="text-destructive ml-1">*</span>}
        </div>
      )}

      <div className="flex gap-4">
        {/* 预览区 */}
        <div className="flex-shrink-0">
          <div
            className={cn(
              "w-20 h-20 rounded-lg border-2 flex items-center justify-center overflow-hidden bg-muted/30",
              !preview && "border-dashed",
              displayError && "border-destructive",
              previewClassName
            )}
          >
            {preview ? (
              <img src={preview} alt="Preview" className="w-full h-full object-contain" />
            ) : (
              <Upload className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
        </div>

        {/* 上传区 */}
        <div className="flex-1 space-y-2">
          <Input
            type="file"
            accept={constraints.acceptedFormats.join(',')}
            onChange={handleFileSelect}
            disabled={disabled}
            className="cursor-pointer"
            placeholder={placeholder}
          />
          
          {/* 提示信息 */}
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">
              支持 {Array.from(constraints.acceptedFormats).map(f => f.split('/')[1]?.toUpperCase() || 'ICO').join('、')}
              {' • '}
              最大 {(constraints.maxSize / (1024 * 1024)).toFixed(0)}MB
              {constraints.recommendedSize && (
                <>
                  {' • '}
                  建议 {constraints.recommendedSize.width}x{constraints.recommendedSize.height}px
                </>
              )}
            </p>
            
            {selectedFile && !displayError && (
              <p className="text-xs text-green-600 flex items-center gap-1">
                <Check className="w-3 h-3" />
                已选择: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
              </p>
            )}
          </div>
        </div>
      </div>

      {displayError && (
        <p className="text-sm text-destructive flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          {displayError}
        </p>
      )}
    </div>
  )
}