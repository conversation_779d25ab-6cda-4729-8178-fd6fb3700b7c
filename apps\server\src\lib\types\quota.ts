import type { Application } from '@prisma/client'

// 基于Prisma类型的配额信息
export type ApplicationQuotaInfo = Pick<Application, 
  'creditsQuota' | 'creditsUsed' | 'creditsExpireDate' | 'trafficQuotaGB' | 'trafficUsedGB'
>

// 应用配额概览
export interface QuotaOverview {
  credits: {
    quota: number
    used: number
    available: number
    expireDate: Date | null
    expired: boolean
  }
  account: {
    used: number
  }
  traffic: {
    quotaGB: number
    usedGB: number
    availableGB: number
    usagePercent: number
  }
}

// 配额检查结果
export interface QuotaCheckResult {
  sufficient: boolean
  reason?: string
}

// 流量消费参数
export interface ConsumeTrafficParams {
  applicationId: string
  trafficGB: number
  endpoint?: string
  description?: string
}

// 点数配额检查参数
export interface CheckCreditsQuotaParams {
  applicationId: string
  requiredCredits: number
}

// 流量配额检查参数
export interface CheckTrafficQuotaParams {
  applicationId: string
  requiredTrafficGB: number
}