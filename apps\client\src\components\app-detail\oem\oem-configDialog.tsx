// oem-config-dialog.tsx
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState, useEffect } from 'react'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { LoadingButton } from '@coozf/ui/components/loading'
import { AlertCircle, Monitor, Apple, AppWindow } from 'lucide-react'
import { OEMConfigSchema, type OEMConfig } from '@coozf/zod'
import { trpcClient } from '@/lib/trpc'
import { toast } from 'sonner'
import { ImageUploader } from '@/components/image-uploader'
import { OEM_ICON_CONFIGS } from '@/constants/oem-icons'
import { Separator } from '@coozf/ui/components/separator'

interface OEMConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialConfig?: OEMConfig
  onSave: (config: OEMConfig) => void
  applicationId: string
}

interface IconFiles {
  logo: File | null
  trayIconMac: File | null
  trayIconWindows: File | null
}

export function OEMConfigDialog({ open, onOpenChange, initialConfig, onSave, applicationId }: OEMConfigDialogProps) {
  const [iconFiles, setIconFiles] = useState<IconFiles>({
    logo: null,
    trayIconMac: null,
    trayIconWindows: null,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<OEMConfig>({
    resolver: zodResolver(OEMConfigSchema),
    defaultValues: {
      name: initialConfig?.name || '',
      brandName: initialConfig?.brandName || '',
      companyName: initialConfig?.companyName || '',
      logoUrl: initialConfig?.logoUrl || '',
      trayIconMacUrl: initialConfig?.trayIconMacUrl || '',
      trayIconWindowsUrl: initialConfig?.trayIconWindowsUrl || '',
      homeUrl: initialConfig?.homeUrl || '',
      copyright: initialConfig?.copyright || '',
    },
  })

  // 重置表单
  useEffect(() => {
    if (open) {
      form.reset({
        name: initialConfig?.name || '',
        brandName: initialConfig?.brandName || '',
        companyName: initialConfig?.companyName || '',
        logoUrl: initialConfig?.logoUrl || '',
        trayIconMacUrl: initialConfig?.trayIconMacUrl || '',
        trayIconWindowsUrl: initialConfig?.trayIconWindowsUrl || '',
        homeUrl: initialConfig?.homeUrl || '',
        copyright: initialConfig?.copyright || '',
      })
      setIconFiles({
        logo: null,
        trayIconMac: null,
        trayIconWindows: null,
      })
    }
  }, [open, initialConfig, form])

  // 上传单个文件
  const uploadFile = async (file: File, type: string): Promise<string> => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'png'

    const { uploadUrl, accessUrl } = await trpcClient.oss.getUploadUrl.query(
      type === 'logo'
        ? {
            type: 'oem-logo',
            applicationId,
            logoType: 'icon',
            extension: fileExtension as any,
          }
        : {
            type: 'file',
            extension: fileExtension as any,
            category: 'image',
          },
    )

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: { 'Content-Type': file.type },
    })

    if (!response.ok) {
      throw new Error(`${type} 上传失败`)
    }

    return accessUrl
  }

  // 批量上传文件
  const uploadAllFiles = async (): Promise<Partial<OEMConfig>> => {
    const uploadPromises: Promise<{ key: string; url: string }>[] = []

    // 准备所有需要上传的文件
    Object.entries(iconFiles).forEach(([key, file]) => {
      if (file) {
        uploadPromises.push(
          uploadFile(file, key).then((url) => ({
            key: `${key}Url`,
            url,
          })),
        )
      }
    })

    // 并行上传所有文件
    const results = await Promise.all(uploadPromises)

    // 转换为对象格式
    return results.reduce((acc, { key, url }) => {
      acc[key] = url
      return acc
    }, {} as any)
  }

  // 验证所有必填图标
  const validateIcons = (): boolean => {
    let hasError = false

    // 新增时，所有图标都必须上传

    if (!iconFiles.logo && !initialConfig?.logoUrl) {
      form.setError('logoUrl', { message: '请上传品牌 Logo' })
      hasError = true
    }
    if (!iconFiles.trayIconMac && !initialConfig?.trayIconMacUrl) {
      form.setError('trayIconMacUrl', { message: '请上传 Mac 托盘图标' })
      hasError = true
    }
    if (!iconFiles.trayIconWindows && !initialConfig?.trayIconWindowsUrl) {
      form.setError('trayIconWindowsUrl', { message: '请上传 Windows 托盘图标' })
      hasError = true
    }

    return !hasError
  }

  // 提交表单
  const onSubmit = async (data: OEMConfig) => {
    // 验证图标
    if (!validateIcons()) {
      toast.error('请上传所有必填图标')
      return
    }

    setIsSubmitting(true)
    try {
      // 上传所有新文件
      const uploadedUrls = await uploadAllFiles()

      const config: OEMConfig = {
        ...data,
        ...uploadedUrls, // 覆盖新上传的文件 URL
        homeUrl: data.homeUrl || undefined,
      }

      onSave(config)
      onOpenChange(false)
      toast.success('配置保存成功')
    } catch (error) {
      console.error('保存失败:', error)
      toast.error('保存失败，请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const isEdit = !!initialConfig

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-none! max-h-[90vh] w-[700px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? '编辑' : '新增'}品牌配置</DialogTitle>
          <DialogDescription>设置品牌信息和客户端图标，所有图标都是必填项</DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">基本信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  应用名称 <span className="text-destructive">*</span>
                </Label>
                <Input id="name" {...form.register('name')} placeholder="我的定制客户端" disabled={isSubmitting} />
                <p className="text-xs text-muted-foreground">客户端界面显示的应用标题，最多10个字符</p>
                {form.formState.errors.name && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {form.formState.errors.name.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="brandName">
                  品牌名称 <span className="text-destructive">*</span>
                  <span className="text-xs text-muted-foreground ml-1">(唯一)</span>
                </Label>
                <Input id="brandName" {...form.register('brandName')} placeholder="MyApp" disabled={isSubmitting} />
                <p className="text-xs text-muted-foreground">用于下载链接和安装包标识，仅支持英文字母和数字</p>
                {form.formState.errors.brandName && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {form.formState.errors.brandName.message}
                  </p>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="companyName">
                公司名称 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="companyName"
                {...form.register('companyName')}
                placeholder="北京某某科技有限公司"
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">用于版权信息和客户端关于页面，显示公司法定名称</p>
              {form.formState.errors.companyName && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {form.formState.errors.companyName.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="copyright">
                版权声明 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="copyright"
                {...form.register('copyright')}
                placeholder="Copyright © 2025 MyApp"
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">显示在客户端底部和关于页面的版权信息</p>
              {form.formState.errors.copyright && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {form.formState.errors.copyright.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="homeUrl">
                首页地址
                <span className="text-xs text-muted-foreground ml-1">(选填)</span>
              </Label>
              <Input
                id="homeUrl"
                {...form.register('homeUrl')}
                placeholder="https://example.com"
                type="url"
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">客户端默认加载首页地址</p>
              {form.formState.errors.homeUrl && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {form.formState.errors.homeUrl.message}
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* 图标配置 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">
              图标配置
              <span className="text-xs text-muted-foreground ml-2">所有图标都是必填项</span>
            </h3>

            {/* 品牌 Logo */}
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Monitor className="w-4 h-4" />
                品牌主图标
              </div>
              <ImageUploader
                value={initialConfig?.logoUrl}
                onChange={(file) => setIconFiles((prev) => ({ ...prev, logo: file }))}
                constraints={OEM_ICON_CONFIGS.logo.constraints}
                disabled={isSubmitting}
                error={form.formState.errors.logoUrl?.message}
              />
              <p className="text-xs text-muted-foreground">{OEM_ICON_CONFIGS.logo.description}</p>
            </div>

            {/* Mac 托盘图标 */}
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Apple className="w-4 h-4" />
                Mac 托盘图标
              </div>
              <ImageUploader
                value={initialConfig?.trayIconMacUrl}
                onChange={(file) => setIconFiles((prev) => ({ ...prev, trayIconMac: file }))}
                constraints={OEM_ICON_CONFIGS.trayIconMac.constraints}
                disabled={isSubmitting}
                error={form.formState.errors.trayIconMacUrl?.message}
              />
              <p className="text-xs text-muted-foreground">{OEM_ICON_CONFIGS.trayIconMac.description}</p>
            </div>

            {/* AppWindow 托盘图标 */}
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium">
                <AppWindow className="w-4 h-4" />
                Windows 托盘图标
              </div>
              <ImageUploader
                value={initialConfig?.trayIconWindowsUrl}
                onChange={(file) => setIconFiles((prev) => ({ ...prev, trayIconWindows: file }))}
                constraints={OEM_ICON_CONFIGS.trayIconWindows.constraints}
                disabled={isSubmitting}
                error={form.formState.errors.trayIconWindowsUrl?.message}
              />
              <p className="text-xs text-muted-foreground">{OEM_ICON_CONFIGS.trayIconWindows.description}</p>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
              取消
            </Button>
            <LoadingButton type="submit" isPending={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? '保存中...' : '保存配置'}
            </LoadingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
