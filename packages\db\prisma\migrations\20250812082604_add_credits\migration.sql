/*
  Warnings:

  - You are about to drop the column `accountQuota` on the `applications` table. All the data in the column will be lost.
  - You are about to drop the column `accountQuotaExpireDate` on the `applications` table. All the data in the column will be lost.
  - You are about to drop the `twofactor` table. If the table is not empty, all the data it contains will be lost.

*/

-- AlterTable
ALTER TABLE `api_calls` MODIFY `costType` ENUM('ACCOUNT_QUOTA', 'TRAFFIC', 'CREDITS') NOT NULL;

-- AlterTable
ALTER TABLE `orders` MODIFY `quotaType` ENUM('ACCOUNT', 'TRAFFIC', 'CREDITS') NOT NULL;

-- 添加新的点数相关字段
ALTER TABLE `applications` ADD COLUMN `creditsQuota` INTEGER NOT NULL DEFAULT 0;
ALTER TABLE `applications` ADD COLUMN `creditsUsed` INTEGER NOT NULL DEFAULT 0;
ALTER TABLE `applications` ADD COLUMN `creditsExpireDate` DATETIME(3) NULL;
ALTER TABLE `applications` ADD COLUMN `platformCreditsConfig` JSON NULL;

-- 数据迁移：将现有accountQuota*5迁移到creditsQuota
UPDATE `applications` SET `creditsQuota` = `accountQuota` * 5;

-- 数据迁移：将accountQuotaExpireDate迁移到creditsExpireDate  
UPDATE `applications` SET `creditsExpireDate` = `accountQuotaExpireDate`;

-- 数据迁移：计算creditsUsed（统计每个application下的auth_accounts数量*5）
UPDATE `applications` a 
SET `creditsUsed` = (
    SELECT COUNT(*) * 5
    FROM `auth_accounts` aa 
    WHERE aa.applicationId = a.id
      AND aa.isDeleted = false
);

-- 数据迁移：将现有ACCOUNT_QUOTA枚举值改为CREDITS
UPDATE `api_calls` SET `costType` = 'CREDITS' WHERE `costType` = 'ACCOUNT_QUOTA';

-- 现在可以安全删除旧字段
ALTER TABLE `applications` DROP COLUMN `accountQuota`;
ALTER TABLE `applications` DROP COLUMN `accountQuotaExpireDate`;

-- AlterTable
ALTER TABLE `system_configs` MODIFY `category` ENUM('CRAWLER_PROXY', 'GENERAL', 'PLATFORM_CONFIG', 'CREDITS_CONFIG') NOT NULL;