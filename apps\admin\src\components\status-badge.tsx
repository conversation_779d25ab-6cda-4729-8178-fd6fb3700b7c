import { Badge } from '@coozf/ui/components/badge'
import type { ClientBuildStatus } from '@coozf/zod'
import { Clock, Play, CheckCircle, XCircle, Pause } from 'lucide-react'

interface StatusBadgeProps {
  status: ClientBuildStatus
}

export function StatusBadge({ status }: StatusBadgeProps) {
  switch (status) {
    case 'PENDING':
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          待构建
        </Badge>
      )
    case 'BUILDING':
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <Play className="w-3 h-3" />
          构建中
        </Badge>
      )
    case 'SUCCESS':
      return (
        <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3" />
          成功
        </Badge>
      )
    case 'FAILED':
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="w-3 h-3" />
          失败
        </Badge>
      )
    case 'CANCELLED':
      return (
        <Badge variant="outline" className="flex items-center gap-1">
          <Pause className="w-3 h-3" />
          已取消
        </Badge>
      )
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}
