import { RegisterV2Params } from '@coozf/zod'
import { cacheService } from './services/cache'
import { logger } from './services/logger'

type DeviceInfo = {
  socketId: string
  connectedAt: Date
  lastSeen: Date
  deviceId: string
}

type DeviceInfoV2 = DeviceInfo & RegisterV2Params

// 基于Redis的设备管理器
class DeviceManager {
  private readonly DEVICE_PREFIX = 'device:'
  private DEVICE_INDEX = 'device:index'
  private redis = cacheService.getRedisInstance()

  async connect(deviceId: string, socketId: string, data: Record<string, any> = {}) {
    // 存储设备映射关系（有这个key就表示在线）
    await this.redis
      .multi()
      .setex(
        `${this.DEVICE_PREFIX}${deviceId}`,
        60 * 60 * 24 * 30,
        JSON.stringify({
          socketId,
          connectedAt: new Date(),
          lastSeen: new Date(),
          ...data,
        }),
      )
      .zadd(this.DEVICE_INDEX, Date.now(), deviceId)
      .exec()
    // await cacheService.set(
    //   `${this.DEVICE_PREFIX}${deviceId}`,
    //   {
    //     socketId,
    //     connectedAt: new Date(),
    //     lastSeen: new Date(),
    //     ...data,
    //   },
    //   60 * 60 * 24 * 30,
    // )

    logger.info('设备注册成功', {
      type: 'device',
      event: 'register',
      deviceId,
      socketId,
    })
  }

  async disconnect(deviceId: string, socketId: string) {
    const current = await cacheService.get(`${this.DEVICE_PREFIX}${deviceId}`)
    if (current?.socketId === socketId) {
      await this.redis.multi().del(`${this.DEVICE_PREFIX}${deviceId}`).zrem(this.DEVICE_INDEX, deviceId).exec()
      logger.info('设备断开删除成功', {
        type: 'device',
        event: 'disconnect',
        deviceId,
        socketId,
      })
    }
  }

  async getSocketId(deviceId: string): Promise<string | undefined> {
    const deviceInfo = await cacheService.get(`${this.DEVICE_PREFIX}${deviceId}`)
    return deviceInfo?.socketId
  }

  async isOnline(deviceId: string) {
    return await cacheService.exists(`${this.DEVICE_PREFIX}${deviceId}`)
  }

  async getAllDevices() {
    // 使用 SCAN 命令安全地查找所有设备键值对
    const deviceKeyValues = await cacheService.scanKeyValues(`${this.DEVICE_PREFIX}*`)

    const devices: (DeviceInfo | DeviceInfoV2)[] = []
    for (const [key, deviceInfo] of deviceKeyValues) {
      if (deviceInfo) {
        const deviceId = key.replace(this.DEVICE_PREFIX, '')
        devices.push({
          deviceId,
          ...deviceInfo,
        })
      }
    }

    return devices
  }

  // 分页查询设备
  async getDevicesPaginated(page: number = 1, pageSize: number = 10, sortOrder: 'asc' | 'desc' = 'desc') {
    const start = (page - 1) * pageSize
    const end = start + pageSize - 1

    // 获取总数
    const total = await this.redis.zcard(this.DEVICE_INDEX)

    // 根据排序顺序获取设备ID
    const deviceIds =
      sortOrder === 'desc'
        ? await this.redis.zrevrange(this.DEVICE_INDEX, start, end) // 降序（最新的在前）
        : await this.redis.zrange(this.DEVICE_INDEX, start, end) // 升序（最旧的在前）

    if (deviceIds.length === 0) {
      return {
        data: [],
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      }
    }

    // 使用你现有的 scanKeyValues 逻辑批量获取
    const keys = deviceIds.map((id) => `${this.DEVICE_PREFIX}${id}`)
    const pipeline = this.redis.pipeline()
    keys.forEach((key) => pipeline.get(key))
    const values = await pipeline.exec()

    const devices: (DeviceInfo | DeviceInfoV2)[] = []
    deviceIds.forEach((deviceId, index) => {
      const value = values?.[index]?.[1] as string | null
      if (value) {
        try {
          const deviceInfo = JSON.parse(value)
          devices.push({
            deviceId,
            ...deviceInfo,
          })
        } catch (e) {
          console.error(`Failed to parse device info for ${deviceId}:`, e)
        }
      }
    })

    return {
      data: devices,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  async filterOnline(deviceIds: string[]) {
    // 使用 SCAN 命令查找所有设备键
    const keys = await cacheService.scanKeys(`${this.DEVICE_PREFIX}*`)
    const onlines = keys.map((key) => key.replace(this.DEVICE_PREFIX, '')).filter((item) => deviceIds.includes(item))

    return onlines
  }

  async getStats() {
    // 使用 SCAN 命令统计设备数量
    const keys = await cacheService.scanKeys(`${this.DEVICE_PREFIX}*`)
    return { total: keys.length }
  }

  async clear() {
    // 使用 CacheService 的 clearPattern 方法，内部已经使用 SCAN
    const clearedCount = await cacheService.clearPattern(`${this.DEVICE_PREFIX}*`)

    logger.info('所有设备连接已清理', {
      type: 'device',
      event: 'clear_all',
      clearedCount,
    })
  }
}

export const deviceManager = new DeviceManager()
