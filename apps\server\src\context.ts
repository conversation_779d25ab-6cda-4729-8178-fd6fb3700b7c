import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { db } from '@coozf/db'
import type { User } from '@coozf/db'
import type { Server as SocketIOServer } from 'socket.io'

const createContext = ({ req, res }: CreateFastifyContextOptions) => {
  const user: User | null = null
  return {
    req,
    res,
    db,
    user,
    isPublicApi: false,
    io: (req.server as any).io as SocketIOServer,
  }
}

export type Context = Awaited<ReturnType<typeof createContext>> & {
  user: User | null
  isPublicApi?: boolean
  io: SocketIOServer
}

export default createContext
