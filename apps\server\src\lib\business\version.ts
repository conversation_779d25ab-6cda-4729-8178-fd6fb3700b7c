import { db } from '@coozf/db'
import { Prisma, VersionType, Platform } from '@prisma/client'
import { CreateVersionInput, CheckUpdateParams, CheckUpdateRes, OEMConfig } from '@coozf/zod'
import { adminOssService } from '../services/oss'
import { messagePublisher } from '../services/message-publisher'
import { TRPCError } from '@trpc/server'
import semver from 'semver'
import { config, env } from '@/env'
import { paginate } from '../utils'
import { storageService } from '@coozf/huoshan'
import { ClientVersionService } from './client-version'

// YAML文件结构类型定义
interface VersionYamlFile {
  url: string
  sha512: string
  size: number
}

interface VersionYaml {
  version: string
  files: VersionYamlFile[]
  path: string
  sha512: string
  releaseDate: string
}

const updateWinFiles = ['win32-x64.yml', 'win32-ia32.yml']
const updateMacFiles = ['darwin-x64-mac.yml', 'darwin-arm64-mac.yml']
/**
 * 版本管理业务逻辑
 */
export class VersionService {
  /**
   * 创建新版本
   */
  static async createVersion(data: CreateVersionInput, publishedId: string) {
    // 验证版本号格式（简单的正则验证）
    const { version, platform, type, description, forceUpdate, fileUrl } = data
    if (!semver.valid(version)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '无效的版本号格式',
      })
    }

    const latestVersion = await db.version.findFirst({
      where: { isActive: true, platform, type },
      orderBy: { createdAt: 'desc' },
    })

    if (latestVersion && semver.gte(latestVersion.version, version)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '新版本号必须大于当前最新版本号',
      })
    }

    let finalDownloadUrl: string

    const isDesktop = type === 'DESKTOP'

    if (isDesktop) {
      // 桌面端：检查 OSS 资源是否存在并生成下载地址
      if (data.platform === 'WIN' || data.platform === 'MAC') {
        await this.checkOssResourceExists(data.version, data.platform)
        finalDownloadUrl = this.generateDesktopDownloadUrl(data.version)
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '该版本资源未构建完成',
        })
      }
    } else {
      if (fileUrl) {
        // 通过文件id 获取url
        finalDownloadUrl = fileUrl
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '文件id不能为空',
        })
      }
    }
    const newVersion = await db.version.create({
      data: {
        version: version,
        type: type,
        platform: type === 'DESKTOP' ? platform : null,
        downloadUrl: finalDownloadUrl,
        forceUpdate: forceUpdate,
        description: description,
        publishedId,
        isActive: type !== 'DESKTOP',
      },
    })

    // 自动触发 oem 的构建
    if (isDesktop) {
      const apps = await db.application.findMany({
        where: {
          oemEnabled: true,
          oemConfig: {
            not: undefined,
          },
        },
        select: {
          id: true,
          name: true,
          oemConfig: true,
          brandName: true,
        },
      })

      await Promise.all(
        apps.map((app) =>
          ClientVersionService.createClientVersion(
            {
              applicationId: app.id,
              baseVersionId: newVersion.id,
              platform: platform!,
              forceUpdate,
              description,
            },
            app,
          ),
        ),
      )
    } else {
      // 版本创建成功后，发布更新消息到 Redis
      try {
        await messagePublisher.publishVersionUpdate({
          type: type,
          version: version,
          forceUpdate: forceUpdate,
          description: description,
          downloadUrl: finalDownloadUrl,
          timestamp: Date.now(),
          publishedBy: publishedId,
        })
      } catch (error) {
        // 记录错误但不影响版本创建的成功响应
        console.error('发布版本更新消息失败:', error)
      }
    }

    return newVersion
  }

  // 桌面版发布
  static async publishDesktopVersion(id: string, publishedId: string) {
    // 检查版本是否存在
    const existingVersion = await db.version.findUnique({
      where: { id, isActive: false, type: 'DESKTOP' },
      include: {
        clientVersions: {
          distinct: ['applicationId'], // 基于 applicationId 去重
          orderBy: { createdAt: 'desc' }, // 确保取最新的
          select: {
            id: true,
            buildStatus: true,
          },
        },
      },
    })

    if (!existingVersion) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '该版本未发布',
      })
    }
    if (existingVersion.clientVersions.some((cv) => cv.buildStatus !== 'SUCCESS')) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `该版本有oem未成功构建`,
      })
    }

    // 创建软连接
    try {
      const urls = this.getDesktopPackage(existingVersion.version, existingVersion.platform!)
      await Promise.all(
        Object.entries(urls).map(([key, url]) => {
          const urlObject = new URL(url)
          return storageService.createSoftLink(
            urlObject.pathname.substring(1),
            `lj-register-${env.MODE}/${key}.${existingVersion.platform === 'MAC' ? 'dmg' : 'exe'}`,
            env.OSS_DEFAULT_BUCKET,
          )
        }),
      )
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '创建软连接失败，请稍后重试',
      })
    }

    await db.$transaction(async (tx) => {
      await tx.version.update({
        where: { id: existingVersion.id },
        data: { isActive: true },
      })
      await tx.clientVersion.updateMany({
        where: { baseVersionId: existingVersion.id },
        data: { isActive: true },
      })
    })

    // 版本创建成功后，发布更新消息到 Redis
    try {
      await messagePublisher.publishVersionUpdate({
        type: existingVersion.type,
        version: existingVersion.version,
        forceUpdate: existingVersion.forceUpdate,
        description: existingVersion.description ?? '',
        downloadUrl: existingVersion.downloadUrl,
        timestamp: Date.now(),
        publishedBy: publishedId,
      })
    } catch (error) {
      // 记录错误但不影响版本创建的成功响应
      console.error('发布版本更新消息失败:', error)
    }

    return existingVersion
  }

  /**
   * 获取版本列表（支持分页和筛选）
   */
  static async getVersionList(params: { page: number; pageSize: number; type?: VersionType; platform?: Platform }) {
    const { page, pageSize, type, platform } = params

    // 构建查询条件
    const where = {
      ...(type ? { type } : {}),
      ...(platform ? { platform } : {}),
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal() {
          return db.version.count({ where })
        },
        getItems(skip, take) {
          return db.version.findMany({
            where,
            skip,
            take,
            orderBy: { publishedAt: 'desc' },
            include: {
              admin_user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
              clientVersions: {
                distinct: ['applicationId'], // 基于 applicationId 去重
                orderBy: { createdAt: 'desc' }, // 确保取最新的
                select: {
                  id: true,
                  buildStatus: true,
                  application: {
                    select: {
                      id: true,
                      name: true,
                      brandName: true,
                      oemConfig: true,
                    },
                  },
                },
              },
            },
          })
        },
        transform(item) {
          if (item.type === 'DESKTOP') {
            return {
              ...item,
              oemSuccessCount: item.clientVersions.filter((cv) => cv.buildStatus === 'SUCCESS').length,
              downloadUrls: VersionService.getDesktopPackage(item.version, item.platform ?? undefined),
              clientVersions: item.clientVersions.map((cv) => ({
                id: cv.id,
                name: cv.application.name,
                oemConfig: cv.application.oemConfig as OEMConfig,
                buildStatus: cv.buildStatus,
              })),
            }
          }
          return {
            ...item,
            oemSuccessCount: 0,
            downloadUrls: null,
            clientVersions: [],
          }
        },
      },
    )
  }

  /**
   * 更新版本信息
   */
  static async updateVersion(
    id: string,
    data: {
      downloadUrl?: string
      forceUpdate?: boolean
      description?: string
      isActive?: boolean
    },
  ) {
    return await db.version.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除版本（软删除）
   */
  static async deleteVersion(id: string) {
    return await db.version.delete({
      where: { id },
    })
  }

  /**
   * 检查是否需要更新
   */
  static async checkForUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    let { type, appId, version } = params

    if (version && semver.lt(version, '4.10.0')) {
      // 版本号小于4.10.0时，更新到 yixiaoer oem
      appId = env.YIXIAOER_APPID
      params.appId = appId
    }

    // 当传入appId且type为DESKTOP时，查询ClientVersion表
    if (appId && type === 'DESKTOP') {
      return await this.checkClientVersionUpdate(params)
    }

    // 否则查询原有Version表（公用版本）
    return await this.checkPublicVersionUpdate(params)
  }

  /**
   * 检查公用版本更新
   */
  private static async checkPublicVersionUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    const { type, platform, version } = params
    const where: Prisma.VersionWhereInput = {
      type,
      isActive: true,
    }

    // 桌面端需要指定平台
    if (type === 'DESKTOP') {
      where.platform = platform
    }

    const latestVersion = await db.version.findFirst({
      where,
      orderBy: { publishedAt: 'desc' },
    })

    if (!latestVersion) {
      return {
        hasUpdate: false,
        latestVersion: null,
        forceUpdate: false,
      }
    }

    const { downloadUrl, forceUpdate, description, version: latestVersionNumber, id } = latestVersion
    const hasUpdate = !version || semver.gt(latestVersion.version, version)

    if (!hasUpdate) {
      return {
        hasUpdate: false,
        forceUpdate: false,
        latestVersion: {
          id,
          downloadUrl,
          forceUpdate,
          description,
          version: latestVersionNumber,
        },
      }
    }
    const forceUpdateVersions = await db.version.findMany({
      where: {
        forceUpdate: true,
        version: {
          gt: version,
          lte: latestVersionNumber,
        },
      },
      orderBy: { createdAt: 'asc' },
    })

    return {
      hasUpdate: true,
      forceUpdate: forceUpdateVersions.length > 0,
      latestVersion: {
        id: latestVersion.id,
        downloadUrl: latestVersion.downloadUrl,
        forceUpdate: latestVersion.forceUpdate,
        description: latestVersion.description,
        version: latestVersionNumber,
      },
    }
  }

  /**
   * 检查 OSS 资源是否存在
   * 支持两种调用方式：
   * 1. checkOssResourceExists(version, platform) - 根据版本和平台检查
   * 2. checkOssResourceExists(path, platform) - 直接检查指定路径和平台
   */
  static async checkOssResourceExists(versionOrPath: string, platform: Platform): Promise<void> {
    let checkUrl: string

    // 判断第一个参数是 version 还是 path
    if (versionOrPath.includes('/')) {
      // 包含 "/" 则认为是 path，直接使用
      checkUrl = versionOrPath.endsWith('/') ? versionOrPath : `${versionOrPath}/`
    } else {
      // 不包含 "/" 则认为是 version，需要拼接路径
      checkUrl = `desktop/${config.ossEnvPrefix}/${versionOrPath}/`
    }

    // 根据平台确定必需文件
    let requiredFiles: string[] = []
    if (platform === 'WIN') {
      requiredFiles = updateWinFiles
    } else if (platform === 'MAC') {
      requiredFiles = updateMacFiles
    }

    try {
      // 获取 OSS 文件列表
      const result = await adminOssService.getListObjects(checkUrl, config.OSS_DEFAULT_BUCKET)
      const existingFiles = result.data.Contents?.map((obj: any) => obj.Key) || []

      if (existingFiles.length <= 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '该版本未构建',
        })
      }

      // 检查所有必需文件是否存在
      const missingFiles = requiredFiles.filter((fileName) => !existingFiles.includes(checkUrl + fileName))

      if (missingFiles.length > 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `该版本未全部构建，缺少文件: ${missingFiles.join(', ')}`,
        })
      }
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error
      }
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: `资源检查失败: ${error instanceof Error ? error.message : error}`,
      })
    }
  }

  /**
   * 获取桌面端打包文件
   */
  static getDesktopPackage(version: string, platform?: Platform, oemName?: string) {
    const baseUrl = env.OSS_DOWNLOAD_DESKTOP_URL
    let url = `${baseUrl}/desktop/${config.ossEnvPrefix}/${version}`
    if (oemName) {
      url = `${baseUrl}/desktop/${config.ossEnvPrefix}-oem/${oemName}/${version}`
    }
    const ver = env.MODE === 'prod' ? 'latest' : version
    const tag = env.MODE === 'prod' ? '' : '-测试环境'
    const oems = oemName ? [`${oemName}-${ver}`] : [`lj-register-${ver}`]
    const macs = {
      'mac-x64': `${url}/${oems}-mac-x64${tag}.dmg`,
      'mac-arm64': `${url}/${oems}-mac-arm64${tag}.dmg`,
    }
    const wins = {
      'win-ia32': `${url}/${oems}-win-ia32${tag}.exe`,
      'win-x64': `${url}/${oems}-win-x64${tag}.exe`,
    }
    if (platform === 'WIN') {
      return wins
    } else if (platform === 'MAC') {
      return macs
    } else {
      return { ...wins, ...macs }
    }
  }

  /**
   * 生成桌面端下载地址
   */
  static generateDesktopDownloadUrl(version: string, oemName?: string): string {
    const baseUrl = env.OSS_DOWNLOAD_DESKTOP_URL
    if (oemName) {
      return `${baseUrl}/desktop/${config.ossEnvPrefix}-oem/${oemName}/${version}/`
    }
    return `${baseUrl}/desktop/${config.ossEnvPrefix}/${version}/`
  }

  /**
   * 上传浏览器插件到 OSS
   */
  private static async uploadBrowserPluginToOss(version: string, fileBuffer: Buffer): Promise<string> {
    try {
      const envPrefix = env.MODE === 'prod' ? '' : `${env.MODE}/`
      const fileName = `browser-plugin/${envPrefix}browser-plugin-${version}.zip`

      // 上传文件到 OSS
      await adminOssService.uploadFile(fileBuffer, fileName)

      // 生成下载地址
      const baseUrl = process.env.OSS_DOWNLOAD_URL || ''
      return `${baseUrl}/${fileName}`
    } catch (error) {
      throw new Error(`浏览器插件上传失败: ${error instanceof Error ? error.message : error}`)
    }
  }

  /**
   * 检查客户端版本更新（OEM版本）
   */
  private static async checkClientVersionUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    const { appId, platform, version } = params

    // 查询最新的成功构建的客户端版本
    const latestClientVersion = await db.clientVersion.findFirst({
      where: {
        applicationId: appId,
        platform: platform,
        buildStatus: 'SUCCESS',
      },
      orderBy: { createdAt: 'desc' },
      include: {
        baseVersion: {
          select: {
            id: true,
            version: true,
          },
        },
      },
    })

    if (!latestClientVersion) {
      // 如果没有客户端版本，回退到公用版本检查
      return {
        hasUpdate: false,
        forceUpdate: false,
        latestVersion: null,
      }
    }

    // 版本比较逻辑
    const hasUpdate = !version || semver.gt(latestClientVersion.baseVersion.version, version)

    if (!hasUpdate) {
      return {
        hasUpdate: false,
        forceUpdate: false,
        latestVersion: {
          id: latestClientVersion.id,
          downloadUrl: latestClientVersion.downloadUrl || '',
          forceUpdate: latestClientVersion.forceUpdate,
          description: latestClientVersion.description,
          version: latestClientVersion.baseVersion.version,
        },
      }
    }

    return {
      hasUpdate: true,
      forceUpdate: false, // 客户端版本暂不支持强制更新
      latestVersion: {
        id: latestClientVersion.id,
        downloadUrl: latestClientVersion.downloadUrl || '',
        forceUpdate: false,
        description: latestClientVersion.description,
        version: latestClientVersion.baseVersion.version,
      },
    }
  }
}
