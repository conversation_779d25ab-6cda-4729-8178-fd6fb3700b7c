import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { WebhookService, logger } from '@/lib'
import { db } from '@coozf/db'
import { DouyinWebhookBody, DouyinWebhookEvents } from './types/douyin'

// 转发给应用的标准数据格式
interface ForwardedWebhookData {
  platform: string
  event: string
  original_data: unknown
  user_info: {
    platform_user_id?: string
    user_id?: string
    account_id?: string
  }
  timestamp: number
  [key: string]: unknown
}

const PLATFORM_CODE = 'douyin'

const PLATFORM_DB_CODE = 'douyinopen'

async function findApplicationByUserId(platformUserId: string) {
  try {
    const authAccount = await db.authAccount.findFirst({
      where: {
        platformCode: PLATFORM_DB_CODE,
        platformUserId,
      },
      include: {
        application: true,
      },
    })

    // 使用显式的null检查来避免TypeScript警告
    if (!authAccount) {
      return null
    }

    return {
      authAccount,
      application: authAccount.application,
    }
  } catch (error) {
    logger.error('查找抖音关联应用失败', {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      platformUserId,
      error: error instanceof Error ? error.message : error,
    })
    return null
  }
}

async function handleDouyinWebhookEvent(body: DouyinWebhookBody, headers: Record<string, string>) {
  const { event } = body

  const { from_user_id: fromUserId } = body
  let { to_user_id: toUserId } = body

  let openId = ''

  switch (event) {
    case DouyinWebhookEvents.Unauthorize:
      openId = fromUserId
      break
    case DouyinWebhookEvents.ContractUnauthorize:
      openId = fromUserId
      break
    case DouyinWebhookEvents.ImEnterDirectMessage:
      openId = toUserId
      break
    case DouyinWebhookEvents.IMGroupSendMessage:
      openId = fromUserId
      break
    case DouyinWebhookEvents.IMGroupReceiveMessage:
      openId = toUserId
      break
    case DouyinWebhookEvents.GroupFansEvent:
      openId = toUserId
      break
    case DouyinWebhookEvents.ContractUnauthorize:
      openId = fromUserId
      break
    case DouyinWebhookEvents.EnterGroupAuditChange:
      openId = fromUserId
      break
    case DouyinWebhookEvents.IMSendMessage:
      openId = fromUserId
      break
    case DouyinWebhookEvents.IMReceiveMessage:
      openId = toUserId
      break
    case DouyinWebhookEvents.IMRecallMsg:
      openId = fromUserId
      break
    default:
      break
  }

  logger.info(`收到抖音webhook事件: ${event}`, {
    type: 'webhook',
    platform: PLATFORM_DB_CODE,
    event: event,
    original_data: body,
  })

  // 查找关联应用
  const appInfo = await findApplicationByUserId(openId)
  if (!appInfo?.application.webhookUrl) {
    logger.warn('未找到抖音用户关联的应用或webhook地址', {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      event: event,
      platformUserId: openId,
    })
    return
  }

  const forwardedData: ForwardedWebhookData = {
    platform: PLATFORM_DB_CODE,
    event,
    original_data: body,
    user_info: {
      platform_user_id: openId,
      account_id: appInfo.authAccount.id,
    },
    timestamp: Date.now(),
  }

  // 转发给应用
  await forwardWebhookToApplication(
    appInfo.application.webhookUrl,
    appInfo.application.webhookSecret,
    forwardedData,
    headers,
  )
}

async function forwardWebhookToApplication(
  webhookUrl: string,
  webhookSecret: string,
  data: ForwardedWebhookData,
  headers: Record<string, string>,
): Promise<boolean> {
  try {
    // 发送通知
    return await WebhookService.sendNotification(
      webhookUrl,
      webhookSecret,
      PLATFORM_CODE,
      data.original_data,
      data.event,
      headers,
    )
  } catch (error) {
    logger.error('转发webhook到应用失败', {
      type: 'webhook',
      platform: PLATFORM_DB_CODE,
      webhookUrl,
      eventType: data.event,
      error: error instanceof Error ? error.message : error,
    })
    return false
  }
}

/**
 * 抖音Webhook路由注册
 */
export async function douyinWebhookRoutes(app: FastifyInstance) {
  // 注册各个webhook端点
  app.post('/', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { body, headers } = request as { body: DouyinWebhookBody; headers: Record<string, string> }

      if (body.event === DouyinWebhookEvents.VerifyWebhook) {
        // 验证webhook
        return body.content
      }

      // 处理抖音的webhook事件
      await handleDouyinWebhookEvent(body, {
        'x-douyin-signature': headers['x-douyin-signature'] || '',
        'msg-id': headers['msg-id'] || '',
      })

      return reply.status(200).send('success')
    } catch (error) {
      logger.error('处理抖音webhook事件失败', {
        type: 'webhook',
        platform: PLATFORM_DB_CODE,
        error: error instanceof Error ? error.message : error,
      })
      return reply.status(500).send({
        success: false,
        message: 'Internal Server Error',
      })
    }
  })

  // webhook健康检查
  app.get('/health', async (_, reply) => {
    return reply.send({
      success: true,
      message: '小红书webhook服务正常',
      timestamp: Date.now(),
      platform: PLATFORM_CODE,
    })
  })
}
