import type { User } from '@coozf/db'

// 安全用户类型（不包含密码）
export type SafeUser = Omit<User, 'password'>

// 登录请求
export interface LoginRequest {
  email: string
  password: string
}

// 注册请求
export interface RegisterRequest {
  email: string
  password: string
  name: string
  phone?: string
}

// 登录响应
export interface LoginResponse {
  user: SafeUser
  token?: string
  sessionToken?: string
}

// JWT载荷
export interface JWTPayload {
  userId: string
  email: string
  iat: number
  exp: number
}

// OAuth相关类型
export interface OAuthState {
  appId: string
  platform: string
  timestamp: number
  random: string
  customState?: string
}

// 密码重置请求
export interface PasswordResetRequest {
  email: string
}

// 密码重置确认
export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

// 验证码请求
export interface VerificationCodeRequest {
  identifier: string // 手机号或邮箱
  type: 'login' | 'register' | 'reset_password'
}