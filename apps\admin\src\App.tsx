import { QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider } from '@tanstack/react-router'
import { queryClient } from './lib/trpc'
import { AuthProvider } from './lib/auth/auth-provider'
import { useAuth } from './lib/auth/auth-context'
import { router } from './router'
import './App.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@coozf/ui/components/sonner'
import { GlobalAlertDialogProvider } from '@coozf/ui/components/alert-dialog-provider'

function InnerApp() {
  const auth = useAuth()

  return (
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <RouterProvider router={router} context={{ auth }} />
      <Toaster position="top-right" richColors />
    </ThemeProvider>
  )
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <GlobalAlertDialogProvider>
        <AuthProvider>
          <InnerApp />
        </AuthProvider>
      </GlobalAlertDialogProvider>
    </QueryClientProvider>
  )
}

export default App
