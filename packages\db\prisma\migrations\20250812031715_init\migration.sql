-- CreateTable
CREATE TABLE `user` (
    `id` VARCHAR(191) NOT NULL,
    `name` TEXT NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `emailVerified` BOOLEAN NOT NULL,
    `image` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `phoneNumber` VARCHAR(191) NULL,
    `phoneNumberVerified` BOOLEAN NULL,

    UNIQUE INDEX `user_email_key`(`email`),
    UNIQUE INDEX `user_phoneNumber_key`(`phoneNumber`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `session` (
    `id` VARCHAR(191) NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `token` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `ipAddress` TEXT NULL,
    `userAgent` TEXT NULL,
    `userId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `session_token_key`(`token`),
    INDEX `session_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `account` (
    `id` VARCHAR(191) NOT NULL,
    `accountId` TEXT NOT NULL,
    `providerId` TEXT NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `accessToken` TEXT NULL,
    `refreshToken` TEXT NULL,
    `idToken` TEXT NULL,
    `accessTokenExpiresAt` DATETIME(3) NULL,
    `refreshTokenExpiresAt` DATETIME(3) NULL,
    `scope` TEXT NULL,
    `password` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `account_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `verification` (
    `id` VARCHAR(191) NOT NULL,
    `identifier` TEXT NOT NULL,
    `value` TEXT NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NULL,
    `updatedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `applications` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `appId` VARCHAR(64) NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `secret` VARCHAR(255) NOT NULL,
    `status` ENUM('ACTIVE', 'SUSPENDED', 'DELETED') NOT NULL DEFAULT 'ACTIVE',
    `webhookUrl` VARCHAR(500) NULL,
    `webhookSecret` VARCHAR(255) NOT NULL,
    `accountQuota` INTEGER NOT NULL DEFAULT 0,
    `trafficQuotaGB` DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    `trafficUsedGB` DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    `accountQuotaExpireDate` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `oemConfig` JSON NULL,
    `oemEnabled` BOOLEAN NOT NULL DEFAULT false,
    `brandName` VARCHAR(20) NULL,

    UNIQUE INDEX `applications_appId_key`(`appId`),
    UNIQUE INDEX `applications_brandName_key`(`brandName`),
    INDEX `applications_userId_idx`(`userId`),
    INDEX `applications_appId_idx`(`appId`),
    INDEX `applications_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `auth_accounts` (
    `id` VARCHAR(191) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `platformCode` VARCHAR(20) NOT NULL,
    `platformUserId` VARCHAR(100) NOT NULL,
    `platformUserName` VARCHAR(100) NOT NULL,
    `platformAvatar` VARCHAR(500) NULL,
    `platformCookieHash` LONGTEXT NOT NULL,
    `isDeleted` BOOLEAN NOT NULL DEFAULT false,
    `state` VARCHAR(100) NULL,
    `scope` VARCHAR(200) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `authType` ENUM('COOKIE', 'TOKEN') NOT NULL DEFAULT 'COOKIE',
    `platformTokenExpiresAt` DATETIME(3) NULL,

    INDEX `auth_accounts_applicationId_idx`(`applicationId`),
    INDEX `auth_accounts_platformUserId_idx`(`platformUserId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `api_calls` (
    `id` VARCHAR(191) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `endpoint` VARCHAR(255) NOT NULL,
    `method` VARCHAR(10) NOT NULL,
    `costType` ENUM('ACCOUNT_QUOTA', 'TRAFFIC') NOT NULL,
    `costAmount` DECIMAL(10, 2) NOT NULL,
    `statusCode` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `api_calls_applicationId_idx`(`applicationId`),
    INDEX `api_calls_endpoint_idx`(`endpoint`),
    INDEX `api_calls_costType_idx`(`costType`),
    INDEX `api_calls_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `orders` (
    `id` VARCHAR(191) NOT NULL,
    `orderNo` VARCHAR(32) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `amount` DECIMAL(10, 2) NOT NULL,
    `source` ENUM('SYSTEM') NOT NULL DEFAULT 'SYSTEM',
    `type` ENUM('PURCHASE', 'GIFT') NOT NULL,
    `paymentMethod` ENUM('BANK_TRANSFER', 'ALI_PAY', 'WECHAT_PAY', 'BALANCE', 'NONE') NOT NULL DEFAULT 'NONE',
    `status` ENUM('PENDING', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'COMPLETED',
    `invoiceRequested` BOOLEAN NOT NULL DEFAULT false,
    `remarks` VARCHAR(500) NULL,
    `quotaType` ENUM('ACCOUNT', 'TRAFFIC') NOT NULL,
    `quotaAmount` DECIMAL(10, 2) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `orders_orderNo_key`(`orderNo`),
    INDEX `orders_orderNo_idx`(`orderNo`),
    INDEX `orders_status_idx`(`status`),
    INDEX `orders_type_idx`(`type`),
    INDEX `orders_createdAt_idx`(`createdAt`),
    INDEX `orders_applicationId_fkey`(`applicationId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `admin_user` (
    `id` VARCHAR(191) NOT NULL,
    `name` TEXT NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `emailVerified` BOOLEAN NOT NULL,
    `image` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `twoFactorEnabled` BOOLEAN NULL,
    `username` VARCHAR(191) NULL,
    `displayUsername` TEXT NULL,
    `banReason` TEXT NULL,
    `banned` BOOLEAN NULL,
    `role` TEXT NULL,

    UNIQUE INDEX `admin_user_email_key`(`email`),
    UNIQUE INDEX `admin_user_username_key`(`username`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `admin_session` (
    `id` VARCHAR(191) NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `token` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,
    `ipAddress` TEXT NULL,
    `userAgent` TEXT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `impersonatedBy` TEXT NULL,

    UNIQUE INDEX `admin_session_token_key`(`token`),
    INDEX `admin_session_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `admin_account` (
    `id` VARCHAR(191) NOT NULL,
    `accountId` TEXT NOT NULL,
    `providerId` TEXT NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `accessToken` TEXT NULL,
    `refreshToken` TEXT NULL,
    `idToken` TEXT NULL,
    `accessTokenExpiresAt` DATETIME(3) NULL,
    `refreshTokenExpiresAt` DATETIME(3) NULL,
    `scope` TEXT NULL,
    `password` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL,
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `admin_account_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `admin_verification` (
    `id` VARCHAR(191) NOT NULL,
    `identifier` TEXT NOT NULL,
    `value` TEXT NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NULL,
    `updatedAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `versions` (
    `id` VARCHAR(191) NOT NULL,
    `version` VARCHAR(20) NOT NULL,
    `type` ENUM('DESKTOP', 'BROWSER_PLUGIN', 'RPA', 'CRAWLER') NOT NULL,
    `platform` ENUM('WIN', 'MAC') NULL,
    `downloadUrl` VARCHAR(500) NOT NULL,
    `forceUpdate` BOOLEAN NOT NULL DEFAULT false,
    `description` TEXT NULL,
    `publishedId` VARCHAR(191) NOT NULL,
    `publishedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `versions_type_idx`(`type`),
    INDEX `versions_platform_idx`(`platform`),
    INDEX `versions_isActive_idx`(`isActive`),
    INDEX `versions_publishedAt_idx`(`publishedAt`),
    INDEX `versions_publishedId_fkey`(`publishedId`),
    UNIQUE INDEX `versions_version_type_platform_key`(`version`, `type`, `platform`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `publish_records` (
    `id` VARCHAR(191) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `publishId` VARCHAR(64) NOT NULL,
    `estimatedTraffic` DECIMAL(10, 2) NOT NULL,
    `actualTraffic` DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    `accountCount` INTEGER NOT NULL DEFAULT 0,
    `pendingCount` INTEGER NOT NULL DEFAULT 0,
    `successCount` INTEGER NOT NULL DEFAULT 0,
    `failedCount` INTEGER NOT NULL DEFAULT 0,
    `status` ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PARTIAL') NOT NULL DEFAULT 'PENDING',
    `metadata` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `publish_records_publishId_key`(`publishId`),
    INDEX `publish_records_applicationId_idx`(`applicationId`),
    INDEX `publish_records_publishId_idx`(`publishId`),
    INDEX `publish_records_status_idx`(`status`),
    INDEX `publish_records_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `traffic_usage` (
    `id` VARCHAR(191) NOT NULL,
    `publishRecordId` VARCHAR(191) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `authAccountId` VARCHAR(191) NOT NULL,
    `taskId` VARCHAR(64) NOT NULL,
    `estimatedTraffic` DECIMAL(10, 2) NOT NULL,
    `actualTraffic` DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    `status` ENUM('PENDING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING',
    `errorMessage` TEXT NULL,
    `metadata` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `traffic_usage_taskId_key`(`taskId`),
    INDEX `traffic_usage_publishRecordId_idx`(`publishRecordId`),
    INDEX `traffic_usage_applicationId_idx`(`applicationId`),
    INDEX `traffic_usage_authAccountId_idx`(`authAccountId`),
    INDEX `traffic_usage_taskId_idx`(`taskId`),
    INDEX `traffic_usage_status_idx`(`status`),
    INDEX `traffic_usage_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `client_versions` (
    `id` VARCHAR(191) NOT NULL,
    `applicationId` VARCHAR(191) NOT NULL,
    `baseVersionId` VARCHAR(191) NOT NULL,
    `platform` ENUM('WIN', 'MAC') NOT NULL,
    `buildStatus` ENUM('PENDING', 'BUILDING', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
    `downloadUrl` VARCHAR(500) NULL,
    `buildLog` LONGTEXT NULL,
    `errorMessage` TEXT NULL,
    `gitlabProjectId` INTEGER NULL,
    `gitlabPipelineId` INTEGER NOT NULL,
    `gitlabCommitSha` VARCHAR(100) NULL,
    `gitlabWebUrl` VARCHAR(500) NULL,
    `buildStartedAt` DATETIME(3) NULL,
    `buildCompletedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `description` TEXT NULL,
    `gitlabRef` VARCHAR(100) NULL,

    INDEX `client_versions_applicationId_idx`(`applicationId`),
    INDEX `client_versions_buildStatus_idx`(`buildStatus`),
    INDEX `client_versions_createdAt_idx`(`createdAt`),
    INDEX `client_versions_gitlabPipelineId_idx`(`gitlabPipelineId`),
    INDEX `client_versions_baseVersionId_fkey`(`baseVersionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `system_configs` (
    `id` VARCHAR(191) NOT NULL,
    `key` VARCHAR(100) NOT NULL,
    `category` ENUM('CRAWLER_PROXY', 'GENERAL') NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `value` JSON NOT NULL,
    `isEnabled` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `system_configs_key_key`(`key`),
    INDEX `system_configs_key_idx`(`key`),
    INDEX `system_configs_category_idx`(`category`),
    INDEX `system_configs_isEnabled_idx`(`isEnabled`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `twofactor` (
    `id` VARCHAR(191) NOT NULL,
    `secret` TEXT NOT NULL,
    `backupCodes` TEXT NOT NULL,
    `userId` VARCHAR(191) NOT NULL,

    INDEX `twoFactor_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `session` ADD CONSTRAINT `session_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `account` ADD CONSTRAINT `account_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `applications` ADD CONSTRAINT `applications_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `auth_accounts` ADD CONSTRAINT `auth_accounts_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `api_calls` ADD CONSTRAINT `api_calls_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `admin_session` ADD CONSTRAINT `admin_session_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `admin_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `admin_account` ADD CONSTRAINT `admin_account_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `admin_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `versions` ADD CONSTRAINT `versions_publishedId_fkey` FOREIGN KEY (`publishedId`) REFERENCES `admin_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `publish_records` ADD CONSTRAINT `publish_records_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `traffic_usage` ADD CONSTRAINT `traffic_usage_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `traffic_usage` ADD CONSTRAINT `traffic_usage_authAccountId_fkey` FOREIGN KEY (`authAccountId`) REFERENCES `auth_accounts`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `traffic_usage` ADD CONSTRAINT `traffic_usage_publishRecordId_fkey` FOREIGN KEY (`publishRecordId`) REFERENCES `publish_records`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `client_versions` ADD CONSTRAINT `client_versions_applicationId_fkey` FOREIGN KEY (`applicationId`) REFERENCES `applications`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `client_versions` ADD CONSTRAINT `client_versions_baseVersionId_fkey` FOREIGN KEY (`baseVersionId`) REFERENCES `versions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `twofactor` ADD CONSTRAINT `twoFactor_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `admin_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
