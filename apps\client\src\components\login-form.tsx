'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect, useState } from 'react'
import { trpc } from '../lib/trpc'
import { Input } from '@coozf/ui/components/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@coozf/ui/components/form'
import { toast } from 'sonner'
import { authClient } from '@/lib/auth/auth-client'
import { useMutation } from '@tanstack/react-query'
import { LoadingButton } from '@coozf/ui/components/loading'

const phoneSchema = z
  .string()
  .min(1, '请输入手机号码')
  .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码')

// 手机登录表单验证 schema
const phoneLoginSchema = z.object({
  phone: phoneSchema,
  code: z.string().min(1, '请输入验证码').length(6, '验证码必须是6位'),
})
type PhoneLoginForm = z.infer<typeof phoneLoginSchema>

export function LoginForm() {
  const [countdown, setCountdown] = useState(0)

  // 手机登录表单
  const phoneForm = useForm<PhoneLoginForm>({
    resolver: zodResolver(phoneLoginSchema),
    defaultValues: {
      phone: '',
      code: '',
    },
  })

  // hasSendCode
  const [hasSendCode, setHasSendCode] = useState(false)
  const phone = phoneForm.watch('phone')

  useEffect(() => {
    if (phoneSchema.safeParse(phone).success) {
      setHasSendCode(true)
    }
  }, [phone])

  // 手机号登录 mutation
  const phoneLoginMutation = useMutation({
    mutationFn: (values: PhoneLoginForm) => {
      return authClient.phoneNumber.verify({
        phoneNumber: values.phone,
        code: values.code,
      })
    },
    onSuccess: () => {
      toast.success('登录成功')
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 发送验证码 mutation
  const sendSmsMutation = useMutation(trpc.auth.sendSms.mutationOptions({
    onSuccess: (data) => {
      toast.success('发送成功', {
        description: data.code,
      })
      if (data.code) {
        phoneForm.setValue('code', data.code)
      }
      // 开始倒计时
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  }))

  // 手机登录提交处理
  const onPhoneSubmit = (values: PhoneLoginForm) => {
    phoneLoginMutation.mutate(values)
  }

  // 发送验证码处理
  const handleSendCode = () => {
    if (countdown === 0 && hasSendCode) {
      sendSmsMutation.mutate({ phone, type: 'login' })
    }
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col items-center gap-4 text-center -mt-5">
        <img
          src="/logo.svg"
          alt="登录"
          className="h-11 w-auto relative -top-[50px]"
        />
      </div>

      <Form {...phoneForm}>
        <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="flex flex-col gap-8">
          <FormField
            control={phoneForm.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>手机号码</FormLabel>
                <FormControl>
                  <Input type="tel" placeholder="请输入您的手机号码" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={phoneForm.control}
            name="code"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>验证码</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input type="text" placeholder="请输入验证码" className="flex-1" {...field} />
                  </FormControl>
                  <LoadingButton
                    type="button"
                    variant="outline"
                    onClick={handleSendCode}
                    disabled={countdown > 0 || !hasSendCode}
                    isPending={sendSmsMutation.isPending}
                    className="shrink-0 min-w-[100px]"
                  >
                    {countdown > 0 ? `${countdown}s后重发` : '获取验证码'}
                  </LoadingButton>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <LoadingButton type="submit" className="w-full" isPending={phoneLoginMutation.isPending}>
            登录
          </LoadingButton>
        </form>
      </Form>

      <div className="text-center text-sm text-muted-foreground">
        <p>
          登录即表示您同意我们的{' '}
          <a href="#" className="text-primary hover:underline">
            服务条款
          </a>{' '}
          和{' '}
          <a href="#" className="text-primary hover:underline">
            隐私政策
          </a>
        </p>
      </div>
    </div>
  )
}
