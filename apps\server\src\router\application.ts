import { z } from 'zod'
import { router } from '@/trpc'
import type { Prisma } from '@coozf/db'
import { CreateApplicationSchema, UpdateApplicationSchema, ApplicationListSchema } from '@coozf/zod'
import { ApplicationService, QuotaService, PlatformCreditsService } from '@/lib'
import { applicationProcedure, applicationWithQuotaProcedure, protectedProcedure } from '@/procedure'
import { randomBytes } from 'crypto'
import { applicationCache } from '@/lib/services/cache'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: protectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    return ApplicationService.getApplicationList({
      ...input,
      userId: ctx.user.id,
      isUser: false,
    })
  }),

  // 创建应用
  create: protectedProcedure.input(CreateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 生成唯一的应用ID和密钥
    const appId = await ApplicationService.generateUniqueAppId()
    const { secret, hashedSecret } = await ApplicationService.generateAndHashSecret()
    const webhookSecret = `whsec_${randomBytes(16).toString('hex')}`

    const newApplication = await ctx.db.application.create({
      data: {
        ...input,
        appId,
        secret: hashedSecret, // 存储加密后的 Secret
        webhookSecret, // 存储 webhook 密钥
        userId: ctx.user.id,
      },
    })

    // 返回应用信息，包含明文 Secret（仅此一次）
    return {
      ...newApplication,
      secret, // 明文 Secret，仅在创建时返回
    }
  }),

  // 获取单个应用详情
  byId: applicationWithQuotaProcedure.query(async ({ ctx }) => {
    return ctx.applicationWithQuota
  }),

  // 更新应用
  update: applicationProcedure.input(UpdateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 只更新允许的字段
    const updateData: Prisma.ApplicationUpdateInput = {}
    if (input.name) updateData.name = input.name
    if (input.description !== undefined) updateData.description = input.description
    if (input.webhookUrl !== undefined) updateData.webhookUrl = input.webhookUrl
    if (input.status) updateData.status = input.status

    const updated = await ctx.db.application.update({
      where: { id: ctx.applicationId },
      data: updateData,
    })

    await applicationCache.del(ctx.applicationId)

    return updated
  }),

  // 删除应用
  delete: applicationProcedure.mutation(async ({ ctx }) => {
    await ctx.db.application.delete({
      where: { id: ctx.applicationId },
    })

    return { success: true }
  }),

  // 重新生成 Secret
  regenerateSecret: applicationProcedure.mutation(async ({ ctx }) => {
    // 生成新的 Secret
    const { secret, hashedSecret } = await ApplicationService.generateAndHashSecret()

    // 更新数据库中的 Secret
    const updated = await ctx.db.application.update({
      where: { id: ctx.applicationId },
      data: { secret: hashedSecret },
    })

    // 返回新的明文 Secret（仅此一次）
    return {
      ...updated,
      secret, // 明文 Secret，仅在重新生成时返回
      message: '密钥已重新生成，请妥善保存，此密钥不会再次显示',
    }
  }),

  // 获取应用趋势数据
  getApplicationTrends: applicationProcedure
    .input(
      z.object({
        days: z.number().min(1).max(90).default(30),
      }),
    )
    .query(async ({ ctx, input }) => {
      // 计算精确的日期范围，确保包含完整的天数
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 999) // 设为今天结束
      
      const startDate = new Date(endDate)
      startDate.setDate(startDate.getDate() - input.days + 1) // 包含今天，所以是 +1
      startDate.setHours(0, 0, 0, 0) // 设为开始日期的开始

      // 使用Prisma获取新增账号数据
      const accountsInRange = await ctx.db.authAccount.findMany({
        where: {
          applicationId: ctx.applicationId,
          isDeleted: false,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      })

      // 按日期分组统计账号
      const accountStatsMap = new Map<string, number>()
      accountsInRange.forEach((account) => {
        const dateStr = account.createdAt.toISOString().split('T')[0] ?? ''
        accountStatsMap.set(dateStr, (accountStatsMap.get(dateStr) ?? 0) + 1)
      })

      const accountTrends = Array.from(accountStatsMap.entries()).map(([dateStr, count]) => ({
        date: new Date(dateStr),
        count
      }))

      // 使用Prisma获取流量使用数据
      const publishRecordsInRange = await ctx.db.publishRecord.findMany({
        where: {
          applicationId: ctx.applicationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
          actualTraffic: true,
        },
      })

      // 按日期分组统计流量
      const trafficStatsMap = new Map<string, number>()
      publishRecordsInRange.forEach((record) => {
        const dateStr = record.createdAt.toISOString().split('T')[0] ?? ''
        const currentTraffic = trafficStatsMap.get(dateStr) ?? 0
        trafficStatsMap.set(dateStr, currentTraffic + Number(record.actualTraffic))
      })

      const trafficTrends = Array.from(trafficStatsMap.entries()).map(([dateStr, traffic]) => ({
        date: new Date(dateStr),
        traffic
      }))

      // 使用Prisma获取API调用数据
      const apiCallsInRange = await ctx.db.apiCall.findMany({
        where: {
          applicationId: ctx.applicationId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      })

      // 按日期分组统计API调用
      const apiStatsMap = new Map<string, number>()
      apiCallsInRange.forEach((call) => {
        const dateStr = call.createdAt.toISOString().split('T')[0] ?? ''
        apiStatsMap.set(dateStr, (apiStatsMap.get(dateStr) ?? 0) + 1)
      })

      const apiTrends = Array.from(apiStatsMap.entries()).map(([dateStr, count]) => ({
        date: new Date(dateStr),
        count
      }))

      // 合并所有数据到日期映射
      const dailyStatsMap = new Map<
        string,
        {
          apiCalls: number
          accountCount: number
          trafficGB: number
        }
      >()

      // 处理新增账号数据
      accountTrends.forEach((trend) => {
        const dateStr = trend.date.toISOString().split('T')[0] ?? ''
        const existing = dailyStatsMap.get(dateStr) ?? { apiCalls: 0, accountCount: 0, trafficGB: 0 }
        existing.accountCount = trend.count
        dailyStatsMap.set(dateStr, existing)
      })

      // 处理流量使用数据
      trafficTrends.forEach((trend) => {
        const dateStr = trend.date.toISOString().split('T')[0] ?? ''
        const existing = dailyStatsMap.get(dateStr) ?? { apiCalls: 0, accountCount: 0, trafficGB: 0 }
        existing.trafficGB = trend.traffic
        dailyStatsMap.set(dateStr, existing)
      })

      // 处理API调用数据
      apiTrends.forEach((trend) => {
        const dateStr = trend.date.toISOString().split('T')[0] ?? ''
        const existing = dailyStatsMap.get(dateStr) ?? { apiCalls: 0, accountCount: 0, trafficGB: 0 }
        existing.apiCalls = trend.count
        dailyStatsMap.set(dateStr, existing)
      })

      // 生成完整的日期序列，确保返回连续的时间序列数据
      const trends: {
        date: string
        apiCalls: number
        accountCount: number
        trafficGB: number
      }[] = []

      // 从最早的日期开始生成序列
      for (let i = input.days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        date.setHours(0, 0, 0, 0) // 确保日期一致性
        const dateStr = date.toISOString().split('T')[0] ?? ''

        const existing = dailyStatsMap.get(dateStr)
        trends.push({
          date: dateStr,
          apiCalls: existing?.apiCalls ?? 0,
          accountCount: existing?.accountCount ?? 0,
          trafficGB: Number((existing?.trafficGB ?? 0).toFixed(2)), // 保留两位小数
        })
      }

      return trends
    }),

  // 获取应用配额概览
  getQuotaOverview: applicationProcedure.query(async ({ ctx }) => {
    return await QuotaService.getQuotaOverview(ctx.applicationId)
  }),

  // 获取平台点数配置
  getPlatformConfigs: protectedProcedure.query(async () => {
    return await PlatformCreditsService.getAllPlatformConfigs()
  }),
})
