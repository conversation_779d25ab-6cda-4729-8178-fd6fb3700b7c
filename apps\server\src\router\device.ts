import { z } from 'zod'
import { router, publicProcedure } from '../trpc'
import { deviceManager } from '../lib/device-manager'

export const deviceRouter = router({

  // 获取在线设备
  listOnline: publicProcedure
    .query(async () => {
      return await deviceManager.getAllDevices()
    }),

  // 设备状态
  isOnline: publicProcedure
    .input(z.object({ deviceId: z.string() }))
    .query(async ({ input }) => {
      const isOnline = await deviceManager.isOnline(input.deviceId)
      return { isOnline }
    }),

  // 统计信息
  getStats: publicProcedure
    .query(async () => {
      return await deviceManager.getStats()
    })
})