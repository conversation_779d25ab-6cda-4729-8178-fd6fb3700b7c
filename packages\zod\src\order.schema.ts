import { z } from 'zod'

// 订单枚举
export const OrderSourceEnum = z.enum(['SYSTEM'])
export const OrderTypeEnum = z.enum(['PURCHASE', 'GIFT'])
export const PaymentMethodEnum = z.enum(['BANK_TRANSFER'])
export const OrderStatus = z.enum(['PENDING', 'COMPLETED', 'CANCELLED'])
export const QuotaType = z.enum(['ACCOUNT', 'TRAFFIC'])

// 订单相关的 Zod Schema
export const CreateOrderSchema = z.object({
  applicationId: z.string().min(1, '应用ID不能为空'),
  amount: z.number().min(0.01, '金额必须大于0'),
  type: OrderTypeEnum,
  paymentMethod: PaymentMethodEnum.optional(),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
  // 配额相关字段
  quotaType: QuotaType,
  quotaAmount: z.number().min(0.01, '配额数量必须大于0'),
})

export const UpdateOrderSchema = z.object({
  status: OrderStatus.optional(),
  invoiceRequested: z.boolean().optional(),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

export const SelectOrderSchema = z.object({
  id: z.string(),
  orderNo: z.string(),
  userId: z.string(),
  applicationId: z.string(),
  antCoins: z.number(),
  amount: z.number(),
  source: OrderSourceEnum,
  type: OrderTypeEnum,
  paymentMethod: PaymentMethodEnum,
  status: OrderStatus,
  invoiceRequested: z.boolean(),
  remarks: z.string().nullable(),
  // 配额相关字段
  quotaType: QuotaType.nullable(),
  quotaAmount: z.number().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 订单列表查询参数
export const OrderListSchema = z.object({
  applicationId: z.string().optional(),
  status: OrderStatus.optional(),
  type: OrderTypeEnum.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(), // 搜索订单号
})

// 管理员充值Schema
export const AdminRechargeSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  amount: z.number().min(0.01, '充值金额必须大于0'),
  type: OrderTypeEnum.default('GIFT'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

// 管理员配额订单Schema
export const AdminAccountQuotaOrderSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  accountCount: z.number().min(1, '账号数量必须大于0'),
  expireDate: z.date(), // 账号配额到期时间（必填，会更新到Application表）
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

export const AdminTrafficQuotaOrderSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  trafficGB: z.number().min(0.1, '流量配额必须大于0.1GB'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

// 管理员点数配额订单Schema
export const AdminCreditsQuotaOrderSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  creditsAmount: z.number().min(1, '点数数量必须大于0'),
  expireDate: z.date().optional(), // 点数配额到期时间（可选）
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

// 类型推导
export type CreateOrderInput = z.infer<typeof CreateOrderSchema>
export type UpdateOrderInput = z.infer<typeof UpdateOrderSchema>
export type SelectOrderOutput = z.infer<typeof SelectOrderSchema>
export type OrderListParams = z.infer<typeof OrderListSchema>
export type AdminRechargeParams = z.infer<typeof AdminRechargeSchema>
export type AdminAccountQuotaOrderInput = z.infer<typeof AdminAccountQuotaOrderSchema>
export type AdminTrafficQuotaOrderInput = z.infer<typeof AdminTrafficQuotaOrderSchema>
export type AdminCreditsQuotaOrderInput = z.infer<typeof AdminCreditsQuotaOrderSchema>

