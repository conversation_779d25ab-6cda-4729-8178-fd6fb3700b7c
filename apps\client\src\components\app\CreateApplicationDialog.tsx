import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { LoadingButton } from '@coozf/ui/components/loading'
import { trpc } from '@/lib/trpc'
import { useMutation } from '@tanstack/react-query'

const createApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
})

type CreateApplicationForm = z.infer<typeof createApplicationSchema>

interface CreateApplicationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (data: { name: string; secret: string }) => void
}

export function CreateApplicationDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: CreateApplicationDialogProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<CreateApplicationForm>({
    resolver: zodResolver(createApplicationSchema),
    defaultValues: {
      name: '',
    },
  })

  const nameValue = watch('name')

  const createApplicationMutation = useMutation(trpc.application.create.mutationOptions({
    onSuccess: (data) => {
      onSuccess(data)
      reset()
    },
  }))

  const onSubmit = (data: CreateApplicationForm) => {
    createApplicationMutation.mutate(data)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[400px]">
        <DialogHeader>
          <DialogTitle>创建应用</DialogTitle>
          <DialogDescription>
            创建一个新的应用程序，系统将自动生成应用ID和密钥
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              应用名称 <span className="text-destructive">*</span>
            </Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="请输入应用名称"
              maxLength={10}
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{errors.name?.message}</span>
              <span>{nameValue?.length || 0}/10</span>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <LoadingButton
              type="submit"
              isPending={createApplicationMutation.isPending}
            >
              创建应用
            </LoadingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}