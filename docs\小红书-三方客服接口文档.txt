小红书-三方客服接口文档

文档更新
时间	更新内容
2025.5.21	1、解决聚光账号绑定三方的脏数据问题
2、解决了企业号在私信通授权给其他企业号后无法在三方回复意向评论的问题，三方接口的处理方案与KOS意向评论一致
2025.4.9	变更员工号与企业号间关于意向评论的逻辑
1、员工号的意向评论统一需要通过企业号UID进行获取，员工号UID获取将返回为空
2、意向笔记新增了笔记作者UID的字段，可通过该字段区分该评论来源于哪个账号
3、若为KOS意向评论，调用私信发送接口针对意向评论回复时，需要在user_id和from_user_id字段中均填入KOS UID，否则将无法发送成功
2025.2.28	支持小红书最新组件-交易卡，发送后可支持用户直接唤端
产品功能与介绍详见私信获客工具_交易卡_产品说明及操作手册（定向邀测中）
2025.02.11	支持小红书企业员工号KOS账号类型小红书三方IM支持KOS企业员工号接口文档
1、新增接口查询绑定的KOS账号列表
2、支持查询KOS账号物料
3、支持接收/发送KOS账号消息
2024.12.25	1、推送三方数据新增“企微”留资成功字段数据，用户通过企微名片添加成功的会返回对应的企微获客工具链接的名称与ID
2、新增组件发送额度查询接口（该接口实际已废弃，请勿做处理）
2024.10.28	推送三方数据新增“个微复制”字段，0否1是
2024.10.24	名片卡中新增“留资卡”类型 1024更新 支持发送名片
2024.09.20	新增“名片卡片”对接接口，支持从三方发送企微和个微名片（但该能力目前在小红书是白名单开放给商业化客户使用）若此前已完成链路开发对接的服务商，可以直接用下述文档补充开发即可0920更新 支持发送名片；若是新接入服务商看下文即可。
2024.08.30	新平台接入完成加白登记，若有需要请在该文档提交信息：小红书三方IM对接准入服务商/品牌商家域名信息收集
2024.07.08	更新小红书Emoji及对应中文映射
2024.07.04	新增“三方客服-留资和广告归因数据推送“接口，用户进线、开口、留资后均有对应广告信息（广告主ID、计划ID、创意ID）和用户留资数据推到三方指定地址
2024.06.14	增加“三方客服-留资数据回传“接口，实现客户在三方工具标注客资后可回传到聚光平台
2024.05.09	移除三方绑定接口，账户绑定由聚光实现；三方提供获取账号和通知绑定接口
2024.04.25	添加 "三方客服-广告主授权账户绑定" 接口
2024.04.22	三方客服-接收消息接口，消息体中增加c端用户昵称、头像

聚光平台-添加跳转绑定URL说明
域名格式：三方登陆域名?jumpUrl=https://ad.xiaohongshu.com/api/leona/three/im/account/add 
字段说明：
1.三方登陆成功后，跳转jumpUrl链接时三方动态拼装appId与token 例如：appId=46&token=XXX；
2.token中为加密信息【使用统一加解密算法】，token内容必须包含账户编码；
3.由于token中有特殊字符，在url拼接前对token内容进行urlEncode操作；

token内容格式如下：


广告主授权绑定账户通知三方客服
接口： 三方域名/api/open/im/third/bind_account
请求方式：POST
数据格式：JSON
请求参数
Body
参数	类型	是否必填	描述	示例
content	string	是	加密后内容	
content内容解密后字段说明
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（聚光授权客户userId）	"660a6b96a080b200014925de"
nick_name	string	是	用户昵称	
token	string	是	前往鉴权时的token信息	
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
 响应示例



广告主授权解绑账户通知三方客服
接口： 三方域名/api/open/im/third/unbind_account
请求方式：POST
数据格式：JSON

请求参数
Body
参数	类型	是否必填	描述	示例
content	string	是	加密后内容	
content内容解密后字段说明
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（聚光授权客户userId）	"660a6b96a080b200014925de"
app_id	long	是	开放平台应用ID	46
account_code	string	是	广告主在三方的注册的账号编码	"<EMAIL>"
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
 响应示例


广告主授权绑定KOS账户通知三方客服
员工账号（KOS）绑定模式升级版说明手册【对外】
接口： 三方域名/api/open/im/auth/bind_user/event
请求方式：POST
数据格式：JSONx
请求参数
Body
参数	类型	是否必填	描述	示例
content	string	是	加密后内容	
content内容解密后字段说明
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（聚光授权客户userId）	660a6b96a080b200014925de
auth_status	int	是	绑定状态	2-已生效，4-已取消
kos_nick_name	string	是	KOS用户昵称	
kos_user_id	string	是	KOS的用户id	660a6b96a080b200014925de
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
 响应示例


查询广告主授权绑定的KOS账户
请注意，KOS授权信息仅当有更新时才会同步至第三方（有新绑定或解绑时），而KOS授权绑定的范围与小红书平台的私信通矩阵号同步对齐，因此会有部分客户存在已有KOS账号绑定的情况，需要有单独的产品逻辑触发通过该接口拉取一次全量的授权绑定范围，开放平台有授权或更新授权时拉取即可
接口： /api/open/im/auth/bind_users
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId）	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	落地页数据	
				
total	long	是	落地页总数	
				
user_id	string	是	kos user id	
nick_name	string	是	昵称	
avatarImg	string	是	头像url	
响应示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
 响应示例





三方客服-发送消息接口
三方客服通过小红书商业化开放平台申请开发者权限
通过消息发送接口回复小红书用户私信消息
推送消息时机：
1、广告流量进线的用户通过小红书笔记详情页中的组件（胶囊、评论区）或主页私信入口进入客户私信，触发hint提示“对方通过****笔记进入私信，快去联系吧”，同时进行推送
2、用户通过其他路径进入私信，未触发hint提示的，若客户配置了欢迎语，发出欢迎语同时进行推送
3、用户通过其他路径进入私信，未触发hint提示的，客户未配置欢迎语，当用户或客户一方发出消息后，进行推送
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/third/send
请求方式：POST
数据格式：JSON

请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body
B2C消息，user_id和from_user_id必须一致
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（聚光授权客户userId）	"660a6b96a080b200014925de"
request_id	string	是	唯一id，例如uuid，接口成功后返回	"1712039938271-23496543-427c-bee9"
message_type	string	是	消息类型枚举[客服发送消息，会否也有 SMILES 表情消息，普通表情（Text）和自定义表情(URL)][客服侧暂不支持]
TEXT：文本
IMAGE：图片
VIDEO：视频
CARD：卡片
REVOKE：撤回消息	
from_user_id	string	是	客户userId（聚光平台授权绑定的账号，包括企业号和KOS员工号的userId）	"660a6b96a080b200014925de"
to_user_id	string	是	和客户对话的C端用户的小红书user_id	"660a6b96a080b200014925de"
third_account_id	string	是	客户关联三方账号id	""
timestamp	long	是	时间戳，毫秒	*************
content	string	是	消息内容构造，加密后的消息内容	"Q9U/Mpfv/w3w9xinsKIbrboB9j9Moe5oTVbbibzxzPqFoXNH1OtlKsLE5EyxXyM0x7/KeKpK1lEc+OSs747vVhleteP38gOhFN8R9GYG7/+Tw5fOqURH1wS2MIG33hGmcrdXaRio0TkXWkNd0fsIiLCve0GC78qPmQ7Px4xQbkw="
				

请求示例
文本消息


图片消息



视频消息


卡片消息-笔记


卡片消息-落地页


卡片消息-意向评论回复
1、每条评论只能回复一次，评论时间超过14天、失效等不支持回复
2、意向评论的私信回复仅支持笔记作者回复，因此若S账号（员工号）没有授权绑定，私信回复时会报错；若为S的意向评论，请在user_id和from_user_id中填入S的UID
3、企业号的矩阵号授权关系逻辑与KOS一致


卡片消息-服务卡
服务卡已升级为留资卡，老服务卡已下线，请勿请求发送接口，发送将失败！！


卡片消息-名片


卡片消息-留资卡


卡片消息-交易卡


撤回消息
2分钟内可撤回



响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	是		
				
request_id	string	是	请求参数中的唯一id	
message_id	string	是	消息id，用于消息撤回	
响应示例


三方客服-接收消息接口
注册小红书商业化开放平台上报消息推送域名，三方客服实现指定path接口，小红书会将私信消息通过POST请求推送到该地址
消息类型包括：1、C端用户发送给客户的消息；2、系统自动触发的商家发送给C端用户的消息

地址示例
域名：https://adapi.xiaohongshu.com[是不是可以考虑增加用户评论笔记后将评论内容下发给三方客服]
接口：/api/open/im/send
请求方式：POST
数据格式：JSON

推送消息参数
参数		类型	是否必填	描述	示例
message_id		string	是	消息id，用于消息撤回	"1712039938271-23496543-427c-bee9"
message_type		string	是	消息类型枚举[需要补充 SMILES 类型]
TEXT：文本
SMILES：自定义表情
IMAGE：图片
VIDEO：视频
CARD：卡片
HINT：hint提示消息
REVOKE：撤回消息	
message_source		int	是	消息来源枚举
1：C2B - 用户消息
2：C2B - 系统消息
3：B2C - 系统消息/小红书端上回复的消息（非三方客服发送）	
from_user_id		string	是	c端用户userId/b端用户userId（b2c消息）	"660a6b96a080b200014925de"
to_user_id		string	是	客户userId（聚光平台授权绑定的账号，包括企业号和KOS员工号的userId）	"660a6b96a080b200014925de"
timestamp		long	是	时间戳	*************
content		string	是	消息内容构造	{
    "text": "",  //文本内容
}
					
user_info		list	是	c端用户信息	
	user_id	string	是	c端用户id	
	nickname	string	是	用户昵称	"测试账号"
	header_image	string	是	用户头像	"https://sns-avatar-qc.xhscdn.com/avatar/be4e54a984a21be925a215a08fb36c8b.jpg?imageView2/2/w/80/format/jpg"

C端用户消息-示例
文本消息

普通表情（text）
小红书Emoji及对应中文映射

自定义表情消息[@星刻(白志宏)自定义表情会有个URL，普通表情是个text][自定义表情增加一个message_type][不会有自定义表情+文字一起发过去的情况][表情这里区分普通表情和自定义表情，参考 CARD 消息规范，似乎缺少一个 content_type 呢][普通表情直接发文本消息，不区分是否表情](URL)


图片消息

备：如果图片链接无法正常渲染，可尝试用如下代码处理链接
const justifyImageUrl = (url) => {
  try {
    return `${url.split('?')[0]}?imageView2/2/w/540`
  } catch (error) {
    return url
  }
}2

视频消息


卡片消息-笔记


卡片消息-落地页


卡片消息-服务卡
服务卡已升级为留资卡，请勿调用任何老服务卡相关接口！


卡片消息-名片


卡片消息-留资卡


卡片消息-交易卡



撤回消息



系统消息/品牌在小红书端上回复的消息
用户触发的系统自动推送消息，如欢迎语、快捷组件
HINT消息

欢迎语消息


卡片消息-笔记

卡片消息-落地页


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
响应示例



三方客服-查物料信息接口[落地页，服务卡，笔记，我们这边也希望有一个 findById 的接口]
落地页
查询授权客户在聚光创建的落地页列表，默认时间倒序，请注意KOS暂不支持获取落地页
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/page/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body[body 中是否能增加一个 page_id 查询参数？][物料 page 查询是否可以提供一个 last_id, 作为游标起点向后查询，这样，我们就不用每次都从第一页开始查了，已经查过的就跳过，where id > list_id]
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId）	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	落地页数据	
				
total	long	是	落地页总数	
title	string	否	落地页标题	"test"
create_time	long	是	落地页创建时间	*************
page_id	string	是	落地页id	"660a6b96a000014925de80b2"
page_desc	string	否	落地页描述	"婚纱摄影报名"
page_url	string	是	落地页链接	""
cover	string	是	封面	""
响应示例


服务卡
服务卡升级至留资卡，请勿请求服务卡物料查询接口，留资卡物料接口可至名片处获取
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/service/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body[body 中是否能增加 card_id 查询参数？]
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId）	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	落地页列表	
				
total	long	是	落地页总数	
card_id	string	是	服务卡id	"660a6b96a000014925de80b2"
link	string	是	h5页面url	
cover	string	是	封面	
title	string	否	标题	
sub_title	string	否	子标题	
				

响应示例


名片/留资卡
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/material/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	
Body
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId），kos使用时，user_id传kos_user_id即可	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
type	int	是	物料类型	4-名片 5-留资卡
请求示例

响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	响应结构体	
total	long	是	总数	
id	string	是	物料id	"660a6b96a000014925de80b2"
name	string	是	名称	
title	string	是	标题	
image	string	是	封面图	
ext	object	否	扩展字段	
				
social_card_ext	object	否	名片扩展字段	
card_type	int	否	名片类型	1-微信、2-钉钉、3-电话、4-企微 5-留资卡
响应示例


笔记
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/note/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId），，kos使用时，user_id传kos_user_id即可	"660a6b96a080b200014925de"
note_id	string	否	单笔记id精确搜索	
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	笔记数据	
				
total	long	是	笔记总数	
note_id	string	是	笔记id	""
publish_time	long	是	笔记发布时间	*************
link	long	是	笔记链接 	""
title	string	否	标题	"婚纱摄影报名"
cover	string	是	笔记封面	""
user_info	object	是	发布用户信息	""
				
nickname	string	是	昵称	""
header_image	string	是	头图	""
响应示例



意向评论
请注意：接口调用有频控，100次/s，且为统一频控，超出频控会报错，请勿频繁调用
产品介绍：
意向评论非小红书笔记下的所有评论，仅抓取算法识别的高意向评论内容；
意向评论本身存在使用门槛，非所有账号均可使用，具体门槛和产品内容可见下方文档
小红书「私信通」产品使用手册（对客）
特别说明：
若KOS账号已在私信通进行了授权给企业号操作，在三方接口处以企业号身份拉取该接口时，会同步获取到对应S的意向评论，而以KOS的身份拉取时则返回列表会为空
企业号的矩阵号授权关系逻辑与KOS一致

请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/comment/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body[body 中是否能增加 note_id、comment_id 两个查询参数？]
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId）
KOS账号不支持获取意向评论物料，统一以企业号UID进行获取
kos使用时，user_id传kos_user_id即可	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
begin_time	string	否	开始时间	"2024-03-03"
end_time	string	否	结束时间	"2024-03-30"
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	评论数据	
				
total	long	是	评论总数	
				
note_id	string	是	笔记；id	""
cover	string	是	笔记封面	""
note_title	string	否	笔记标题	""
note_author_user_id	string	是	笔记作者uid	
comment_content	string	否	评论内容	""
comment_time	long	是	评论时间	*************
comment_user_name	string	是	评论用户名	""
comment_user_id	string	是	评论用户id	""
comment_id	string	是	评论id（同作者下唯一，用于私信回复）	
uniq_id 	string	是	评论id（全局唯一）	
reply_state	int	是	回复状态
1已回复
0未回复	1
reply_third_account_id	string	否	回复客服	""

响应示例

交易卡
产品功能介绍可见：私信获客工具_交易卡_产品说明及操作手册（定向邀测中）
查询授权客户在聚光创建的交易卡列表，默认时间倒序
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/material/list
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	
Body
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（授权客户userId），kos使用时，user_id传kos_user_id即可	"660a6b96a080b200014925de"
page_num	int	是	页码，第一页从1开始	1
page_size	int	是	分页长度，不超过20	10
type	int	是	物料类型	4-名片 5-留资卡 7-交易卡
请求示例


响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	否	交易卡数据	
				
total	long	是	交易卡总数	
id	string	否	交易卡id	"10000100032"
image	string	是	封面	"https://ads-img-al.xhscdn.com/aurora/104101l831c5bipsa2u05p1igpc00000004046pou50bok"
title	string	是	主标题	"主标题一二三四五六七"
ext	object	否	扩展字段	
				
trade_card_ext	object	否	交易卡扩展信息	
				
sub_title	string	是	副标题	
link_platform	string	是	跳转链接类型	
响应示例



三方客服-留资数据回传
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/third/back_lead_result
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body
参数	类型	是否必填	描述	示例
user_id	string	是	开放平台鉴权id（聚光授权客户userId）,kos回传线索时：user_id = kos_user_id	"660a6b96a080b200014925de"
request_id	string	是	唯一id，例如uuid，接口成功后返回	"1712039938271-23496543-427c-bee9"
brand_user_id	string	是	客户userId（聚光平台授权绑定的userId）	"660a6b96a080b200014925de"
c_user_id	string	是	c端用户userId	"660a6b96a080b200014925de"
third_back_source	string	是	三方标识	"MEIQIA"
timestamp	long	是	时间戳，毫秒	*************
content	string	是	消息内容构造，加密后的消息内容	"Q9U/Mpfv/w3w9xinsKIbrboB9j9Moe5oTVbbibzxzPqFoXNH1OtlKsLE5EyxXyM0x7/KeKpK1lEc+OSs747vVhleteP38gOhFN8R9GYG7/+Tw5fOqURH1wS2MIG33hGmcrdXaRio0TkXWkNd0fsIiLCve0GC78qPmQ7Px4xQbkw="
				

content示例数据：
数据名称	举例
三方标识	美洽
C端用户信息	user_info {user_id；nickname}
B端用户信息	user_info {user_id；nickname}
客资更新时间	time
标注客服姓名	customer_service_name

省份地区	"area":"上海"
电话号码	"phone_num":"13900000000"
微信号	"wechat":"wx12345"
备注信息	"remark":"备注",
其他list	{ 邮箱：
留学国家：（一些自定义的字段）
性别：
年龄：
微博：
...}


请求示例

响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	是		
				
request_id	string	是	请求参数中的唯一id	
响应示例


三方客服-留资和广告归因数据推送
注册小红书商业化开放平台上报消息推送域名，三方客服实现指定path接口，小红书会将留资数据、进线及开口广告归因数据通过POST请求推送到该地址
推送时机：当用户有进线/开口/留资对应转化动作的时候，每个转化动作被归因后都会进行推送，归因需要时间因此推送会比进线/开口/留资有分钟级的延迟
备注：
1.连续时间内同一个用户的进线、开口、留资推送的广告数据会一致（如果用户1上个月进线/开口/留资过今天又进线了，这两次广告数据可能不一样）
2.三方客服侧需注意，收到小红书推送给你们的留资等数据后，不要再通过“留资数据回传”接口给小红书传回来，可能造成推送循环
3.必须按照约定的响应参数返回，小红书侧需要按照相关参数判定是否推送成功
地址示例
域名：https://adapi.xiaohongshu.com
接口：/api/open/im/push_lead
请求方式：POST
数据格式：JSON

推送消息参数
参数		类型	是否必填	描述	示例
user_id		string	是	c端 userId	"660a6b96a080b200014925de"
brand_user_id		string	是	广告主的 userId	"660a6b96a080b200014925de"
kos_user_id		string	是	kos的uid（表示线索是由kos获取到的)	"660a6b96a080b200014925de"
conv_time
		string	否	归因时间	"2024-06-24T14:33:00.18Z"
advertiser_name
		string	否	广告主名称	
advertiser_id		string	否	广告主 id	
campaign_name		string	否	计划名称	
campaign_id
		string	否	计划 id	
creativity_name
		string	否	创意名称	
creativity_id
		string	否	创意 id	
leads_tag
		string	否	线索标签	
area		string	否	省份地区	
phone_num		string	否	手机号（加密后的）	
wechat		string	否	微信号（加密后的）	
remark		string	否	备注	
push_type		int	是	推送类型枚举：
1.进线归因后推送（无线索数据）
2.开口归因后推送（无线索数据）
3.留资后推送（无广告数据）
4.留资归因后推送	
wechat_copy		int	否	是否个人微信复制	0-否 1-是
link_id
		string	 否	企微链接 id	
link_name		string	否	 企微链接名称	
customer_channel		string	否	企微链接customer_channel（用于企微加微用户与小红书用户匹配）	
msg_app_open		int		交易卡是否唤端成功	0-否 1-是
wechat_type		int	 是	微信号类型 	1-文本，2-图片链接
响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
响应示例


三方客服- 组件额度查询
该能力已废弃，请勿关心
请求地址
接口：https://adapi.xiaohongshu.com/api/open/im/third/limit
请求方式：POST
数据格式：JSON
请求参数
Header
参数	类型	是否必填	描述	示例
Access-Token	string	是	开放平台token	

Body
参数	类型	是否必填	描述	示例
user_id	string	是	 企业号（聚光授权客户userId）员工账号（c 端 userId）	"5c8650cb0000000001004367"
account_type	Interge	是	 账号类型 1-企业号 2-员工账号( 目前只支持查询企业号额度信息)	
响应参数
参数	类型	是否必填	描述	示例
code	int	是	错误码，0为成功	0
msg	string	否	错误信息，接口失败时返回失败原因	"参数不合法，用户id必填"
success	bool	是	标识接口成功/失败	true
data	object	是	user_id不在组件付费灰测名单内中时该对象会返回 null
	
				
group_budget	object	是	账号总额度信息	
				
used_budget	long	 是	使用额度	
total_budget	long	是	总额度	
				
user_budget	 object		单账号额度信息	
				
used_budget	long	 是	使用额度	
total_budget	long	是	总额度	
响应示例




加解密算法
加解密秘钥系统对接时小红书研发给出，如：secretKey = "jQZ+ECLKuf6UZ8hePdMKhg=="








