import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { omitBy } from 'lodash'

type VersionSearchInput = {
  search?: string
  type?: 'DESKTOP' | 'BROWSER_PLUGIN' | 'RPA' | 'CRAWLER'
  platform?: 'WIN' | 'MAC'
}

export function TableSearch({
  values,
  onSearch,
}: {
  values?: VersionSearchInput
  onSearch: (values: VersionSearchInput) => void
}) {
  const form = useForm<VersionSearchInput>({
    defaultValues: {
      search: values?.search?.trim() ?? '',
      type: values?.type,
      platform: values?.platform,
    },
  })

  function onSubmit(values: VersionSearchInput) {
    const params: VersionSearchInput = {
      search: values.search?.trim(),
      type: values.type,
      platform: values.platform,
    }
    const paramsOmitEmpty = omitBy(params, (value) => !value)
    onSearch(paramsOmitEmpty)
  }

  const resetFilters = () => {
    form.reset({
      search: '',
      type: undefined,
      platform: undefined,
    })
    onSearch({})
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="search"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>版本号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索版本号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>类型</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部类型</SelectItem>
                  <SelectItem value="DESKTOP">桌面端</SelectItem>
                  <SelectItem value="BROWSER_PLUGIN">浏览器插件</SelectItem>
                  <SelectItem value="RPA">RPA</SelectItem>
                  <SelectItem value="CRAWLER">爬虫脚本</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="platform"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>平台</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择平台" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部平台</SelectItem>
                  <SelectItem value="WIN">Windows</SelectItem>
                  <SelectItem value="MAC">macOS</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
        <Button type="button" variant="outline" onClick={resetFilters}>
          重置
        </Button>
      </form>
    </Form>
  )
}
