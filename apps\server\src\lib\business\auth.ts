import bcrypt from 'bcryptjs'
import type { User } from '@coozf/db'

// 不返回密码的用户类型
export type SafeUser = Omit<User, 'password'>

/**
 * 认证业务逻辑服务
 */
export class AuthService {
  /**
   * 密码加密
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12)
  }

  /**
   * 密码验证
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  /**
   * 生成默认头像URL
   */
  static generateAvatarUrl(userId: string): string {
    // 使用 DiceBear API 生成头像
    const style = 'avataaars' // 可选风格：avataaars, bottts, pixel-art 等
    const encodedSeed = encodeURIComponent(userId)
    return `https://api.dicebear.com/7.x/${style}/svg?seed=${encodedSeed}&backgroundColor=b6e3f4`
  }
}
