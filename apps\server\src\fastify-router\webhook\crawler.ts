import type { Application } from '@coozf/db'
import { WebhookService, sessionService, ApiError, PublishService, logger, MediaAccountServies } from '@/lib'
import type { FastifyInstance } from 'fastify'
import { AuthResultCallbackSchema, CrawlerWebhookSchema, PublishTaskCallbackSchema } from '@coozf/zod'
import { applicationCache } from '@/lib/services/cache'
import { TRPCError } from '@trpc/server'

declare module 'fastify' {
  interface FastifyRequest {
    application: Pick<Application, 'id' | 'name' | 'appId' | 'webhookUrl' | 'webhookSecret'>
    recordId: string
    extra?: unknown
  }
}

export const crawlerWebhookRoutes = async (app: FastifyInstance) => {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const sessionToken = await sessionService.getSessionToken(authHeader ?? '')
    request.recordId = sessionToken.recordId
    request.extra = sessionToken.extra
    // 获取应用信息
    const existingApp = await applicationCache.getOrSetDefault(sessionToken.applicationId)
    if (!existingApp) {
      throw new ApiError(40100, 'Unauthorized') /*  */
    }
    request.application = existingApp
  })
  app.post('/', async (request, reply) => {
    const { application, body } = request
    const res = CrawlerWebhookSchema.parse(body)
    logger.info('收到爬虫Webhook请求', {
      type: 'webhook',
      source: 'crawler',
      applicationId: application.id,
      recordId: request.recordId,
      event: res.event,
    })

    let replyBody = request.body

    if (request.recordId && res.event === 'publishResult') {
      const publishResult = PublishTaskCallbackSchema.parse(body)
      logger.info('收到发布结果回调', {
        type: 'webhook',
        source: 'crawler',
        event: 'publishResult',
        applicationId: application.id,
        recordId: request.recordId,
        publishResult,
      })

      if (publishResult.stages === 'push') {
        const result = await PublishService.handleTaskCallback(
          publishResult.taskId,
          request.recordId,
          publishResult.stageStatus === 'success',
          publishResult.errorMessage,
        )
        logger.info('处理发布结果回调完成', {
          type: 'webhook',
          source: 'crawler',
          event: 'publishResult_processed',
          applicationId: application.id,
          recordId: request.recordId,
          result,
          publishResult,
        })
      }
    }
    if (request.recordId && res.event === 'authResult') {
      const authResult = AuthResultCallbackSchema.parse(body)
      logger.info('收到授权结果回调', {
        type: 'webhook',
        source: 'crawler',
        event: 'authResult',
        applicationId: application.id,
        recordId: request.recordId,
        authResult,
      })
      sessionService.revokeSessionToken(request.headers.authorization ?? '') // 确认 sessionToken 有效性
      const { event, status, verify } = authResult
      const { callBackData, accountId } = request.extra as { callBackData?: unknown; accountId?: string }
      const baseReplyBody = {
        event,
        status,
        operationId: request.recordId,
        callBackData: callBackData,
      }
      if (status === 'SUCCESS') {
        try {
          const res = accountId
            ? await MediaAccountServies.updateMediaAccount({
                id: accountId,
                applicationId: application.id,
                platformCode: authResult.platformCode,
                platformUserId: authResult.userId,
                platformUserName: authResult.userName,
                platformAvatar: authResult.avatar,
                platformCookie: authResult.cookie,
              })
            : await MediaAccountServies.createMediaAccount(authResult, application)
          replyBody = {
            ...baseReplyBody,
            cookie: authResult.cookie,
            localStorage: authResult.localStorage,
            verify,
            data: res,
          }
        } catch (error) {
          logger.error('创建媒体账号失败', {
            type: 'webhook',
            source: 'crawler',
            event: 'authResult',
            applicationId: application.id,
            recordId: request.recordId,
            authResult,
            error,
          })
          replyBody = {
            ...baseReplyBody,
            status: 'FAILED',
            errorMessage: error instanceof TRPCError ? error.message : '创建媒体账号失败',
          }
        }
      } else {
        replyBody = {
          ...baseReplyBody,
          ...authResult,
        }
      }
    }

    const { webhookUrl, webhookSecret } = application
    if (!webhookUrl) {
      return reply.status(400).send({
        success: false,
        message: 'Webhook URL not found',
      })
    } else {
      await WebhookService.sendNotification(webhookUrl, webhookSecret, 'web', replyBody)
    }
    return reply.send({
      statusCode: 0,
      message: 'Webhook received',
    })
  })
}
