import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useQuery, useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/users/table-search'
import type { UserListInput } from '@coozf/zod'
import { UserSession } from '@/components/users/user-session'
import { getTableColumns } from '@/components/users/columns'

type UserItem = RouterOutput['user']['list']['data'][number]

type Search = {
  phone?: string
}

export const Route = createFileRoute('/_authenticated/users')({
  component: UsersPage,
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      phone: search?.phone as string,
    }
  },
})

function UsersPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  })

  const { phone } = useSearch({ strict: false })

  const [filter, setFilter] = useState<UserListInput>({ phone })

  const [selectedUser, setSelectedUser] = useState<UserItem | null>(null)
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false)

  // 获取用户列表
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.user.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...filter,
    }),
  )

  // 强制用户下线
  const forceOfflineMutation = useMutation(
    trpc.user.forceOffline.mutationOptions({
      onSuccess: () => {
        toast.success('用户已被强制下线')
        refetch()
        setIsDeviceModalOpen(false)
      },
      onError: (error: any) => {
        toast.error(error.message || '强制下线失败')
      },
    }),
  )

  const users = usersData?.data || []

  const handleViewDevices = (user: UserItem) => {
    setSelectedUser(user)
    setIsDeviceModalOpen(true)
  }

  const handleForceOffline = (userId: string) => {
    if (window.confirm('确定要强制该用户下线吗？')) {
      forceOfflineMutation.mutate({ id: userId })
    }
  }

  const columns = useMemo(
    () => getTableColumns(handleViewDevices, handleForceOffline),
    [handleViewDevices, handleForceOffline],
  )

  return (
    <div className="flex flex-1 flex-col gap-2 overflow-hidden">
      <TableSearch
        values={filter}
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 })
          setFilter(value)
        }}
      />

      <DataTable
        columns={columns}
        data={users}
        rowCount={usersData?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />
      <UserSession open={isDeviceModalOpen} onOpenChange={setIsDeviceModalOpen} selectedUser={selectedUser} />
    </div>
  )
}
