import { z } from 'zod'
import { router } from '@/trpc'
import { protectedProcedure } from '@/procedure'
import { OrderListSchema, PaginationSchema } from '@coozf/zod'
import { OrderService } from '@/lib'
import { TRPCError } from '@trpc/server'
import type { Prisma } from '@coozf/db'

export const orderRouter = router({
  // 注意：adminRecharge 已废弃，请使用 admin/quota 路由中的配额管理功能

  // 获取订单列表
  list: protectedProcedure.input(OrderListSchema.and(PaginationSchema)).query(async ({ input, ctx }) => {
    return OrderService.getOrderList({
      ...input,
      userId: ctx.user.id, // 普通用户只能查看自己的订单
    })
  }),

  // 获取订单详情
  getOrderById: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .query(async ({ ctx, input }) => {
      const order = OrderService.getOrderById(input.orderId, ctx.user.id)
      return order
    }),

  // 申请发票
  requestInvoice: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const order = await OrderService.getOrderById(input.orderId, ctx.user.id)

      if (!order) {
        throw new TRPCError({
          message: '订单不存在',
          code: 'NOT_FOUND',
        })
      }
      // 只有购买订单才能申请发票
      if (order.type !== 'PURCHASE') {
        throw new Error('只有购买订单才能申请发票')
      }

      await OrderService.requestInvoice(input.orderId)
      return { success: true, message: '发票申请成功，请联系客服处理' }
    }),

  // 取消订单
  cancelOrder: protectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const order = await OrderService.getOrderById(input.orderId, ctx.user.id)

      if (!order) {
        throw new TRPCError({
          message: '订单不存在',
          code: 'NOT_FOUND',
        })
      }
      await OrderService.cancelOrder(input.orderId)
      return { success: true, message: '订单已取消' }
    }),
})
