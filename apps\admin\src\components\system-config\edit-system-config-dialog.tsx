import { useState, useEffect } from 'react'
import { Button } from '@coozf/ui/components/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import { Textarea } from '@coozf/ui/components/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Switch } from '@coozf/ui/components/switch'
import { X } from 'lucide-react'
import { type RouterOutput } from '@/lib/trpc'
import { categoryMap, type Category } from '@coozf/zod'

type SystemConfigItem = RouterOutput['systemConfig']['list']['items'][number]

interface EditSystemConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  config: SystemConfigItem | null
  onSubmit: (data: any) => void
  isPending: boolean
}

interface ConfigValue {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  value: any
}

export function EditSystemConfigDialog({
  open,
  onOpenChange,
  config,
  onSubmit,
  isPending,
}: EditSystemConfigDialogProps) {
  const [formData, setFormData] = useState({
    key: '',
    category: 'GENERAL' as Category,
    name: '',
    description: '',
    isEnabled: true,
  })

  const [configValue, setConfigValue] = useState<ConfigValue>({
    type: 'string',
    value: '',
  })

  const [arrayItems, setArrayItems] = useState<string[]>([''])

  // 当config变化时，更新表单数据
  useEffect(() => {
    if (config) {
      setFormData({
        key: config.key,
        category: config.category,
        name: config.name,
        description: config.description || '',
        isEnabled: config.isEnabled,
      })

      // 根据配置值类型设置配置值
      const value = config.value
      if (Array.isArray(value)) {
        setConfigValue({ type: 'array', value })
        setArrayItems(value.map(String))
      } else if (typeof value === 'boolean') {
        setConfigValue({ type: 'boolean', value: value ? 'true' : 'false' })
      } else if (typeof value === 'number') {
        setConfigValue({ type: 'number', value: String(value) })
      } else if (typeof value === 'object' && value !== null) {
        setConfigValue({ type: 'object', value: JSON.stringify(value, null, 2) })
      } else {
        setConfigValue({ type: 'string', value: String(value) })
      }
    }
  }, [config])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!config) return

    let finalValue: any
    switch (configValue.type) {
      case 'string':
        finalValue = configValue.value
        break
      case 'number':
        finalValue = Number(configValue.value)
        break
      case 'boolean':
        finalValue = configValue.value === 'true'
        break
      case 'array':
        finalValue = arrayItems.filter((item) => item.trim() !== '')
        break
      case 'object':
        try {
          finalValue = JSON.parse(configValue.value)
        } catch {
          finalValue = {}
        }
        break
      default:
        finalValue = configValue.value
    }

    onSubmit({
      id: config.id,
      ...formData,
      value: finalValue,
    })
  }

  const addArrayItem = () => {
    setArrayItems([...arrayItems, ''])
  }

  const removeArrayItem = (index: number) => {
    setArrayItems(arrayItems.filter((_, i) => i !== index))
  }

  const updateArrayItem = (index: number, value: string) => {
    const newItems = [...arrayItems]
    newItems[index] = value
    setArrayItems(newItems)
  }

  if (!config) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑系统配置</DialogTitle>
          <DialogDescription>修改系统配置项</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 基本信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="key">配置键 *</Label>
              <Input
                id="key"
                value={formData.key}
                onChange={(e) => setFormData({ ...formData, key: e.target.value })}
                placeholder="例：crawler_proxy_whitelist"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">分类 *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value as 'CRAWLER_PROXY' | 'GENERAL' })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(categoryMap).map((cat) => (
                    <SelectItem key={cat.key} value={cat.key}>
                      {cat.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">配置名称 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="例：爬虫代理白名单"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="配置项的详细描述"
              rows={2}
            />
          </div>

          {/* 配置值 */}
          <div className="space-y-2">
            <Label>配置值类型</Label>
            <Select
              value={configValue.type}
              onValueChange={(value) => {
                const newType = value as ConfigValue['type']
                if (newType === 'array' && !Array.isArray(arrayItems)) {
                  setArrayItems([''])
                }
                setConfigValue({ type: newType, value: newType === 'array' ? arrayItems : '' })
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="string">字符串</SelectItem>
                <SelectItem value="number">数字</SelectItem>
                <SelectItem value="boolean">布尔值</SelectItem>
                <SelectItem value="array">数组</SelectItem>
                <SelectItem value="object">对象</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 根据类型显示不同的输入组件 */}
          <div className="space-y-2">
            <Label>配置值 *</Label>
            {configValue.type === 'string' && (
              <Input
                value={configValue.value}
                onChange={(e) => setConfigValue({ ...configValue, value: e.target.value })}
                placeholder="输入字符串值"
                required
              />
            )}
            {configValue.type === 'number' && (
              <Input
                type="number"
                value={configValue.value}
                onChange={(e) => setConfigValue({ ...configValue, value: e.target.value })}
                placeholder="输入数字值"
                required
              />
            )}
            {configValue.type === 'boolean' && (
              <Select value={configValue.value} onValueChange={(value) => setConfigValue({ ...configValue, value })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择布尔值" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">true</SelectItem>
                  <SelectItem value="false">false</SelectItem>
                </SelectContent>
              </Select>
            )}
            {configValue.type === 'array' && (
              <div className="space-y-2">
                {arrayItems.map((item, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={item}
                      onChange={(e) => updateArrayItem(index, e.target.value)}
                      placeholder={`项目 ${index + 1}`}
                    />
                    {arrayItems.length > 1 && (
                      <Button type="button" variant="outline" size="sm" onClick={() => removeArrayItem(index)}>
                        <X className="size-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={addArrayItem}>
                  添加项目
                </Button>
              </div>
            )}
            {configValue.type === 'object' && (
              <Textarea
                value={configValue.value}
                onChange={(e) => setConfigValue({ ...configValue, value: e.target.value })}
                placeholder='输入JSON对象，例：{"key": "value"}'
                rows={4}
                required
              />
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="enabled"
              checked={formData.isEnabled}
              onCheckedChange={(checked) => setFormData({ ...formData, isEnabled: checked })}
            />
            <Label htmlFor="enabled">启用配置</Label>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? '更新中...' : '更新'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
