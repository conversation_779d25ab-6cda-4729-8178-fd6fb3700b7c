/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedVersionsRouteImport } from './routes/_authenticated/versions'
import { Route as AuthenticatedUsersRouteImport } from './routes/_authenticated/users'
import { Route as AuthenticatedSystemConfigRouteImport } from './routes/_authenticated/system-config'
import { Route as AuthenticatedOrdersRouteImport } from './routes/_authenticated/orders'
import { Route as AuthenticatedOnlineDevicesRouteImport } from './routes/_authenticated/online-devices'
import { Route as AuthenticatedAuthAccountsRouteImport } from './routes/_authenticated/auth-accounts'
import { Route as AuthenticatedApplicationsRouteImport } from './routes/_authenticated/applications'
import { Route as AuthenticatedAdminUserRouteImport } from './routes/_authenticated/admin-user'
import { Route as AuthenticatedAccountsRouteImport } from './routes/_authenticated/accounts'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedVersionsRoute = AuthenticatedVersionsRouteImport.update({
  id: '/versions',
  path: '/versions',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedUsersRoute = AuthenticatedUsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedSystemConfigRoute =
  AuthenticatedSystemConfigRouteImport.update({
    id: '/system-config',
    path: '/system-config',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedOrdersRoute = AuthenticatedOrdersRouteImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedOnlineDevicesRoute =
  AuthenticatedOnlineDevicesRouteImport.update({
    id: '/online-devices',
    path: '/online-devices',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedAuthAccountsRoute =
  AuthenticatedAuthAccountsRouteImport.update({
    id: '/auth-accounts',
    path: '/auth-accounts',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedApplicationsRoute =
  AuthenticatedApplicationsRouteImport.update({
    id: '/applications',
    path: '/applications',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedAdminUserRoute = AuthenticatedAdminUserRouteImport.update({
  id: '/admin-user',
  path: '/admin-user',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAccountsRoute = AuthenticatedAccountsRouteImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/accounts': typeof AuthenticatedAccountsRoute
  '/admin-user': typeof AuthenticatedAdminUserRoute
  '/applications': typeof AuthenticatedApplicationsRoute
  '/auth-accounts': typeof AuthenticatedAuthAccountsRoute
  '/online-devices': typeof AuthenticatedOnlineDevicesRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/system-config': typeof AuthenticatedSystemConfigRoute
  '/users': typeof AuthenticatedUsersRoute
  '/versions': typeof AuthenticatedVersionsRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/accounts': typeof AuthenticatedAccountsRoute
  '/admin-user': typeof AuthenticatedAdminUserRoute
  '/applications': typeof AuthenticatedApplicationsRoute
  '/auth-accounts': typeof AuthenticatedAuthAccountsRoute
  '/online-devices': typeof AuthenticatedOnlineDevicesRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/system-config': typeof AuthenticatedSystemConfigRoute
  '/users': typeof AuthenticatedUsersRoute
  '/versions': typeof AuthenticatedVersionsRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/accounts': typeof AuthenticatedAccountsRoute
  '/_authenticated/admin-user': typeof AuthenticatedAdminUserRoute
  '/_authenticated/applications': typeof AuthenticatedApplicationsRoute
  '/_authenticated/auth-accounts': typeof AuthenticatedAuthAccountsRoute
  '/_authenticated/online-devices': typeof AuthenticatedOnlineDevicesRoute
  '/_authenticated/orders': typeof AuthenticatedOrdersRoute
  '/_authenticated/system-config': typeof AuthenticatedSystemConfigRoute
  '/_authenticated/users': typeof AuthenticatedUsersRoute
  '/_authenticated/versions': typeof AuthenticatedVersionsRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/accounts'
    | '/admin-user'
    | '/applications'
    | '/auth-accounts'
    | '/online-devices'
    | '/orders'
    | '/system-config'
    | '/users'
    | '/versions'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/accounts'
    | '/admin-user'
    | '/applications'
    | '/auth-accounts'
    | '/online-devices'
    | '/orders'
    | '/system-config'
    | '/users'
    | '/versions'
    | '/'
  id:
    | '__root__'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/accounts'
    | '/_authenticated/admin-user'
    | '/_authenticated/applications'
    | '/_authenticated/auth-accounts'
    | '/_authenticated/online-devices'
    | '/_authenticated/orders'
    | '/_authenticated/system-config'
    | '/_authenticated/users'
    | '/_authenticated/versions'
    | '/_authenticated/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/versions': {
      id: '/_authenticated/versions'
      path: '/versions'
      fullPath: '/versions'
      preLoaderRoute: typeof AuthenticatedVersionsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/users': {
      id: '/_authenticated/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/system-config': {
      id: '/_authenticated/system-config'
      path: '/system-config'
      fullPath: '/system-config'
      preLoaderRoute: typeof AuthenticatedSystemConfigRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/orders': {
      id: '/_authenticated/orders'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof AuthenticatedOrdersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/online-devices': {
      id: '/_authenticated/online-devices'
      path: '/online-devices'
      fullPath: '/online-devices'
      preLoaderRoute: typeof AuthenticatedOnlineDevicesRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/auth-accounts': {
      id: '/_authenticated/auth-accounts'
      path: '/auth-accounts'
      fullPath: '/auth-accounts'
      preLoaderRoute: typeof AuthenticatedAuthAccountsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/applications': {
      id: '/_authenticated/applications'
      path: '/applications'
      fullPath: '/applications'
      preLoaderRoute: typeof AuthenticatedApplicationsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/admin-user': {
      id: '/_authenticated/admin-user'
      path: '/admin-user'
      fullPath: '/admin-user'
      preLoaderRoute: typeof AuthenticatedAdminUserRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/accounts': {
      id: '/_authenticated/accounts'
      path: '/accounts'
      fullPath: '/accounts'
      preLoaderRoute: typeof AuthenticatedAccountsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedAccountsRoute: typeof AuthenticatedAccountsRoute
  AuthenticatedAdminUserRoute: typeof AuthenticatedAdminUserRoute
  AuthenticatedApplicationsRoute: typeof AuthenticatedApplicationsRoute
  AuthenticatedAuthAccountsRoute: typeof AuthenticatedAuthAccountsRoute
  AuthenticatedOnlineDevicesRoute: typeof AuthenticatedOnlineDevicesRoute
  AuthenticatedOrdersRoute: typeof AuthenticatedOrdersRoute
  AuthenticatedSystemConfigRoute: typeof AuthenticatedSystemConfigRoute
  AuthenticatedUsersRoute: typeof AuthenticatedUsersRoute
  AuthenticatedVersionsRoute: typeof AuthenticatedVersionsRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedAccountsRoute: AuthenticatedAccountsRoute,
  AuthenticatedAdminUserRoute: AuthenticatedAdminUserRoute,
  AuthenticatedApplicationsRoute: AuthenticatedApplicationsRoute,
  AuthenticatedAuthAccountsRoute: AuthenticatedAuthAccountsRoute,
  AuthenticatedOnlineDevicesRoute: AuthenticatedOnlineDevicesRoute,
  AuthenticatedOrdersRoute: AuthenticatedOrdersRoute,
  AuthenticatedSystemConfigRoute: AuthenticatedSystemConfigRoute,
  AuthenticatedUsersRoute: AuthenticatedUsersRoute,
  AuthenticatedVersionsRoute: AuthenticatedVersionsRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
