import type { FastifyInstance } from 'fastify'
import { xiaohongshuWebhookRoutes } from './xiaohongshu'
import { crawlerWebhookRoutes } from './crawler'
import { gitlabWebhookRoutes } from './gitlab'
import { douyinWebhookRoutes} from './douyin'

const routes = async (app: FastifyInstance) => {
  app.register(xiaohongshuWebhookRoutes, { prefix: '/xiaohongshu' })
  app.register(douyinWebhookRoutes, { prefix: '/douyin' })
  app.register(crawlerWebhookRoutes, { prefix: '/web' })
  app.register(gitlabWebhookRoutes, { prefix: '/gitlab' })
}

export default routes
