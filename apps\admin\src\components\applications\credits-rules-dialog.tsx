import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@coozf/ui/components/dialog'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import type { RouterOutput } from '@/lib/trpc'
import { LoadingButton } from '@coozf/ui/components/loading'
import { Label } from '@coozf/ui/components/label'
import { Separator } from '@coozf/ui/components/separator'

type ApplicationItem = RouterOutput['application']['list']['data'][number]

interface CreditsRulesDialogProps {
  application: ApplicationItem
  creditsConfig?: {
    NORMAL?: number
    VERTICAL?: number
  }
  onUpdateCreditsConfig: (config: { NORMAL?: number; VERTICAL?: number }) => void
  isLoading?: boolean
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function CreditsRulesDialog({
  application,
  creditsConfig,
  onUpdateCreditsConfig,
  isLoading = false,
  open,
  onOpenChange,
}: CreditsRulesDialogProps) {
  const [normalPoints, setNormalPoints] = useState<string>(creditsConfig?.NORMAL?.toString() || '')
  const [verticalPoints, setVerticalPoints] = useState<string>(creditsConfig?.VERTICAL?.toString() || '')

  const handleUpdateConfig = () => {
    const config: { NORMAL?: number; VERTICAL?: number } = {}

    if (normalPoints) {
      const normal = Number(normalPoints)
      if (!isNaN(normal) && normal > 0) {
        config.NORMAL = normal
      }
    }

    if (verticalPoints) {
      const vertical = Number(verticalPoints)
      if (!isNaN(vertical) && vertical > 0) {
        config.VERTICAL = vertical
      }
    }

    onUpdateCreditsConfig(config)
  }

  const isValidConfig = () => {
    if (normalPoints) {
      const normal = Number(normalPoints)
      if (isNaN(normal) || normal <= 0) return false
    }
    if (verticalPoints) {
      const vertical = Number(verticalPoints)
      if (isNaN(vertical) || vertical <= 0) return false
    }
    return normalPoints || verticalPoints
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>点数规则设置</DialogTitle>
          <DialogDescription>配置应用 "{application.name}" 的点数规则和配额设置</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* 当前配置状态 */}
          <div className="grid gap-3">
            <Label className="text-sm font-medium">当前配置状态</Label>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div>
                点数使用: {application.creditsUsed} / {application.creditsQuota}
              </div>
              <div>普通平台点数: {creditsConfig?.NORMAL || '未设置'}</div>
              <div>垂直平台点数: {creditsConfig?.VERTICAL || '未设置'}</div>
            </div>
          </div>

          <Separator />

          {/* 点数配置 */}
          <div className="grid gap-4">
            <Label className="text-sm font-medium">点数配置</Label>

            <div className="grid gap-2">
              <Label htmlFor="normal-points" className="text-sm">
                普通平台点数
              </Label>
              <Input
                id="normal-points"
                type="number"
                placeholder="请输入普通平台点数"
                value={normalPoints}
                onChange={(e) => setNormalPoints(e.target.value)}
                min={1}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="vertical-points" className="text-sm">
                垂直平台点数
              </Label>
              <Input
                id="vertical-points"
                type="number"
                placeholder="请输入垂直平台点数"
                value={verticalPoints}
                onChange={(e) => setVerticalPoints(e.target.value)}
                min={1}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange?.(false)} disabled={isLoading}>
            取消
          </Button>
          <LoadingButton onClick={handleUpdateConfig} isPending={isLoading} disabled={!isValidConfig() || isLoading}>
            更新点数配置
          </LoadingButton>
        </div>
      </DialogContent>
    </Dialog>
  )
}
