import { z } from 'zod'
import { router } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'
import type { Prisma } from '@coozf/db'
import { UserListSchema } from '@coozf/zod'
import { PaginationSchema } from 'node_modules/@coozf/zod/src/common'
import { paginate } from '@/lib/utils'

export const userRouter = router({
  // 获取用户列表
  list: adminProtectedProcedure.input(UserListSchema.merge(PaginationSchema)).query(async ({ ctx, input = {} }) => {
    const { page = 1, pageSize = 10, phone, startDate, endDate, search } = input
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.UserWhereInput = {}

    // 手机号搜索
    if (phone) {
      where.phoneNumber = {
        contains: phone,
      }
    }

    // 通用搜索（姓名或邮箱）
    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
          },
        },
        {
          email: {
            contains: search,
          },
        },
      ]
    }

    // 注册时间范围筛选
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate)
      }
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => ctx.db.user.count({ where }),
        getItems: (skip, take) =>
          ctx.db.user.findMany({
            where,
            skip,
            take: pageSize,
            orderBy: { createdAt: 'desc' },
            include: {
              applications: {
                select: {
                  id: true,
                },
              },
              sessions: {
                where: {
                  expiresAt: {
                    gt: new Date(),
                  },
                },
                select: {
                  id: true,
                  updatedAt: true,
                  ipAddress: true,
                  userAgent: true,
                },
              },
            },
          }),
        transform: (user) => ({
          ...user,
          applicationCount: user.applications.length,
          isOnline: user.sessions.length > 0,
          activeSessionCount: user.sessions.length,
          lastActivity: user.sessions.length > 0 ? user.sessions[0]?.updatedAt : null,
        }),
      },
    )
  }),

  // 获取用户详情
  getById: adminProtectedProcedure.input(z.object({ id: z.string() })).query(async ({ ctx, input }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: input.id },
      include: {
        applications: {
          select: {
            id: true,
            name: true,
            appId: true,
            status: true,
            createdAt: true,
          },
        },
      },
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    return user
  }),

  // 更新用户信息
  update: adminProtectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        phone: z.string().optional(),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      const updatedUser = await ctx.db.user.update({
        where: { id },
        data: updateData,
      })

      return updatedUser
    }),

  // 删除用户
  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.user.delete({
      where: { id: input.id },
    })

    return { success: true, message: '用户删除成功' }
  }),

  // 强制用户下线
  forceOffline: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    // 删除用户的所有活跃会话
    await ctx.db.session.deleteMany({
      where: { userId: input.id },
    })

    return { success: true, message: '用户已被强制下线' }
  }),

  // 删除单个设备会话
  deleteSession: adminProtectedProcedure
    .input(
      z.object({
        userId: z.string(),
        sessionId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 删除指定的会话
      await ctx.db.session.delete({
        where: {
          id: input.sessionId,
          userId: input.userId, // 确保会话属于指定用户
        },
      })

      return { success: true, message: '设备已下线' }
    }),

  // 获取用户在线状态
  getOnlineStatus: adminProtectedProcedure.input(z.object({ id: z.string() })).query(async ({ ctx, input }) => {
    const now = new Date()

    // 查找用户的活跃会话
    const activeSessions = await ctx.db.session.findMany({
      where: {
        userId: input.id,
        expiresAt: {
          gt: now,
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    })

    return {
      isOnline: activeSessions.length > 0,
      activeSessions: activeSessions.map((session) => ({
        id: session.id,
        lastActivity: session.updatedAt,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
      })),
    }
  }),

  // 获取统计数据
  getStats: adminProtectedProcedure.query(async ({ ctx }) => {
    // 总用户数
    const totalUsers = await ctx.db.user.count()

    // 本月新增用户
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const thisMonthUsers = await ctx.db.user.count({
      where: {
        createdAt: {
          gte: startOfMonth,
        },
      },
    })

    // 已验证邮箱的用户
    const verifiedUsers = await ctx.db.user.count({
      where: {
        emailVerified: true,
      },
    })

    // 有应用的用户数
    const usersWithApps = await ctx.db.user.count({
      where: {
        applications: {
          some: {},
        },
      },
    })

    // 在线用户数
    const now = new Date()
    const onlineUsers = await ctx.db.user.count({
      where: {
        sessions: {
          some: {
            expiresAt: {
              gt: now,
            },
          },
        },
      },
    })

    return {
      totalUsers,
      thisMonthUsers,
      verifiedUsers,
      usersWithApps,
      onlineUsers,
    }
  }),
})
