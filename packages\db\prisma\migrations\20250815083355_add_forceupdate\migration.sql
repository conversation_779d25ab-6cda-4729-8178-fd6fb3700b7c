-- -----------------------------------------------------------------------------
-- [手动添加] 步骤 1: 创建一个临时表来备份数据
-- -----------------------------------------------------------------------------
CREATE TABLE `twofactor_backup` AS SELECT * FROM `twofactor`;


-- -----------------------------------------------------------------------------
-- Prisma 生成的原始 SQL (大部分保留)
-- -----------------------------------------------------------------------------

/*
  Warnings:
  - You are about to drop the `twofactor` table. If the table is not empty, all the data it contains will be lost.
*/
-- DropForeignKey
ALTER TABLE `twofactor` DROP FOREIGN KEY `twoFactor_userId_fkey`;

-- AlterTable
ALTER TABLE `client_versions` ADD COLUMN `forceUpdate` BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE `twofactor`;

-- CreateTable
CREATE TABLE `twoFactor` (
    `id` VARCHAR(191) NOT NULL,
    `secret` TEXT NOT NULL,
    `backupCodes` TEXT NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    
    -- 手动检查并添加索引，如果原始表有的话
    INDEX `twoFactor_userId_fkey`(`userId`),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `twoFactor` ADD CONSTRAINT `twoFactor_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `admin_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;


-- -----------------------------------------------------------------------------
-- [手动添加] 步骤 2: 从临时备份表恢复数据到新表
-- -----------------------------------------------------------------------------
INSERT INTO `twoFactor` (`id`, `secret`, `backupCodes`, `userId`)
SELECT `id`, `secret`, `backupCodes`, `userId` FROM `twofactor_backup`;


-- -----------------------------------------------------------------------------
-- [手动添加] 步骤 3: 删除临时表
-- -----------------------------------------------------------------------------
DROP TABLE `twofactor_backup`;