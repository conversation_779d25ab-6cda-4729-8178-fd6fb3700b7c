import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { useQuery } from '@tanstack/react-query'
import SuccessIcon from '@/assets/success.png'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/xhs-juguang-auth')({
  component: XhsJuguangAuth,
})

const JUMP_URL = 'jumpUrl'
const APPID = 'appId'
const TOKEN = 'token'

function XhsJuguangAuth() {
  const url = new URL(window.location.href)
  const searchParams = new URLSearchParams(url.search)
  const jumpUrl = searchParams.get(JUMP_URL) || ''

  const xhsQuery = useQuery(trpc.xiaohongshu.agreeAuth.queryOptions())

  const onImmediatelyAuthorize = () => {
    const targetUrl = `${jumpUrl}?${APPID}=${xhsQuery.data?.appId}&${TOKEN}=${encodeURIComponent(xhsQuery.data?.token || '')}`
    console.log('%c targetUrl:\n', 'color:#FF7A45', targetUrl)
    window.open(targetUrl, '_self')
  }

  return (
    <div className="w-full h-screen flex items-center justify-center bg-[url('@/assets/xhs-jg-auth-bg.png')] bg-cover bg-center bg-no-repeat">
      <Card className="w-[440px] max-h-max bg-white shadow-[0px_6px_18px_6px_rgba(31,35,41,0.03)] border border-[#DEE0E3] rounded-2xl px-10 py-20 pb-[120px]">
        <CardContent className="flex flex-col items-center">
          <img src={SuccessIcon} className="w-[100px] h-[100px]" />
          <p className="text-sm text-foreground font-medium mt-6">小红书聚光绑定成功</p>

          <div className="flex items-center text-sm text-[#666666] font-normal gap-1 mt-2">
            <div className="w-2.5 h-2.5 flex items-center justify-center">·</div>
            <span>将获取小红书账号信息及对话消息</span>
          </div>

          <Button
            className="w-full !h-[54px] mt-[54px]"
            disabled={!xhsQuery.isSuccess}
            onClick={onImmediatelyAuthorize}
          >
            完成
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
