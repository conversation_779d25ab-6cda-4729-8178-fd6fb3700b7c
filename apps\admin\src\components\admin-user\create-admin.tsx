import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@coozf/ui/components/form'
import { VisuallyHidden } from '@coozf/ui/lib/utils'
import { Input } from '@coozf/ui/components/input'
import { Button } from '@coozf/ui/components/button'
import { LoadingButton } from '@coozf/ui/components/loading'
import { useForm, type UseFormReturn } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useEffect } from 'react'
import { PasswordInput } from '@/components/password-input'
import { RadioGroup, RadioGroupItem } from '@coozf/ui/components/radio-group'
import { createScheam, type CreateInput } from '@coozf/zod'
import { queryClient, trpc } from '@/lib/trpc'

interface CreateAdminProps {
  open: boolean
  setOpen: (open: boolean) => void
  onSuccess: () => void
}

export function CreateAdmin({ open, setOpen, onSuccess }: CreateAdminProps) {
  const form = useForm<CreateInput>({
    resolver: zodResolver(createScheam),
    defaultValues: {
      name: '',
      password: '',
      role: 'user',
    },
    mode: 'onChange',
  })

  const mutation = useMutation(
    trpc.adminUser.create.mutationOptions({
      onSuccess: () => {
        onSuccess()
        setOpen(false)
        queryClient.invalidateQueries({
          queryKey: trpc.adminUser.list.queryKey(),
        })
      },
    }),
  )

  const onSubmit = async (values: CreateInput) => {
    mutation.mutate(values)
  }

  useEffect(() => {
    // 当open变为false时，重置表单
    if (!open) {
      form.reset()
    }
  }, [open, form])

  return (
    <CreateOrEditorAdminTemplate
      title="新增账号"
      open={open}
      setOpen={setOpen}
      isLoading={mutation.isPending}
      onSubmit={onSubmit}
      form={form}
      type="create"
    />
  )
}

interface CreateOrEditorAdminProps {
  title: string
  open: boolean
  setOpen: (open: boolean) => void
  isLoading: boolean
  onSubmit: (values: CreateInput) => void
  form: UseFormReturn<CreateInput>
  type: 'create' | 'edit'
}

function CreateOrEditorAdminTemplate({
  title,
  open,
  setOpen,
  isLoading,
  onSubmit,
  form,
  type,
}: CreateOrEditorAdminProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{title}</DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>
            <div className="flex flex-col my-6 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>姓名</FormLabel>
                    <FormControl>
                      <Input {...field} className="flex-grow" autoComplete="off" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号</FormLabel>
                    <FormControl>
                      <Input {...field} className="flex-grow" autoComplete="off" disabled={type === 'edit'} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <PasswordInput {...field} autoComplete="current-password" />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-1">
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="admin" />
                          </FormControl>
                          <FormLabel className="font-normal">管理员</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="user" />
                          </FormControl>
                          <FormLabel className="font-normal">普通用户</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset()
                  setOpen(false)
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton isPending={isLoading} disabled={isLoading || !form.formState.isValid} type="submit">
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
