import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Label } from '@coozf/ui/components/label'
import { Badge } from '@coozf/ui/components/badge'
import { Settings, Image, Monitor, Smartphone, Copyright } from 'lucide-react'
import type { OEMConfig } from '@coozf/zod'

interface OEMConfigurationProps {
  oemConfig?: OEMConfig
  onConfigureClick: () => void
}

// 图片预览组件
function ImagePreview({ src, alt, fallbackIcon: FallbackIcon }: { 
  src?: string; 
  alt: string;
  fallbackIcon: React.ComponentType<any>;
}) {
  if (!src) {
    return (
      <div className="w-12 h-12 border border-dashed border-muted-foreground/50 rounded-md flex items-center justify-center">
        <FallbackIcon className="w-5 h-5 text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="w-12 h-12 border rounded-md overflow-hidden bg-muted/50">
      <img 
        src={src} 
        alt={alt}
        className="w-full h-full object-contain"
        onError={(e) => {
          const target = e.target as HTMLImageElement
          target.style.display = 'none'
          const parent = target.parentElement
          if (parent) {
            parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-muted-foreground text-xs">加载失败</div>`
          }
        }}
      />
    </div>
  )
}

export function OEMConfiguration({ oemConfig, onConfigureClick }: OEMConfigurationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>品牌定制配置</CardTitle>
        <CardDescription>设置您的品牌信息和客户端外观</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {!oemConfig ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-4">尚未配置品牌信息</div>
              <Button onClick={onConfigureClick}>
                <Settings className="w-4 h-4 mr-2" />
                配置品牌信息
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* 基础信息 */}
              <div>
                <h4 className="text-sm font-medium mb-3 flex items-center">
                  <Settings className="w-4 h-4 mr-2" />
                  基础信息
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-muted-foreground">应用名称</Label>
                    <div className="text-sm">{oemConfig.name || '未设置'}</div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-muted-foreground">品牌名称</Label>
                    <div className="text-sm">{oemConfig.brandName || '未设置'}</div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-muted-foreground">公司名称</Label>
                    <div className="text-sm">{oemConfig.companyName || '未设置'}</div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-muted-foreground">版权信息</Label>
                    <div className="text-sm flex items-center">
                      <Copyright className="w-3 h-3 mr-1" />
                      {oemConfig.copyright || '未设置'}
                    </div>
                  </div>
                  <div className="space-y-1 md:col-span-2">
                    <Label className="text-xs font-medium text-muted-foreground">首页地址</Label>
                    <div className="text-sm text-blue-600 hover:text-blue-800">
                      {oemConfig.homeUrl ? (
                        <a href={oemConfig.homeUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                          {oemConfig.homeUrl}
                        </a>
                      ) : (
                        <span className="text-muted-foreground">未设置</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* 视觉资源 */}
              <div>
                <h4 className="text-sm font-medium mb-3 flex items-center">
                  <Image className="w-4 h-4 mr-2" />
                  视觉资源
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground">应用Logo</Label>
                    <div className="flex items-center gap-3">
                      <ImagePreview 
                        src={oemConfig.logoUrl} 
                        alt="应用Logo" 
                        fallbackIcon={Image}
                      />
                      <Badge variant={oemConfig.logoUrl ? "default" : "secondary"}>
                        {oemConfig.logoUrl ? '已配置' : '未配置'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground flex items-center">
                      <Monitor className="w-3 h-3 mr-1" />
                      Mac托盘图标
                    </Label>
                    <div className="flex items-center gap-3">
                      <ImagePreview 
                        src={oemConfig.trayIconMacUrl} 
                        alt="Mac托盘图标" 
                        fallbackIcon={Monitor}
                      />
                      <Badge variant={oemConfig.trayIconMacUrl ? "default" : "secondary"}>
                        {oemConfig.trayIconMacUrl ? '已配置' : '未配置'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground flex items-center">
                      <Smartphone className="w-3 h-3 mr-1" />
                      Windows托盘图标
                    </Label>
                    <div className="flex items-center gap-3">
                      <ImagePreview 
                        src={oemConfig.trayIconWindowsUrl} 
                        alt="Windows托盘图标" 
                        fallbackIcon={Smartphone}
                      />
                      <Badge variant={oemConfig.trayIconWindowsUrl ? "default" : "secondary"}>
                        {oemConfig.trayIconWindowsUrl ? '已配置' : '未配置'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2 pt-2 border-t">
                <Button variant="outline" onClick={onConfigureClick}>
                  <Settings className="w-4 h-4 mr-2" />
                  修改配置
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
