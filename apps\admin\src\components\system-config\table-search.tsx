import { useState } from 'react'
import { Button } from '@coozf/ui/components/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { Search, X } from 'lucide-react'
import { categoryMap } from '@coozf/zod'

interface SystemConfigSearchInput {
  category?: 'CRAWLER_PROXY' | 'GENERAL'
  isEnabled?: boolean
}

interface TableSearchProps {
  values: SystemConfigSearchInput
  onSearch: (values: SystemConfigSearchInput) => void
}

export function TableSearch({ values, onSearch }: TableSearchProps) {
  const [localValues, setLocalValues] = useState<SystemConfigSearchInput>(values)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(localValues)
  }

  const handleReset = () => {
    const resetValues: SystemConfigSearchInput = {}
    setLocalValues(resetValues)
    onSearch(resetValues)
  }

  const hasFilters = Object.values(localValues).some((value) => value !== undefined && value !== '')

  return (
    <form onSubmit={handleSubmit} className="flex items-center gap-2">
      {/* 分类筛选 */}
      <Select
        value={localValues.category || ''}
        onValueChange={(value) =>
          setLocalValues((prev) => ({
            ...prev,
            category: value === '' ? undefined : (value as 'CRAWLER_PROXY' | 'GENERAL'),
          }))
        }
      >
        <SelectTrigger className="w-40">
          <SelectValue placeholder="选择分类" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部分类</SelectItem>
          {Object.values(categoryMap).map((cat) => (
            <SelectItem key={cat.key} value={cat.key}>
              {cat.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* 启用状态筛选 */}
      <Select
        value={localValues.isEnabled === undefined ? '' : localValues.isEnabled ? 'true' : 'false'}
        onValueChange={(value) =>
          setLocalValues((prev) => ({
            ...prev,
            isEnabled: value === '' ? undefined : value === 'true',
          }))
        }
      >
        <SelectTrigger className="w-32">
          <SelectValue placeholder="状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部状态</SelectItem>
          <SelectItem value="true">启用</SelectItem>
          <SelectItem value="false">禁用</SelectItem>
        </SelectContent>
      </Select>

      <Button type="submit" size="sm" className="shrink-0">
        <Search className="size-4" />
        查询
      </Button>

      {hasFilters && (
        <Button type="button" size="sm" variant="outline" onClick={handleReset}>
          <X className="size-4" />
          重置
        </Button>
      )}
    </form>
  )
}
