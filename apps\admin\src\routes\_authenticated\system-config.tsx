import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '@coozf/ui/components/button'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/system-config/table-search'
import { CreateSystemConfigDialog } from '@/components/system-config/create-system-config-dialog'
import { EditSystemConfigDialog } from '@/components/system-config/edit-system-config-dialog'
import { DeleteConfirmDialog } from '@/components/system-config/delete-confirm-dialog'
import { getTableColumns } from '@/components/system-config/columns'
import { Plus, Settings2 } from 'lucide-react'

type SystemConfigItem = RouterOutput['systemConfig']['list']['items'][number]

type SystemConfigSearchInput = {
  category?: 'CRAWLER_PROXY' | 'GENERAL'
  isEnabled?: boolean
}

type Search = {
  category?: string
  isEnabled?: string
}

export const Route = createFileRoute('/_authenticated/system-config')({
  component: SystemConfigPage,
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      category: search?.category as string,
      isEnabled: search?.isEnabled as string,
    }
  },
})

function SystemConfigPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { category, isEnabled } = useSearch({ strict: false })

  const [filter, setFilter] = useState<SystemConfigSearchInput>({
    category: category as 'CRAWLER_PROXY' | 'GENERAL' | undefined,
    isEnabled: isEnabled === 'true' ? true : isEnabled === 'false' ? false : undefined,
  })

  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editConfig, setEditConfig] = useState<SystemConfigItem | null>(null)
  const [deleteConfigId, setDeleteConfigId] = useState<string | null>(null)

  // 获取配置列表
  const {
    data: configsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.systemConfig.list.queryOptions({
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      ...filter,
    }),
  )

  // 创建配置
  const createConfigMutation = useMutation(
    trpc.systemConfig.create.mutationOptions({
      onSuccess: () => {
        refetch()
        setCreateDialogOpen(false)
        toast.success('配置创建成功')
      },
      onError: (error) => {
        toast.error(`创建失败: ${error.message}`)
      },
    }),
  )

  // 更新配置
  const updateConfigMutation = useMutation(
    trpc.systemConfig.update.mutationOptions({
      onSuccess: () => {
        refetch()
        setEditConfig(null)
        toast.success('配置更新成功')
      },
      onError: (error) => {
        toast.error(`更新失败: ${error.message}`)
      },
    }),
  )

  // 删除配置
  const deleteConfigMutation = useMutation(
    trpc.systemConfig.delete.mutationOptions({
      onSuccess: () => {
        refetch()
        setDeleteConfigId(null)
        toast.success('配置删除成功')
      },
      onError: (error) => {
        toast.error(`删除失败: ${error.message}`)
      },
    }),
  )

  // 初始化爬虫代理配置
  const initCrawlerConfigMutation = useMutation(
    trpc.systemConfig.initCrawlerProxyConfigs.mutationOptions({
      onSuccess: (result) => {
        refetch()
        toast.success(`配置初始化完成`)
        console.log('初始化结果:', result)
      },
      onError: (error) => {
        toast.error(`初始化失败: ${error.message}`)
      },
    }),
  )

  // 切换启用状态
  const toggleEnabledMutation = useMutation(
    trpc.systemConfig.toggleEnabled.mutationOptions({
      onSuccess: () => {
        refetch()
        toast.success('状态更新成功')
      },
      onError: (error) => {
        toast.error(`状态更新失败: ${error.message}`)
      },
    }),
  )

  const configs = configsData?.items || []

  const handleEditConfig = (config: SystemConfigItem) => {
    setEditConfig(config)
  }

  const handleDeleteConfig = (configId: string) => {
    setDeleteConfigId(configId)
  }

  const handleToggleEnabled = (configId: string, isEnabled: boolean) => {
    toggleEnabledMutation.mutate({ id: configId, isEnabled })
  }

  const confirmDeleteConfig = () => {
    if (deleteConfigId) {
      deleteConfigMutation.mutate({ id: deleteConfigId })
    }
  }

  const columns = useMemo(
    () => getTableColumns(handleEditConfig, handleDeleteConfig, handleToggleEnabled),
    [handleToggleEnabled],
  )

  return (
    <div className="flex flex-1 flex-col gap-6 overflow-hidden">
      {/* 页头 */}
      <div className="flex items-center gap-2">
        <Settings2 className="size-5" />
        <h1 className="text-2xl font-bold">系统配置管理</h1>
      </div>

      {/* 搜索和操作栏 */}
      <div className="flex items-center justify-between">
        <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 })
            setFilter(value)
          }}
        />
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => initCrawlerConfigMutation.mutate()}
            disabled={initCrawlerConfigMutation.isPending}
          >
            <Settings2 className="size-4" />
            初始化爬虫配置
          </Button>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="size-4" />
            新增配置
          </Button>
        </div>
      </div>

      {/* 配置表格 */}
      <DataTable
        columns={columns}
        data={configs}
        rowCount={configsData?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />

      {/* 创建配置弹窗 */}
      <CreateSystemConfigDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={(data) => createConfigMutation.mutate(data)}
        isPending={createConfigMutation.isPending}
      />

      {/* 编辑配置弹窗 */}
      <EditSystemConfigDialog
        open={!!editConfig}
        onOpenChange={() => setEditConfig(null)}
        config={editConfig}
        onSubmit={(data) => updateConfigMutation.mutate(data)}
        isPending={updateConfigMutation.isPending}
      />

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={!!deleteConfigId}
        onOpenChange={() => setDeleteConfigId(null)}
        onConfirm={confirmDeleteConfig}
        isPending={deleteConfigMutation.isPending}
      />
    </div>
  )
}