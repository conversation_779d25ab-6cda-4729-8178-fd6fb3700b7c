{"name": "coozf-open-trpc", "private": true, "scripts": {"build:client": "pnpm -F ui build && pnpm -F client build", "build:admin": "pnpm -F ui build && pnpm -F admin build && mkdir -p apps/server/admin-public && cp -r apps/admin/dist/* apps/server/admin-public/", "build:server": "pnpm -F server build", "build:copy-frontend": "pnpm run build:client && mkdir -p apps/server/public && cp -r apps/client/dist/* apps/server/public/", "build": "run-s build:client build:admin build:server", "generate": "pnpm -F db generate", "dev:client": "pnpm -F client dev", "dev:server": "pnpm -F server dev", "dev:admin": "pnpm -F admin dev", "dev": "run-p dev:*", "push": "pnpm -F db push", "studio": "pnpm -F db studio", "start:client": "pnpm -F client start", "start:server": "pnpm -F server start", "start": "run-p start:*", "pre": "pnpm -F pres-e2e pre", "check-types": "turbo run check-types", "clean": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' + && find . -name 'package-lock.json' -type f -delete && find . -name 'yarn.lock ' -type f -delete && find . -name 'dist' -type d -prune -exec rm -rf '{}' +", "prepare": "husky"}, "devDependencies": {"@types/node": "^24.0.10", "eslint": "^9.30.1", "husky": "^9.1.7", "prettier": "^3.6.2", "turbo": "^2.5.4", "typescript": "~5.8.3", "tsup": "^8.5.0"}, "dependencies": {"superjson": "^2.2.2"}, "packageManager": "pnpm@10.12.4"}