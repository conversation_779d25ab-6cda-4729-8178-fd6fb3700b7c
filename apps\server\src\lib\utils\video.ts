import { TRPCError } from '@trpc/server'
import { logger } from '../services/logger'

/**
 * 视频处理工具函数
 */
export class VideoUtils {
  /**
   * 根据URL获取视频大小（通过HTTP HEAD请求）
   * @param url 视频URL
   * @returns 视频大小（GB）
   */
  static async getVideoSizeFromUrl(url: string): Promise<number> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      
      if (!contentLength) {
        throw new Error('无法获取视频大小：响应头中没有Content-Length')
      }

      const sizeInBytes = parseInt(contentLength, 10)
      
      if (isNaN(sizeInBytes) || sizeInBytes <= 0) {
        throw new Error('无效的视频大小')
      }

      // 转换为GB
      const sizeInGB = sizeInBytes / (1024 * 1024 * 1024)
      
      return parseFloat(sizeInGB.toFixed(3)) // 保留3位小数
    } catch (error) {
      logger.error('获取视频大小失败', {
        type: 'video',
        operation: 'get_video_size',
        url,
        error: error instanceof Error ? error.message : error,
      })
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: `获取视频大小失败: ${error instanceof Error ? error.message : '未知错误'}`
      })
    }
  }

  /**
   * 批量获取视频大小
   * @param urls 视频URL数组
   * @returns 视频大小数组（GB）
   */
  static async getBatchVideoSizesFromUrls(urls: string[]): Promise<number[]> {
    // 使用 Promise.all，任何一个失败都会立即抛出错误
    return await Promise.all(
      urls.map(url => this.getVideoSizeFromUrl(url))
    )
  }

  /**
   * 支持重试的视频大小获取
   * @param url 视频URL
   * @param maxRetries 最大重试次数
   * @param retryDelay 重试延迟（毫秒）
   * @returns 视频大小（GB）
   */
  static async getVideoSizeWithRetry(
    url: string, 
    maxRetries: number = 3, 
    retryDelay: number = 1000
  ): Promise<number> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.getVideoSizeFromUrl(url)
      } catch (error) {
        if (attempt === maxRetries) {
          logger.error('获取视频大小失败，达到最大重试次数', {
            type: 'video',
            operation: 'get_video_size_retry',
            url,
            maxRetries,
            error: error instanceof Error ? error.message : error,
          })
          // 最后一次失败，返回默认值
          return 0.1
        }
        
        logger.warn('获取视频大小失败，将进行重试', {
          type: 'video',
          operation: 'get_video_size_retry',
          url,
          attempt,
          maxRetries,
          error: error instanceof Error ? error.message : error,
        })
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
    
    return 0.1 // 兜底值
  }

  /**
   * 验证URL是否为有效的视频URL
   * @param url 待验证的URL
   * @returns 是否为有效的视频URL
   */
  static isValidVideoUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname.toLowerCase()
      
      // 检查常见的视频文件扩展名
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']
      
      return videoExtensions.some(ext => pathname.endsWith(ext)) || 
             pathname.includes('/video/') || 
             urlObj.hostname.includes('video')
    } catch {
      return false
    }
  }

  /**
   * 格式化文件大小显示
   * @param sizeInGB 大小（GB）
   * @returns 格式化后的字符串
   */
  static formatFileSize(sizeInGB: number): string {
    if (sizeInGB < 0.001) {
      return `${(sizeInGB * 1024 * 1024).toFixed(2)} MB`
    } else if (sizeInGB < 1) {
      return `${(sizeInGB * 1024).toFixed(2)} MB`
    } else {
      return `${sizeInGB.toFixed(2)} GB`
    }
  }
}