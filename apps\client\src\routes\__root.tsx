import { Outlet, createRootRouteWithContext } from '@tanstack/react-router'
import type { AuthState } from '../lib/auth/auth-context'

interface MyRouterContext {
  auth: AuthState
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: RootComponent,
  notFoundComponent: () => <div>404 页面未找到</div>,
})

function RootComponent() {
  return (
    <div className="min-h-screen bg-background dark:bg-background">
      <Outlet />
    </div>
  )
} 