export interface PaginationParams {
  page: number
  pageSize: number
}

export interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

interface PaginateOptions<TItem, TTransformed = TItem> {
  getTotal: () => Promise<number>
  getItems: (skip: number, take: number) => Promise<TItem[]>
  transform?: (item: TItem) => Promise<TTransformed> | TTransformed
}

export async function paginate<TItem, TTransformed = TItem>(
  params: PaginationParams,
  options: PaginateOptions<TItem, TTransformed>,
): Promise<PaginatedResult<TTransformed>> {
  const { page, pageSize } = params
  const { getTotal, getItems, transform } = options
  const skip = (page - 1) * pageSize

  const [total, items] = await Promise.all([getTotal(), getItems(skip, pageSize)])

  const transformedItems = transform ? await Promise.all(items.map(transform)) : (items as unknown as TTransformed[])

  return {
    data: transformedItems,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize),
  }
}
