import { z } from 'zod'
import { dateRangeSchema, PaginationSchema } from './common'
import { timeStamp } from 'console'
import { id } from 'zod/v4/locales'

// 落地页列表查询参数
export const PageListQuerySchema = PaginationSchema.extend({
  id: z.string().describe('账号id'),
})

// 物料列表查询参数
export const MaterialListQuerySchema = PaginationSchema.extend({
  id: z.string().describe('账号id'),
  type: z.number().describe('物料类型（4-名片 5-留资卡）'),
})

// 笔记列表查询参数
export const NoteListQuerySchema = PaginationSchema.extend({
  id: z.string().describe('账号id'),
  noteId: z.string().optional().describe('笔记id'),
})

// 意向评论列表查询参数
export const CommentListQuerySchema = PaginationSchema.merge(dateRangeSchema).extend({
  id: z.string().describe('账号id'),
})

export const TradeCardListQuerySchema = PaginationSchema.extend({
  id: z.string().describe('账号id'),
  type: z.number().describe('物料类型（7-交易卡）'),
})

export const BindUsersQuerySchema = PaginationSchema.extend({
  id: z.string().describe('账号id'),
})

export const ThirdSendMessageSchema = z.object({
  id: z.string().describe('账号id'),
  requestId: z.string().describe('请求id'),
  messageType: z.enum(['TEXT', 'IMAGE', 'VIDEO', 'CARD', 'REVOKE']),
  fromUserId: z.string().describe('发送人id'),
  toUserId: z.string().describe('接收人id'),
  thirdAccountId: z.string().describe('第三方账号id'),
  timestamp: z.number().describe('时间戳,毫秒级'),
  content: z.object({
    text: z.string().optional().describe('内容'),
    link: z.string().optional().describe('链接'),
    size: z.object({
      width: z.number().describe('宽度'),
      height: z.number().describe('高度'),
    }).optional(),
    duration: z.number().optional().describe('时长'),
    cover: z.string().optional().describe('封面'),
    videoSize: z.number().optional().describe('宽度'),
    noteId: z.string().optional().describe('笔记id'),
    contentType: z.enum(['note', 'common', 'purchaseComments', 'serviceCard', 'social_card', 'lead_card', 'tradeBusinessCard']).optional(),
    pageId: z.string().optional().describe('落地页id'),
    cardId: z.string().optional().describe('名片id'),
    content: z.string().optional().describe('内容'),
    commentId: z.string().optional().describe('评论id'),
    id: z.string().optional().describe('id'),
    messageId: z.string().optional().describe('消息id'),
  }),
})

export const PageListResponseSchema = z.object({
  page_id: z.string().describe('落地页id'),
  title: z.string().describe('落地页名称'),
  page_desc: z.string().describe('落地页描述'),
  page_url: z.string().describe('落地页链接'),
  cover: z.string().describe('落地页封面'),
  create_time: z.number().describe('落地页创建时间'),
})

export const MaterialListResponseSchema = z.object({
  id: z.string().describe('id'),
  name: z.string().optional().describe('名称'),
  title: z.string().describe('标题'),
  image: z.string().describe('封面'),
  ext: z.object({
    social_card_ext: z.object({
      card_type: z.number().describe('微信：1、钉钉：2、电话：3、企微：4、留资卡：5'),
    })
  }).optional(),
})

export const TradeCardListResponseSchema = z.object({
  id: z.string().describe('id'),
  title: z.string().describe('标题'),
  image: z.string().describe('封面'),
  ext: z.object({
    trade_card_ext: z.object({
      sub_title: z.string().describe('副标题'),
      link_platform: z.string().describe('跳转链接类型 ctrip-携程，meituan-美团，wx_mini-微信小程序(目前默认返回微信小程序)'),
    })
  }).optional(),
})

export const NoteListResponseSchema = z.object({
  note_id: z.string().describe('笔记id'),
  title: z.string().describe('笔记标题'),
  cover: z.string().describe('笔记封面'),
  link: z.string().describe('笔记链接'),
  publish_time: z.number().describe('笔记创建时间'),
  user_info: z.object({
    nickname: z.string().describe('用户名'),
    header_image: z.string().describe('用户头像'),
  }),
})

export const CommentListResponseSchema = z.object({
  note_id: z.string().describe('笔记id'),
  cover: z.string().describe('笔记封面'),
  note_title: z.string().describe('笔记标题'),
  comment_content: z.string().describe('评论内容'),
  comment_time: z.number().describe('评论时间'),
  comment_user_name: z.string().describe('评论用户名'),
  comment_user_id: z.string().describe('评论用户id'),
  comment_id: z.string().describe('评论id'),
  uniq_id : z.string().describe('唯一id'),
  note_author_user_id: z.string().describe('笔记作者id'),
  reply_state: z.number().describe('回复状态 0-未回复 1-已回复'),
  reply_third_account_id: z.string().describe('回复账号id'),
})

export const BindUsersResponseSchema = z.object({
  user_id: z.string().describe('用户id'),
  nick_name: z.string().describe('用户名'),
  avatar_img: z.string().describe('用户头像'),
})

export const ThirdSendMessageResponseSchema = z.object({
  request_id: z.string().describe('请求参数中的唯一id'),
  message_id: z.string().describe('消息id，用于消息撤回')
})
    