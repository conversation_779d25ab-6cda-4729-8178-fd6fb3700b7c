// lib/alert-dialog.ts
import { AlertDialogOptions } from '@/components/alert-dialog-provider'

let globalShow: ((options: AlertDialogOptions) => Promise<boolean>) | null = null
let globalClose: (() => void) | null = null
export const setGlobalAlertDialog = (
  showFn: (options: AlertDialogOptions) => Promise<boolean>,
  closeFn: () => void,
) => {
  globalShow = showFn
  globalClose = closeFn
}

export const alertDialog = {
  show: (options: AlertDialogOptions): Promise<boolean> => {
    if (!globalShow) {
      console.error('AlertDialog not initialized. Make sure AlertDialogProvider is wrapped around your app.')
      return Promise.resolve(false)
    }
    return globalShow(options)
  },

  confirm: (options: Omit<AlertDialogOptions, 'showCancel'>): Promise<boolean> => {
    return alertDialog.show({
      ...options,
      showCancel: true,
    })
  },

  alert: (options: Omit<AlertDialogOptions, 'showCancel'>): Promise<boolean> => {
    return alertDialog.show({
      ...options,
      showCancel: false,
    })
  },

  delete: (options: Omit<AlertDialogOptions, 'confirmButtonVariant'>): Promise<boolean> => {
    return alertDialog.show({
      title: 'Are you sure?',
      description: 'This action cannot be undone.',
      confirmText: 'Delete',
      confirmButtonVariant: 'destructive',
      ...options,
    })
  },

  close: () => {
    if (!globalClose) {
      console.error('AlertDialog not initialized. Make sure AlertDialogProvider is wrapped around your app.')
      return
    }
    globalClose()
  },
}
