import React from 'react'
import { But<PERSON> } from '@coozf/ui/components/button'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@coozf/ui/components/dropdown-menu'
import { MoreHorizontal } from 'lucide-react'

interface DropdownMenuComponentProps {
  children: React.ReactNode
}

const DropdownMenuComponent: React.FC<DropdownMenuComponentProps> = ({ children }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button aria-haspopup="true" size="icon" variant="ghost">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">{children}</DropdownMenuContent>
    </DropdownMenu>
  )
}

export default DropdownMenuComponent
