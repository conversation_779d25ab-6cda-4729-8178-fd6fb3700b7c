import { z } from 'zod'

// 基础文件扩展名验证
const imageExtensions = z.enum(['png', 'jpg', 'jpeg', 'svg', 'ico'])
const documentExtensions = z.enum(['pdf', 'doc', 'docx', 'txt'])
const mediaExtensions = z.enum(['mp4', 'mp3', 'wav', 'avi'])
const archiveExtensions = z.enum(['zip', 'rar', '7z'])

// 文件上传输入 Schema
export const uploadInputSchema = z.discriminatedUnion('type', [
  // OEM 资源上传
  z.object({
    type: z.literal('oem'),
    applicationId: z.string().cuid(),
    category: z.enum(['logo', 'branding', 'assets']).default('logo'),
    subType: z.enum(['main', 'icon', 'splash', 'favicon']).optional(),
    fileName: z.string().min(1).max(100),
    extension: z.union([imageExtensions, documentExtensions, archiveExtensions]),
  }),
  // 用户文件上传
  z.object({
    type: z.literal('user'),
    category: z.enum(['document', 'image', 'media']).default('document'),
    fileName: z.string().min(1).max(100),
    extension: z.union([imageExtensions, documentExtensions, mediaExtensions]),
  }),
  // 临时文件上传
  z.object({
    type: z.literal('temp'),
    fileName: z.string().min(1).max(100),
    extension: imageExtensions, // 临时文件主要是图片
  }),
])

// 简化的上传输入 Schema (自动生成文件名)
export const simpleUploadInputSchema = z.discriminatedUnion('type', [
  // OEM Logo 上传 (最常用场景)
  z.object({
    type: z.literal('oem-logo'),
    applicationId: z.string().cuid(),
    logoType: z.enum(['main', 'icon', 'splash', 'favicon', 'tray']),
    extension: imageExtensions,
  }),
  // 用户头像上传
  z.object({
    type: z.literal('avatar'),
    extension: imageExtensions,
  }),
  // 通用文件上传
  z.object({
    type: z.literal('file'),
    category: z.enum(['document', 'image', 'media']).default('document'),
    extension: z.union([imageExtensions, documentExtensions, mediaExtensions]),
    customPath: z.string().optional(), // 可选的自定义路径
  }),
])

// 文件访问输入 Schema
export const accessInputSchema = z.object({
  filePath: z.string().min(1),
  expires: z.number().min(60).max(3600).default(1800), // 1分钟到1小时
})

// 上传响应 Schema
export const uploadResponseSchema = z.object({
  uploadUrl: z.string().url(),
  filePath: z.string(),
  accessUrl: z.string().url(),
  expiresIn: z.number().optional(),
})

// 访问响应 Schema
export const accessResponseSchema = z.object({
  accessUrl: z.string().url(),
  expiresIn: z.number(),
})

// 类型导出
export type UploadInput = z.infer<typeof uploadInputSchema>
export type SimpleUploadInput = z.infer<typeof simpleUploadInputSchema>
export type AccessInput = z.infer<typeof accessInputSchema>
export type UploadResponse = z.infer<typeof uploadResponseSchema>
export type AccessResponse = z.infer<typeof accessResponseSchema>