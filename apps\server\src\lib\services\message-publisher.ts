import { Queue } from 'bullmq'
import { config } from '@/env'
import { logger } from '@/lib'
import type { VersionType } from '@prisma/client'

/**
 * 更新消息类型
 */
export interface UpdateMessage {
  type: VersionType
  version: string
  forceUpdate: boolean
  description?: string
  downloadUrl: string
  timestamp: number
  publishedBy: string
  applicationId?: string // 新增：应用ID，用于OEM版本区分
}

/**
 * BullMQ 消息发布器
 * 用于在版本创建后通知 API 服务推送更新消息给客户端
 */
export class MessagePublisher {
  private updateQueue: Queue<UpdateMessage>

  constructor() {
    // 创建版本更新队列
    this.updateQueue = new Queue<UpdateMessage>(config.UPDATE_CHANNEL, {
      connection: {
        host: config.REDIS_HOST,
        port: config.REDIS_PORT,
        password: config.REDIS_PASSWORD,
        db: config.REDIS_DB,
      },
      defaultJobOptions: {
        // 任务默认配置
        removeOnComplete: {
          age: 3600, // 完成后保留1小时
          count: 100, // 最多保留100个完成的任务
        },
        removeOnFail: {
          age: 24 * 3600, // 失败后保留24小时
        },
        attempts: 3, // 失败重试3次
        backoff: {
          type: 'exponential',
          delay: 2000, // 重试延迟
        },
      },
    })

    // 监听队列事件
    this.setupQueueEvents()
  }

  /**
   * 设置队列事件监听
   */
  private setupQueueEvents(): void {
    this.updateQueue.on('error', (error) => {
      logger.error('版本更新队列错误', {
        type: 'message_publisher',
        event: 'queue_error',
        error: error.message,
      })
    })
  }

  /**
   * 发布版本更新消息
   */
  async publishVersionUpdate(message: UpdateMessage): Promise<void> {
    try {
      // 添加任务到队列
      const job = await this.updateQueue.add(
        config.UPDATE_CHANNEL, // 任务名称
        message,
        {
          // 可以根据不同类型设置优先级
          priority: message.forceUpdate ? 1 : 10,
          // 添加任务 ID 防止重复
          jobId: `${message.type}-${message.version}-${Date.now()}`,
        },
      )

      logger.info('版本更新任务已添加到队列', {
        type: 'message_publisher',
        event: 'version_update_queued',
        jobId: job.id,
        versionType: message.type,
        version: message.version,
        forceUpdate: message.forceUpdate,
        publishedBy: message.publishedBy,
      })
    } catch (error) {
      logger.error('添加版本更新任务失败', {
        type: 'message_publisher',
        event: 'queue_add_failed',
        message,
        error: error instanceof Error ? error.message : error,
      })
      throw error
    }
  }

  /**
   * 根据版本类型发布特定的更新消息
   */
  async publishUpdateByType(type: VersionType, message: Omit<UpdateMessage, 'type'>): Promise<void> {
    await this.publishVersionUpdate({ ...message, type })
  }

  /**
   * 发布延迟更新（可选功能）
   */
  async publishDelayedUpdate(message: UpdateMessage, delayMs: number): Promise<void> {
    try {
      const job = await this.updateQueue.add('version-update', message, {
        delay: delayMs,
        priority: message.forceUpdate ? 1 : 10,
        jobId: `delayed-${message.type}-${message.version}-${Date.now()}`,
      })

      logger.info('延迟版本更新任务已添加到队列', {
        type: 'message_publisher',
        event: 'delayed_update_queued',
        jobId: job.id,
        delayMs,
        versionType: message.type,
        version: message.version,
      })
    } catch (error) {
      logger.error('添加延迟版本更新任务失败', {
        type: 'message_publisher',
        event: 'delayed_queue_add_failed',
        message,
        delayMs,
        error: error instanceof Error ? error.message : error,
      })
      throw error
    }
  }

  /**
   * 清理资源
   */
  async close(): Promise<void> {
    await this.updateQueue.close()
  }
}

// 导出单例实例
export const messagePublisher = new MessagePublisher()
