import { z } from 'zod'
import { dateRangeSchema, PaginationSchema } from './common'
import { timeStamp } from 'console'
import { id } from 'zod/v4/locales'

// 账号角色
export const AuthorizeRoleSchema = z.object({
  id: z.string().describe('账号id'),
  roleLabels: z.array(z.string()).describe('角色标签')
})

export const AuthorizeRoleResponseSchema = z.object({
  match_result: z.boolean().describe('是否匹配成功'),
  filter_role: z.any().describe('过滤角色')
})

export const SaveRetainConsultCardSchema = z.object({
  id: z.string().describe('账号id'),
  cardId: z.string().optional().describe('留资卡片ID，在对卡片进行修改时，需填写创建卡片时所返回的卡片ID'),
  components: z.array(z.number()).describe('需要添加的输入框，至少传入一个(1、姓名 2、手机 3、城市)'),
  mediaId: z.string().describe('图片的 ID。通过图片上传接口获取，图片尺寸建议：宽263高120，尺寸不一样会自动适配短边占满'),
  title: z.string().describe('卡片标题'),
})

export const SaveRetainConsultCardResponseSchema = z.object({
  cardId: z.string().describe('留资卡片ID')
})

export const GetRetainConsultCardSchema = z.array(z.object({
  card_id: z.string().optional().describe('留资卡片ID，在对卡片进行修改时，需填写创建卡片时所返回的卡片ID'),
  title: z.string().optional().describe('卡片标题'),
  components: z.array(z.number()).optional().describe('需要添加的输入框，至少传入一个(1、姓名 2、手机 3、城市)'),
  media_id: z.string().optional().describe('图片的 ID。通过图片上传接口获取，图片尺寸建议：宽263高120，尺寸不一样会自动适配短边占满'),
  status: z.number().optional().describe('卡片状态 0、通过 1、审核中 2、未通过')
}))

export const UploadImageResponseSchema = z.object({
  media_id: z.string().describe('图片的 ID'),
  width: z.number().describe('图片的宽度'),
  height: z.number().describe('图片的高度'),
  md5: z.string().describe('图片的 md5 值')
})

export const DouyinThirdSendMessageSchema = z.object({
  id: z.string().describe('账号id'),
  content: z.object({
    msgType: z.number().describe('消息类型：1、文本 2、图片 8、留资卡片'),
    content: z.string().describe('1、文本消息内容 2、图片的 image_id。通过图片上传接口获取 8、留资卡片ID'),
  }),
  msgId: z.string().describe('消息 id'),
  conversationId: z.string().describe('会话 id'),
  toUserId: z.string().describe('接收人 id'),
  scene: z.string().describe('发送场景'),
  channel: z.number().describe('发送渠道')
})

export const RecallMessageSchema = z.object({
  id: z.string().describe('账号id'),
  msgId: z.string().describe('消息 id'),
  conversationId: z.string().describe('会话 id'),
  conversationType: z.number().describe('会话类型'),
})