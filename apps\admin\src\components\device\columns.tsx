import type { RouterOutput } from '@/lib/trpc'
import type { ColumnDef } from '@tanstack/react-table'
import { formatDate } from '@coozf/ui/lib/day'
type UserItem = RouterOutput['device']['list']['data'][number]
export function getTableColumns(): ColumnDef<UserItem>[] {
  return [
    {
      accessorKey: 'deviceId',
      header: '设备id',
      cell: ({ row }) => {
        return <div>{row.original.deviceId}</div>
      },
    },
    {
      accessorKey: 'socketId',
      header: '设备socketId',
      cell: ({ row }) => row.original.socketId,
    },
    {
      accessorKey: 'connectedAt',
      header: '连接时间',
      cell: ({ row }) => formatDate(row.original.connectedAt),
    },
    {
      accessorKey: 'type',
      header: '设备类型',
      cell: ({ row }) => {
        if ('type' in row.original) {
          return <div>{row.original.type}</div>
        }
        return <div>-</div>
      },
    },
    {
      accessorKey: 'version',
      header: '版本',
      cell: ({ row }) => {
        if ('version' in row.original) {
          return (
            <div className="flex flex-col gap-1">
              <span>{row.original.name}</span>
              <span className="text-muted-foreground text-xs">v{row.original.version}</span>
            </div>
          )
        }
        return <div>-</div>
      },
    },
    {
      accessorKey: 'appId',
      header: '应用Id',
      cell: ({ row }) => {
        if ('appId' in row.original) {
          return <div>{row.original.appId}</div>
        }
        return <div>-</div>
      },
    },
  ]
}
