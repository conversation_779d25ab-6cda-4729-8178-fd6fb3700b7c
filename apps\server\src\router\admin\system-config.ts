import { z } from 'zod'
import { router } from '@/trpc'
import { SystemConfigService } from '@/lib/business/system-config'
import { adminProtectedProcedure } from '@/procedure'
import { SystemConfigCategory } from '@coozf/db'

// 导入接口类型
interface CreateSystemConfigInput {
  key: string
  category: SystemConfigCategory
  name: string
  description?: string
  value: any
  isEnabled?: boolean
}

// 系统配置相关的Zod验证schema
const CreateSystemConfigSchema = z.object({
  key: z.string().min(1).max(100),
  category: z.nativeEnum(SystemConfigCategory),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  value: z.any(), // JSON值，可以是数组或对象
  isEnabled: z.boolean().default(true),
})

const UpdateSystemConfigSchema = z.object({
  id: z.string().cuid(),
  key: z.string().min(1).max(100).optional(),
  category: z.nativeEnum(SystemConfigCategory).optional(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  value: z.any().optional(), // JSON值，可以是数组或对象
  isEnabled: z.boolean().optional(),
})

const SystemConfigListSchema = z.object({
  category: z.nativeEnum(SystemConfigCategory).optional(),
  isEnabled: z.boolean().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
})

export const systemConfigRouter = router({
  // 获取系统配置列表
  list: adminProtectedProcedure.input(SystemConfigListSchema).query(async ({ input }) => {
    return await SystemConfigService.getConfigList(input)
  }),

  // 根据key获取单个配置
  getByKey: adminProtectedProcedure.input(z.object({ key: z.string() })).query(async ({ input }) => {
    return await SystemConfigService.getConfigByKey(input.key)
  }),

  // 创建系统配置
  create: adminProtectedProcedure.input(CreateSystemConfigSchema).mutation(async ({ input }) => {
    return await SystemConfigService.createConfig(input as CreateSystemConfigInput)
  }),

  // 更新系统配置
  update: adminProtectedProcedure.input(UpdateSystemConfigSchema).mutation(async ({ input }) => {
    const { id, ...updateData } = input
    return await SystemConfigService.updateConfig(id, updateData)
  }),

  // 删除系统配置
  delete: adminProtectedProcedure.input(z.object({ id: z.string().cuid() })).mutation(async ({ input }) => {
    return await SystemConfigService.deleteConfig(input.id)
  }),

  // 切换配置启用状态
  toggleEnabled: adminProtectedProcedure
    .input(z.object({ id: z.string().cuid(), isEnabled: z.boolean() }))
    .mutation(async ({ input }) => {
      return await SystemConfigService.updateConfig(input.id, { isEnabled: input.isEnabled })
    }),

  // 批量初始化爬虫代理配置
  initCrawlerProxyConfigs: adminProtectedProcedure.mutation(async () => {
    return await SystemConfigService.initCrawlerProxyConfigs()
  }),
})