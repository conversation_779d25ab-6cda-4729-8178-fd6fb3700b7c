import z from "zod"

export const category = z.enum(['CRAWLER_PROXY', 'GENERAL', 'PLATFORM_CONFIG', 'CREDITS_CONFIG'])
export const categoryMap = {
          CRAWLER_PROXY: { key: 'CRAWLER_PROXY', label: '爬虫代理', variant: 'default' as const },
          GENERAL: {key: 'GENERAL', label: '通用配置', variant: 'secondary' as const },
          PLATFORM_CONFIG: {key: 'PLATFORM_CONFIG', label: '平台配置', variant: 'destructive' as const },
          CREDITS_CONFIG: {key: 'CREDITS_CONFIG', label: '默认点数配置', variant: 'secondary' as const },
        }
export type Category = z.infer<typeof category>