import type { FastifyError, FastifyInstance, FastifyReply, FastifyRequest } from 'fastify'
import { ZodError } from 'zod'
import { TRPCError } from '@trpc/server'
import { getHTTPStatusCodeFromError } from '@trpc/server/http'
import { ResponseWrapper, ApiError } from '../lib'

export const setupErrorHandler = async (app: FastifyInstance) => {
  app.setErrorHandler((error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    const err = formatError(error)

    // 使用统一日志服务记录错误
    app.loggerService.error(err.message, {
      error: {
        code: err.code,
      },
      request: {
        method: request.method,
        url: request.url,
        headers: request.headers,
        query: request.query,
        params: request.params,
        body: request.body,
      },
    })

    reply.status(Math.floor(err.code / 100)).send(err)
  })
}

const formatZodError = (error: ZodError) => {
  const flattened = error.flatten()
  const { fieldErrors, formErrors } = flattened

  // 如果有表单级别错误，优先显示
  if (formErrors.length > 0 && formErrors[0]) {
    return ResponseWrapper.error(40000, formErrors[0])
  }

  // 处理字段级错误
  const fieldErrorEntries = Object.entries(fieldErrors)
  if (fieldErrorEntries.length > 0) {
    const errorMessages = fieldErrorEntries
      .map(([field, errors]) => {
        const errorList = Array.isArray(errors) ? errors : [errors].filter(Boolean)
        return errorList.length > 0 ? `${field}: ${errorList[0]}` : null
      })
      .filter(Boolean)

    if (errorMessages.length === 1) {
      return ResponseWrapper.error(40000, errorMessages[0]!)
    } else if (errorMessages.length > 1) {
      return ResponseWrapper.error(40000, `${errorMessages[0]!}等${errorMessages.length}个字段错误`)
    }
  }

  // 兜底情况
  return ResponseWrapper.error(40000, '数据校验错误')
}

export const formatError = (error: FastifyError) => {
  if (error instanceof ZodError) {
    return formatZodError(error)
  } else if (error instanceof ApiError) {
    return ResponseWrapper.error(error.code, error.message)
  } else if (error instanceof TRPCError) {
    const httpStatus = getHTTPStatusCodeFromError(error)
    const zodError = error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause : null
    if (zodError) {
      return formatZodError(zodError)
    }
    return ResponseWrapper.error(httpStatus * 100, error.message)
  } else {
    return ResponseWrapper.error(50000, error.message || '服务器错误')
  }
}
