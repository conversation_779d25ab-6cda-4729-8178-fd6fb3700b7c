import { tosService } from '../services/tos'

/**
 * 对象存储业务逻辑层
 */
export class StorageService {
  /**
   * 上传文件
   */
  async uploadFile(buffer: Buffer, name: string, bucketName?: string) {
    return tosService.uploadFile(buffer, name, bucketName)
  }

  /**
   * 获取文件访问地址
   */
  async getAccessUrl(objectName: string, expires = 1800, queries?: Record<string, any>, bucketName?: string) {
    return tosService.getAccessSignatureUrl(objectName, expires, queries, bucketName)
  }

  /**
   * 获取文件上传地址
   */
  async getUploadUrl(objectName: string, checksum?: string, bucketName?: string) {
    return tosService.getUploadSignatureUrl(objectName, checksum, bucketName)
  }

  /**
   * 获取文件元数据地址
   */
  async getHeadUrl(key: string, bucketName?: string) {
    return tosService.getHeadSignatureUrl(key, bucketName)
  }

  /**
   * 流式上传文件
   */
  async uploadByStream(url: string, referer: string, teamCode: string) {
    return tosService.putObjectByStream(url, referer, teamCode)
  }

  /**
   * 获取文件列表
   */
  async getFileList(prefix: string, bucketName?: string) {
    return tosService.getFileList(prefix, bucketName)
  }

  /**
   * 删除单个文件
   */
  async deleteFile(key: string) {
    return tosService.deleteOssObject(key)
  }

  /**
   * 批量删除文件
   */
  async deleteFiles(keys: string[]) {
    return tosService.deleteMultiOssObject(keys)
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(key: string) {
    return tosService.headFileInfo(key)
  }

  /**
   * 获取文件内容
   */
  async getFileContent(key: string, bucketName?: string) {
    return tosService.getFileContent(key, bucketName)
  }

  /**
   * 获取桌面端下载地址
   */
  async getDesktopDownloadUrl(bucketName: string, key: string, expires = 10) {
    return tosService.getDesktopDownloadUrl(bucketName, key, expires)
  }

  /**
   * 创建软连接
   */
  async createSoftLink(target: string, link: string, bucketName: string) {
    return tosService.createSoftLink(target, link, bucketName)
  }
}

export const storageService = new StorageService()
