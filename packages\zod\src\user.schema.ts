import { z } from 'zod'
import { PaginationSchema } from './common'

// 用户相关的 Zod Schema
export const CreateUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
  avatar: z.string().url('请输入有效的头像地址').optional(),
})

export const UpdateUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional(),
  password: z.string().min(6, '密码至少6位').optional(),
  name: z.string().min(1, '姓名不能为空').optional(),
  avatar: z.string().url('请输入有效的头像地址').optional(),
  phoneVerified: z.boolean().optional(),
  emailVerified: z.boolean().optional(),
})

export const LoginSchema = z
  .object({
    username: z.string().min(1, '用户名不能为空'),
    password: z.string().min(6, '密码至少6位'),
  })

export const SelectUserSchema = z.object({
  id: z.string(),
  email: z.string().nullable(),
  phone: z.string().nullable(),
  name: z.string().nullable(),
  phoneVerified: z.boolean(),
  emailVerified: z.boolean(),
  avatar: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// 用户列表查询参数
export const UserListSchema = z.object({
  phone: z.string().optional(), // 手机号搜索
  startDate: z.number().optional(), // 注册开始时间
  endDate: z.number().optional(), // 注册结束时间
  search: z.string().optional(), // 通用搜索（姓名、邮箱）
})

// 类型推导
export type CreateUserInput = z.infer<typeof CreateUserSchema>
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>
export type LoginInput = z.infer<typeof LoginSchema>
export type SelectUserOutput = z.infer<typeof SelectUserSchema>
export type UserListInput = z.infer<typeof UserListSchema>
