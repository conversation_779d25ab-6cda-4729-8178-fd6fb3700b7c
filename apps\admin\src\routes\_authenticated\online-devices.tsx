import { DataTable } from '@/components/data-table'
import { getTableColumns } from '@/components/device/columns'
import { trpc } from '@/lib/trpc'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { useMemo, useState } from 'react'

export const Route = createFileRoute('/_authenticated/online-devices')({
  component: RouteComponent,
})

function RouteComponent() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  })

  const { data, isLoading } = useQuery(
    trpc.device.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    }),
  )

  const columns = useMemo(() => getTableColumns(), [])

  return (
    <div className="flex flex-1 flex-col gap-2 overflow-hidden">
      <div className="flex items-center justify-between">
        {/* <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 })
            setFilter(value)
          }}
        /> */}
      </div>
      <DataTable
        columns={columns}
        data={data?.data}
        rowCount={data?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />
    </div>
  )
}
